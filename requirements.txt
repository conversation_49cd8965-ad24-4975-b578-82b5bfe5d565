alembic==1.14.1
apifairy==1.4.0
Bootstrap-Flask==2.4.1
boto3==1.36.18
Flask==3.1.0
Flask-Admin==1.6.1
Flask-Caching==2.3.0
flask-csrf==0.9.2
Flask-Login==0.6.3
Flask-Mail==0.10.0
flask-marshmallow==1.3.0
Flask-Migrate==4.1.0
Flask-Principal==0.4.0
flask-redis==0.4.0
Flask-Security-Too==5.6.0
Flask-Session==0.8.0
Flask-SQLAlchemy==3.1.1
Flask-Table==0.5.0
flask-talisman==1.1.0
Flask-WTF==1.2.2
gunicorn[gevent]==23.0.0
honcho>=2.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
marshmallow-sqlalchemy==1.4.1
pendulum==3.0.0
psycopg2-binary==2.9.10
PyJWT==2.10.1
pyotp==2.9.0
pytest==8.3.5
pytest-mock==3.14.0
pytest-flask==1.3.0
python-dateutil==2.9.0
python-decouple==3.8
pytz==2025.1
redis==5.2.1
regex==2024.11.6
requests==2.32.3
sentry-sdk==2.20.0
simplejson==3.19.3
SQLAlchemy==2.0.38
sqlalchemy-datatables==2.0.1
sqlalchemy-filters==0.13.0
SQLAlchemy-Utils==0.41.2
textmagic==2.0.3
texttable==1.7.0
Werkzeug==3.1.3
# Keeping it to this version becuase it breaks Flask-admin
# See: https://github.com/pallets-eco/flask-admin/issues/2582
# https://github.com/pallets-eco/flask-admin/issues/2391
# So until flask-admin gets a v2.0 we need to keep WTForms pinned to 3.1.2
WTForms==3.1.2
flask-jwt-extended==4.7.1
pdfplumber==0.10.3
pymupdf>=1.22.0