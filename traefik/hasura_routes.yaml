http:
  routers:
    hasura-api-router:
      rule: "PathPrefix(`/v1/`) || PathPrefix(`/v2/`)"
      entryPoints:
        - web
      service: "hasura-service"
      priority: 10

    hasura-console-router:
      rule: "PathPrefix(`/console`)"
      entryPoints:
        - web
      service: "hasura-service"
      priority: 10

  services:
    hasura-service:
      loadBalancer:
        servers:
          - url: "http://localhost:3020" 