{% raw %}
<div class="topbar-item" data-toggle="dropdown" data-offset="10px,0px" aria-expanded="true">

    <a href="#" class="btn btn-icon btn-light-primary pulse pulse-primary mr-5">
        <i class="flaticon2-bell-5"></i>
     <!--   <span class="pulse-ring"></span> -->
    </a>
    <span class="position-absolute top-0 right-0 translate-middle rounded-circle  badge btn-success" id="notification_count"></span>
</div>
<div class="dropdown-menu p-0 m-0 dropdown-menu-right dropdown-menu-anim-up dropdown-menu-lg"
     style="position: absolute; transform: translate3d(-295px, 72px, 0px); top: 0px; left: 0px; will-change: transform;"
     x-placement="bottom-end">
    <form>
        <!--begin::Header-->
        <div class="d-flex flex-column pt-12 bgi-size-cover bgi-no-repeat rounded-top">
            <!--begin::Title-->
            <!--end::Title-->
            <!--begin::Tabs-->
            <ul class="nav nav-bold nav-tabs nav-tabs-line nav-tabs-line-3x nav-tabs-line-active-border-success mt-3 px-8 font-size-lg"
                role="tablist">
                <li class="nav-item">
                    <a class="nav-link show active" data-toggle="tab" href="#topbar_notifications_notifications">Reminders</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#topbar_notifications_events">Events</a>
                </li>
            </ul>
            <!--end::Tabs-->
        </div>
        <!--end::Header-->
        <!--begin::Content-->
        <div class="tab-content">
            <!--begin::Tabpane reminder-->
            <div class="tab-pane show p-8 active" id="topbar_notifications_notifications" role="tabpanel">
                <!--begin::Scroll-->
                <div class="scroll pr-7 mr-n7 ps" data-scroll="true" data-height="300" data-mobile-height="200"
                     style="overflow: hidden;">

                    <!--begin::Item-->
                    <div id="reminder_notification">
                        <div class="d-flex align-items-center mb-6" v-for="item in records">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-35 flex-shrink-0 mr-3">
                                <span class="{{ item.icon }}"></span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Content-->
                            <div class="d-flex flex-wrap flex-row-fluid">
                                <!--begin::Text-->
                                <div class="d-flex flex-column pr-5 flex-grow-1" style="width: 100%">
                                    <a href="#"
                                       class="text-dark text-hover-primary mb-1 font-weight-bold font-size-lg">{{
                                        item.object_str }}</a>
                                    <span class="text-muted font-weight-bold">{{ item.message }}</span>
                                </div>
                                <!--end::Text-->
                                <!--begin::Action-->
                                <div class="d-flex align-items-center py-2 ">
                                    <a href="#" class="symbol symbol-35 symbol-light-info flex-shrink-0 mr-3"
                                         data-toggle="tooltip" v-on:click="markNotificationAsRead(item.id)"
                                         title="Mark as read">
                                        <span class="symbol-label font-weight-bolder font-size-lg text-success">✓</span>
                                    </a>

                                    <div class="symbol symbol-35 symbol-light-info flex-shrink-0 mr-3"
                                         data-toggle="tooltip"
                                         title="Action">
                                        <a :href="item.action_slug" class="btn btn-icon btn-light btn-sm">
                                            <span class="symbol-label font-weight-bolder font-size-lg">➤</span>
                                        </a>
                                    </div>

                                </div>
                                <!--end::Action-->
                            </div>
                            <!--end::Content-->
                        </div>
                        <pagination :records="recordsLength" v-model="page" :per-page="perPage" @paginate="getPage">
                        </pagination>
                    </div>
                    <!--end::Item-->

                </div>
            </div>
            <!--end::Tabpane reminder-->

            <!--begin::Tabpane event-->                                                <!--begin::Tabpane-->
            <div class="tab-pane p-8" id="topbar_notifications_events" role="tabpanel">
                <!--begin::Scroll-->
                <div class="scroll pr-7 mr-n7 ps" data-scroll="true" data-height="300" data-mobile-height="200"
                     style="overflow: hidden;">

                    <div id="event_notification">
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-6" v-for="event in records">
                            <!--begin::Symbol-->
                            <div v-if="event.notification_code !=null"
                                 class="symbol symbol-35 symbol-light-info flex-shrink-0 mr-3">
                                <span class="symbol-label font-weight-bolder font-size-lg">✓</span>
                            </div>
                            <div v-else class="symbol symbol-35 symbol-light-info flex-shrink-0 mr-3">
                                <span class="symbol-label font-weight-bolder font-size-lg">✖</span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Text-->
                            <div class="d-flex flex-column font-weight-bold">

                                <span v-if="event.notification_code !=null"
                                      class="text-success text-hover-success mb-1 font-size-lg">{{ event.study_id}}</span>

                                <a v-else v-bind:href="'/admin/studies/edit/?id='+event.study_id"
                                   class="text-primary text-hover-primary mb-1 font-size-lg">{{ event.study_id}}</a>


                                <span class="text-muted">{{ event.message}}</span>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Item-->


                        <!--end::Item-->
                        <div class="ps__rail-x" style="left: 0px; bottom: 0px;">
                            <div class="ps__thumb-x" tabindex="0" style="left: 0px; width: 0px;"></div>
                        </div>
                        <div class="ps__rail-y" style="top: 0px; right: 0px;">
                            <div class="ps__thumb-y" tabindex="0" style="top: 0px; height: 0px;"></div>
                        </div>


                        <pagination :records="recordsLength" v-model="page" :per-page="perPage" @paginate="getPage">
                        </pagination>

                    </div>
                    <!--end::Scroll-->

                </div>
                <!--end::Tabpane event-->


            </div>


        </div>
        <!--end::Content-->
    </form>
</div>

{% endraw %}