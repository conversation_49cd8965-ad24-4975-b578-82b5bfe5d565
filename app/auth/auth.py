import sentry_sdk
from flask import Blueprint, request, render_template, flash, redirect, url_for, session, jsonify
from flask_jwt_extended import (
    create_refresh_token,
    jwt_required,
    get_jwt_identity,
)
from flask_login import current_user
from flask_security import LoginForm, login_user, RegisterForm, logout_user
from flask_security.utils import verify_password
from sqlalchemy import func
from urllib.parse import urlparse
from wtforms import fields
from wtforms.validators import InputRequired

from app import db, csrf
from .admin import UserAdmin, RoleAdminView, PermissionAdminView
from .models import User, Role
from .token import create_user_access_token
from ..utils.models import Site

auth_bp = Blueprint(
    "auth_bp", __name__, template_folder="templates", static_folder="static"
)


@auth_bp.route('/login/', methods=('GET', 'POST'))
@auth_bp.route('/login/<message>', methods=('GET', 'POST'))
def login_view(message=None):
    """
    Login view for a normal user
    :return:
    """
    session.clear()
    session.permanent = True
    login_route = url_for('auth_bp.login_view')
    if current_user.is_authenticated:
        return redirect("/admin/")
    form = LoginForm()
    if request.method == 'GET':
        return render_template(
            'security/login_user.html', title='Sign In', form=form, message=message,
            client="Rezibase"
        )
    if request.method == 'POST' and form.validate_on_submit():
        user = User.query.filter(
            func.lower(User.email) == form.email.data.lower(),
            User.roles.any(Role.name == 'user')
        ).first()
        if user is None:
            flash(u'Invalid email or User not in role user', 'auth')
            return redirect(login_route)
        if user is None or not user.verify_and_update_password(form.password.data):
            flash(u'Invalid password', 'auth')
            return redirect(login_route)
        if not user.sites:
            flash(u'User has no site set. Must assign site before login allowed.', 'auth')
            return render_template(
                'security/login_user.html', title='Sign In', form=form, message=message,
                client="Rezibase"
            )
        login_user(user, remember=form.remember.data, authn_via=["password"])
        user.fail_login_count = 0
        # after_this_request(view_commit)
        next_page = form.next.data
        if not next_page or urlparse(next_page).netloc != '':
            # if 'superuser' in user.roles:
            #     next_page = url_for('admin.index')
            # else:
            next_page = "/admin/"
        return redirect(next_page)
    else:
        try:
            user = User.query.filter(
                func.lower(User.email) == form.email.data.lower(),
                User.roles.any(Role.name == 'user')
            ).first()
            if user is not None:
                user.fail_login_count = (user.fail_login_count or 0) + 1
                # after_this_request(view_commit)
            if len(form.errors) > 0:
                sentry_sdk.capture_message(
                    ' '.join([' '.join(x for x in errors) for errors in list(form.errors.values())])
                )
            flash(u'Invalid email, password or role user', 'auth')
        except:
            flash(u'Invalid email, password or role user', 'auth')
        return render_template(
            'security/login_user.html', title='Sign In', form=form, message=message,
            client="Rezibase"
        )
    return render_template('security/login_user.html', title='Sign In', form=form, client=Config.SITE_NAME)


# @auth_bp.route('/api/register', methods=['POST'])
# def register():
#     data = request.get_json()
#
#     if not data:
#         return jsonify({'message': 'No input data provided'}), 400
#
#     form = UserRegistrationForm(data=data)
#     if not form.validate():
#         return jsonify({'message': 'Invalid data', 'errors': form.errors}), 400
#
#     if User.query.filter_by(email=data.get('email')).first():
#         return jsonify({'message': 'Email already registered'}), 400
#
#     user_data = form.to_dict(True)
#     user_data['password'] = hash_password(user_data['password'])  # Hash the password before saving
#     user_data['active'] = False
#     user_data['fs_uniquifier'] = str(uuid.uuid4())
#
#     user = User(**user_data)
#     db.session.add(user)
#     db.session.commit()
#
#     # Assign user role and deactivate user initially
#     db.session.commit()
#
#     return jsonify({'message': 'User registered successfully. Please check your email to activate your account.'}), 201


@auth_bp.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()

    if not data or not data.get('email') or not data.get('password'):
        return jsonify({'message': 'Missing email or password'}), 400

    user = User.query.filter_by(email=data['email']).first()

    if not user.active:
        return jsonify({'message': 'Account Inactive'}), 401

    if user and verify_password(data['password'], user.password):
        # Always set mfaRequired to True if user has TOTP enabled
        access_token = create_user_access_token(user, mfa_required=user.totp_enabled)
        refresh_token = create_refresh_token(identity=str(user.id))
        return jsonify({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user_id': user.id,
            'email': user.email,
            'mfaRequired': user.totp_enabled
        })

    return jsonify({'message': 'Invalid email or password'}), 401


@auth_bp.route('/api/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    access_token = create_user_access_token(user)

    return jsonify(
        {
            'access_token': access_token
        }
    )


@auth_bp.route('/api/me', methods=['GET'])
@jwt_required()
def get_profile():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'message': 'User not found'}), 404

    return jsonify(
        {
            'id': user.id,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'password_reset_required': (user.meta or {}).get('password_reset_required', False),
        }
    )


class UserRegistrationForm(RegisterForm):
    """
    Registration form for emeritus employees
    """
    first_name = fields.StringField(validators=[InputRequired()])
    last_name = fields.StringField(validators=[InputRequired()])
    organisation = fields.StringField()


# @auth_bp.route('/register/', methods=('GET', 'POST'))
# def register_view():
#     """
#     Register view for a normal user
#     :return:
#     """
#     # from app.tasks import send_confirmation_email
#     form = UserRegistrationForm(request.form)
#     if request.method == 'POST' and form.validate():
#         data = form.to_dict(True)
#         data['active'] = False
#         data['roles'] = ['user', ]
#         user = register_user(form)
#         security.datastore.add_role_to_user(user, 'user')
#         security.datastore.deactivate_user(user)
#         db.session.commit()
#         # put false to get all non user model fields
#         data = form.to_dict(False)
#         # if not current_app.testing:
#         #     send_confirmation_email(user.uid, url_parse(request.base_url).netloc)
#         return redirect(url_for('auth_bp.welcome'))
#     return render_template('register.html', register_user_form=form)


@auth_bp.route('/logout/')
def logout_view():
    """
    Logout a user
    :return:
    """
    logout_user()
    session.clear()
    return redirect(url_for('auth_bp.login_view'))


@auth_bp.route('/api/users/', methods=['GET'])
def get_users():
    """
    Get all users
    :return:
    """
    users = User.query.all()
    users_dicts = [user.to_dict() for user in users]
    return jsonify(users_dicts)


# @require_login


@auth_bp.route('/api/users/current', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get current user
    :return:
    """
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    if user:
        return jsonify(user.to_dict())
    else:
        return jsonify({"message": "No user logged in"}), 404


# require login

@csrf.exempt
@auth_bp.route('/api/users/selected_lab', methods=['GET', 'POST'])
def get_selected_lab():
    """
    Get selected lab for a user
    :return:
    """

    if request.method == 'GET':
        return jsonify({'selected_lab': current_user.selected_lab_id})
    else:
        current_user.selected_lab_id = request.json['selected_lab_id']
        db.session.commit()
        return jsonify({'selected_lab': current_user.selected_lab_id})


@auth_bp.route('/approval_wait/')
def approval_wait():
    """
    Waiting for admin approval message after a user has submitted registration form
    :return:
    """
    return render_template('admin_approval.html')


@auth_bp.route('/welcome/')
def welcome():
    """
    Welcome message after a user has submitted registration form
    :return:
    """
    return render_template('welcome.html')


@auth_bp.route('/api/users/site/switch', methods=['POST'])
@jwt_required()
def api_site_switch():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    data = request.get_json()
    site_id = data.get('site_id') if data else None
    
    if not site_id or not str(site_id).isnumeric():
        return jsonify({'error': 'Invalid or missing site_id'}), 400
        
    site_id = int(site_id)
    site = Site.query.get(site_id)
    
    if not site:
        return jsonify({'error': 'Site not found'}), 404
        
    if user.has_role('superuser') or site in user.sites:
        session['site_id'] = site.id
        session['site_text'] = site.name

        access_token = create_user_access_token(user, site_id=site.id)
        return jsonify({
            'message': 'Site switched',
            'site_id': site.id,
            'site_text': site.name,
            'access_token': access_token
        })
    else:
        return jsonify({'error': 'Not authorized for this site'}), 403


def add_admins(admin):
    admin.add_view(RoleAdminView(db.session, category='Users'))
    admin.add_view(UserAdmin(User, db.session, category='Users'))
    admin.add_view(PermissionAdminView(db.session, category='Users'))


@auth_bp.route('/api/change_password', methods=['POST'])
@jwt_required()
def change_password():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    data = request.get_json()
    new_password = data.get('new_password')
    current_password = data.get('current_password')

    if not new_password:
        return jsonify({'message': 'New password is required'}), 400

    password_reset_required = (user.meta or {}).get('password_reset_required', False)

    if not password_reset_required:
        if not current_password:
            return jsonify({'current_password': 'Current password is required'}), 400
        if not verify_password(current_password, user.password):
            return jsonify({'current_password': 'Current password is incorrect'}), 400

    # Set new password and reset flag
    from flask_security import hash_password
    from sqlalchemy.orm.attributes import flag_modified
    from app import db

    user.password = hash_password(new_password)
    user.meta = user.meta or {}
    user.meta['password_reset_required'] = False
    flag_modified(user, 'meta')

    db.session.add(user)
    db.session.commit()
    return jsonify({'message': 'Password changed successfully'})
