from apifairy import response, arguments
from flask import request, session, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from flask_login import current_user
from flask_marshmallow import Schema
from marshmallow import EXCLUDE, fields

from app import ma, db
from app.api_clinibase.api_base import AutoSqlAlchemyClass, ApiFairySchema, ApiFairyModelSchema
from common import user_required_role
from .auth import auth_bp
from .models import User, UserSites
from ..utils.models import Site


class SiteSchema(Schema):
    site_id = fields.Integer()

    class Meta:
        model = Site


user_schema_class = AutoSqlAlchemyClass(User, [], extra_mixin=SiteSchema)


class SiteSchema(ma.Schema):
    class Meta:
        unknown = EXCLUDE

    value = ma.Integer(default=None)
    text = ma.Str(required=True)


@auth_bp.route('/api/staff/list')
@arguments(ApiFairySchema(unknown=EXCLUDE))
@arguments(user_schema_class.filter_klass(unknown=EXCLUDE))
@user_required_role()
@response(user_schema_class.pagination_klass)
def api_staff_list(pagination, query_filter):
    columns = [User.id, User.full_name, UserSites.site_id]
    base_query = db.session.query(*columns).select_from(User).join(UserSites).filter(
        UserSites.site_id == session['site_id']
    )
    return ApiFairyModelSchema.process_query(
        base_query, User, pagination, query_filter,
        user_schema_class
    )


@auth_bp.route('/api/users/site/list')
@user_required_role()
@response(SiteSchema(many=True))
def api_site_list():
    if current_user.has_role('superuser'):
        return [dict(value=site.id, text=site.name) for site in Site.query.all()]
    else:
        return [dict(value=site.id, text=site.name) for site in current_user.sites if
                site.id != 1 or current_user.is_dev_user]


@auth_bp.route('/api/users/site/set_site')
@user_required_role()
def api_site_set():
    if request.args.get('site_id') and request.args.get('site_id').isnumeric():
        if current_user.has_role('superuser'):
            site = Site.query.get(int(request.args.get('site_id')))
            session['site_id'] = site.id
            session['site_text'] = site.name
        else:
            site = Site.query.get(int(request.args.get('site_id')))
            if site in current_user.sites:
                session['site_id'] = site.id
                session['site_text'] = site.name
    return jsonify("OK")
