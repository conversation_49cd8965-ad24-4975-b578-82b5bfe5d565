import pyotp
from flask import jsonify, request
from flask_login import login_required
from flask_jwt_extended import jwt_required, get_jwt_identity

from app import db
from app.auth.auth import auth_bp
from app.auth.token import create_user_access_token

from .models import User


@auth_bp.route('/api/user/mfa/enable/totp/', methods=['POST'])
@jwt_required()
def enable_totp():
    """Request TOTP setup by generating secret and URL"""
    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)

    if current_user.totp_enabled:
        return jsonify({"error": "TOTP is already enabled"}), 400
        
    # Generate random secret
    secret = pyotp.random_base32()
    current_user.totp_secret = secret
    db.session.commit()
    
    # Generate provisioning URL for QR code
    totp = pyotp.TOTP(secret)
    provisioning_url = totp.provisioning_uri(
        name=current_user.email,
        issuer_name="Rezibase"
    )
    
    return jsonify({
        "url": provisioning_url,
        "secret": secret,
        "algorithm": "SHA1",
        "digits": 6,
        "period": 30
    })

@auth_bp.route('/api/user/mfa/verify/<token_type>/', methods=['POST'])
@jwt_required()
def verify_mfa_token(token_type):
    """Verify TOTP or backup code"""
    token = request.json.get('token')
    if not token:
        return jsonify({"token": "Token is required"}), 400

    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)
        
    if token_type == "totp":
        if not current_user.totp_secret:
            return jsonify({"token": "TOTP is not set up"}), 400
            
        totp = pyotp.TOTP(current_user.totp_secret)
        if totp.verify(token):
            # Generate new token with mfaRequired=False
            access_token = create_user_access_token(current_user, mfa_required=False)
            return jsonify({
                "message": "Token verified successfully",
                "access_token": access_token
            })
        return jsonify({"token": "Invalid token"}), 400
        
    elif token_type == "backupCode":
        if not current_user.backup_codes:
            return jsonify({"token": "No backup codes available"}), 400
            
        if token in current_user.backup_codes:
            # Remove used backup code and generate new token with mfaRequired=False
            current_user.backup_codes.remove(token)
            db.session.commit()
            access_token = create_user_access_token(current_user, mfa_required=False)
            return jsonify({
                "message": "Backup code verified successfully",
                "access_token": access_token
            })
        return jsonify({"token": "Invalid backup code"}), 400
        
    return jsonify({"token": "Invalid token type"}), 400

@auth_bp.route('/api/user/mfa/confirm/totp/', methods=['POST'])
@jwt_required()
def confirm_totp():
    """Confirm and enable TOTP setup"""
    token = request.json.get('token')
    if not token:
        return jsonify({"error": "Token is required"}), 400

    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)
        
    if not current_user.totp_secret:
        return jsonify({"error": "TOTP is not set up"}), 400
        
    totp = pyotp.TOTP(current_user.totp_secret)
    if totp.verify(token):
        # Generate backup codes
        backup_codes = [pyotp.random_base32()[:8] for _ in range(10)]
        
        current_user.totp_enabled = True
        current_user.backup_codes = backup_codes
        db.session.commit()
        
        return jsonify({
            "message": "TOTP enabled successfully",
            "backup_codes": backup_codes
        })
        
    return jsonify({"error": "Invalid token"}), 400

@auth_bp.route('/api/user/mfa/static/', methods=['GET'])
@jwt_required()
def get_backup_codes():
    """Get backup codes for current user"""

    current_user_id = get_jwt_identity()
    current_user = User.query.get(current_user_id)

    if not current_user.totp_enabled:
        return jsonify({"error": "TOTP is not enabled"}), 400
        
    return jsonify({"backup_codes": current_user.backup_codes})