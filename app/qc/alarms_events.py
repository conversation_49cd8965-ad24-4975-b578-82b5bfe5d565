from dataclasses import dataclass
from typing import List, Optional
import logging

from app import db
from app.qc.models import (
    SessionData, ControlRuleInterval, ControlRule,
    Alarm, AlarmControlRule, ControlMethodEquipmentParameterControlRule, Session
)
from sqlalchemy import event, inspect

logger = logging.getLogger(__name__)

# Constants for rule names
RULE_1_2S = "1_2s"
RULE_1_3S = "1_3s"
RULE_2_2S = "2_2s"
RULE_4_1S = "4_1s"
RULE_8X = "8x"
RULE_UPPER = "upper"
RULE_LOWER = "lower"


@dataclass
class EvaluationError:
    control_rule_id: int
    error_type: str
    control_method_equipment_parameter_id: int


@dataclass
class EvaluationResult:
    errors: List[EvaluationError]
    standard_residual: Optional[float]
    mean: Optional[float]
    std: Optional[float]
    control_rule_interval: Optional[ControlRuleInterval]


def evaluate_session_data(session_data: SessionData) -> Optional[EvaluationResult]:
    """Evaluate a session's parameter value against control rules."""

    # Find the most recent control interval before this session
    control_interval = ControlRuleInterval.get_for_session_data(session_data.id)

    if session_data.parameter_value is None:
        return None

    # Get control rules for this parameter
    control_rules = ControlRule.query.join(
        ControlMethodEquipmentParameterControlRule
    ).filter(
        ControlMethodEquipmentParameterControlRule.control_method_equipment_parameter_id ==
        session_data.control_method_equipment_parameter_id
    ).all()

    errors = []

    param_value = session_data.parameter_value
    mean = control_interval.mean_value if control_interval else None
    std = control_interval.std_value if control_interval else None

    logger.debug("param_value: %s", param_value)

    if control_interval and (not mean or not std):
        control_interval.calculate_statistics()
        mean = control_interval.mean_value
        std = control_interval.std_value

    logger.debug("mean: %s", mean)
    logger.debug("std: %s", std)

    # session_data.sr_value = (param_value - mean) / std
    # Evaluate each control rule
    for rule in control_rules:
        # 1_2s rule
        if mean and std and rule.name == RULE_1_2S and abs(param_value - mean) > 2 * std:
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_1_2S,
                    control_method_equipment_parameter_id=session_data.control_method_equipment_parameter_id
                )
            )

        # 1_3s rule
        if mean and std and rule.name == RULE_1_3S and abs(param_value - mean) > 3 * std:
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_1_3S,
                    control_method_equipment_parameter_id=session_data.control_method_equipment_parameter_id
                )
            )

        # Get rule value for upper/lower bounds
        rule_value = db.session.query(
            ControlMethodEquipmentParameterControlRule.value
        ).filter_by(
            control_rule_id=rule.id,
            control_method_equipment_parameter_id=session_data.control_method_equipment_parameter_id
        ).one()

        logger.debug("rule_value: %s %s %s", rule_value.value, param_value, rule.name)

        # Upper control rule
        if rule.name == RULE_UPPER and rule_value.value and param_value > rule_value.value:
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_UPPER,
                    control_method_equipment_parameter_id=session_data.control_method_equipment_parameter_id
                )
            )

        # Lower control rule
        if rule.name == RULE_LOWER and rule_value.value and param_value < rule_value.value:
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_LOWER,
                    control_method_equipment_parameter_id=session_data.control_method_equipment_parameter_id
                )
            )

        if not mean or not std:
            continue

        # Get previous values only after the control interval's boundaries
        control_interval_upper_bound = control_interval.get_upper_bound_session()

        previous_values = [
            sd.parameter_value for sd in SessionData.query.join(Session).filter(
                Session.session_time < session_data.session.session_time,
                Session.session_time > control_interval_upper_bound.session_time,
                SessionData.control_method_equipment_parameter_id == session_data.control_method_equipment_parameter_id
            ).order_by(Session.session_time.desc()).limit(7).all()
        ]
        previous_values.insert(0, param_value)

        # 2_2s rule
        if (rule.name == RULE_2_2S and len(previous_values) >= 2 and
                (all(v > mean + 2 * std for v in previous_values[:2]) or
                 all(v < mean - 2 * std for v in previous_values[:2]))):
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_2_2S,
                    control_method_equipment_parameter_id=control_interval.control_method_equipment_parameter_id
                )
            )

        # 4_1s rule
        if (rule.name == RULE_4_1S and len(previous_values) >= 4 and
                (all(v < mean - std for v in previous_values[:4]) or
                 all(v > mean + std for v in previous_values[:4]))):
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_4_1S,
                    control_method_equipment_parameter_id=control_interval.control_method_equipment_parameter_id
                )
            )

        # 8x rule
        if (rule.name == RULE_8X and len(previous_values) >= 8 and
                (all(v > mean for v in previous_values[:8]) or all(v < mean for v in previous_values[:8]))):
            errors.append(
                EvaluationError(
                    control_rule_id=rule.id,
                    error_type=RULE_8X,
                    control_method_equipment_parameter_id=control_interval.control_method_equipment_parameter_id
                )
            )

    # Calculate standard residual
    standard_residual = None
    if mean is not None and std is not None and std != 0:
        standard_residual = (param_value - mean) / (std + 0.0000001)

    return EvaluationResult(
        errors=errors,
        control_rule_interval=control_interval,
        standard_residual=standard_residual,
        mean=mean,
        std=std
    )


def create_or_update_alarms(session_data: SessionData, evaluation_result: EvaluationResult):
    """
    Reconcile alarms based on evaluation results.
    Only add new alarms and remove no longer applicable ones.
    """
    # Get existing alarms and their control rules
    existing_alarms = Alarm.query.filter_by(session_data_id=session_data.id).all()
    existing_rule_map = {}
    for alarm in existing_alarms:
        rule_ids = {acr.control_rule_id for acr in alarm.alarm_control_rules}
        existing_rule_map[alarm.id] = rule_ids

    # Create sets of current and new rule violations
    current_violations = {(alarm.id, rule_id)
                          for alarm_id, rule_ids in existing_rule_map.items()
                          for rule_id in rule_ids}

    new_violations = {(None, error.control_rule_id) for error in evaluation_result.errors}

    # Determine which alarms to add and remove
    violations_to_remove = current_violations - new_violations
    violations_to_add = new_violations - current_violations

    # Remove alarms that are no longer applicable
    alarm_ids_to_remove = {alarm_id for alarm_id, _ in violations_to_remove if alarm_id is not None}
    for alarm_id in alarm_ids_to_remove:
        alarm = next((a for a in existing_alarms if a.id == alarm_id), None)
        if alarm:
            db.session.delete(alarm)

    # Create new alarms for new violations
    if violations_to_add:
        # Group new violations by alarm (since multiple rule violations can be in one alarm)
        new_alarm = Alarm(
            session_data_id=session_data.id,
            open=True,
        )
        db.session.add(new_alarm)

        # Create alarm control rules for new violations
        db.session.add_all(
            [
                AlarmControlRule(control_rule_id=rule_id, alarm=new_alarm)
                for _, rule_id in violations_to_add
            ]
        )


@event.listens_for(Session, 'after_update')
def evaluate_session_on_change(mapper, connection, target: Session):
    insp = inspect(target)
    if not insp.attrs.session_time.history.has_changes():
        logger.debug("Session time not changed %s", target.id)
        return

    for session_data in target.session_datas:
        logger.debug("Updating session data %s", session_data.id)
        evaluate_session_data_on_change(mapper, connection, session_data, skip_inspection=True)


# Event listeners
@event.listens_for(SessionData, 'after_insert')
@event.listens_for(SessionData, 'after_update')
def evaluate_session_data_on_change(mapper, connection, target, skip_inspection=False):
    """
    Evaluate session data when it's created or updated,
    but only if it's not within any control interval.
    """
    insp = inspect(target)
    if not insp.attrs.parameter_value.history.has_changes() and not skip_inspection:
        logger.debug("session value not changed %s", target)
        return

    # Check if session falls within any control interval
    control_interval = ControlRuleInterval.query.filter(
        ControlRuleInterval.control_method_equipment_parameter_id == target.control_method_equipment_parameter_id,
        ControlRuleInterval.session_ids.overlap([target.session_id])
    ).first()

    if control_interval:
        logger.debug("updating control interval: %s", control_interval)

        @event.listens_for(db.session, "after_flush_postexec", once=True)
        def receive_after_flush(*args, **kwargs):
            if control_interval.mean_value and control_interval.std_value and target.parameter_value:
                target.sr_value = (target.parameter_value - control_interval.mean_value) / control_interval.std_value

            for alarm in target.alarms:
                db.session.delete(alarm)

            db.session.add(target)
            db.session.add(control_interval)

        return

    evaluation_result = evaluate_session_data(target)
    if not evaluation_result:
        return

    if evaluation_result.control_rule_interval:
        db.session.expunge(evaluation_result.control_rule_interval)

    @event.listens_for(db.session, "after_flush_postexec", once=True)
    def receive_after_flush(*args, **kwargs):
        control_interval = evaluation_result.control_rule_interval

        if control_interval:
            logger.debug("evaluate_session_data after flush. control_interval_id %s", control_interval.id)
            if control_interval and control_interval.mean_value and control_interval.std_value and target.parameter_value:
                target.sr_value = (target.parameter_value - control_interval.mean_value) / control_interval.std_value

            db.session.add(evaluation_result.control_rule_interval)

        db.session.add(target)
        create_or_update_alarms(target, evaluation_result)
        logger.debug("create_or_update_alarms")


@event.listens_for(ControlRuleInterval, 'after_update')
@event.listens_for(ControlRuleInterval, 'after_insert')
def evaluate_affected_sessions_on_interval_change(mapper, connection, target: ControlRuleInterval):
    """Re-evaluate sessions that fall between this control interval and the next one."""
    insp = inspect(target)
    if not insp.attrs.session_ids.history.has_changes():
        return

    lower_bound_session = target.get_upper_bound_session()

    # Find the next control interval for this parameter
    next_interval = target.get_next_interval()

    # Set upper bound as the minimum session ID of next interval if it exists
    upper_bound_session = None
    if next_interval:
        upper_bound_session = next_interval.get_lower_bound_session()

    # Build query for affected sessions
    query = SessionData.query.join(Session).filter(
        SessionData.control_method_equipment_parameter_id == target.control_method_equipment_parameter_id,
        Session.session_time >= lower_bound_session.session_time
    )

    # Add upper bound if it exists
    if upper_bound_session:
        query = query.filter(Session.session_time < upper_bound_session.session_time)

    affected_sessions = query.all()

    logger.debug("affected sessions: %s", affected_sessions)

    if not affected_sessions:
        return

    evaluation_results = []
    target.calculate_statistics()
    for session_data in affected_sessions:
        evaluation_result = evaluate_session_data(session_data)
        if evaluation_result:
            db.session.expunge(evaluation_result.control_rule_interval)
            evaluation_results.append((session_data, evaluation_result))

    @event.listens_for(db.session, "after_flush_postexec", once=True)
    def receive_after_flush(session, context):
        for session_data in target.get_session_data():
            logger.debug("update internal session: %s", session_data.session_id)
            if not target.mean_value or not target.std_value:
                continue

            session_data.sr_value = ((session_data.parameter_value - target.mean_value) / target.std_value)
            db.session.add(session_data)

        for session_data in affected_sessions:
            logger.debug("control interval: %s", target)

            if not target or not session_data.parameter_value:
                continue

            if not target.mean_value or not target.std_value:
                continue

            logger.debug("updating sr value: %s %s %s", session_data, target, session_data.sr_value)
            session_data.sr_value = ((session_data.parameter_value - target.mean_value) / target.std_value)
            db.session.add(session_data)

        for session_data, evaluation_result in evaluation_results:
            logger.debug("updating control interval: %s", evaluation_result.control_rule_interval)
            existing_obj = session.query(ControlRuleInterval).get(evaluation_result.control_rule_interval.id)
            if existing_obj is None:
                session.add(evaluation_result.control_rule_interval)
            create_or_update_alarms(session_data, evaluation_result)
