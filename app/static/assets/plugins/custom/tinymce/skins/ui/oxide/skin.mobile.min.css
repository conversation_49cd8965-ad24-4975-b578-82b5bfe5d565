/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 */
.tinymce-mobile-outer-container {
  all: initial;
  display: block; }

.tinymce-mobile-outer-container * {
  border: 0;
  -webkit-box-sizing: initial;
  box-sizing: initial;
  cursor: inherit;
  float: none;
  line-height: 1;
  margin: 0;
  outline: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
  text-shadow: none;
  white-space: nowrap; }

.tinymce-mobile-icon-arrow-back::before {
  content: "\e5cd"; }

.tinymce-mobile-icon-image::before {
  content: "\e412"; }

.tinymce-mobile-icon-cancel-circle::before {
  content: "\e5c9"; }

.tinymce-mobile-icon-full-dot::before {
  content: "\e061"; }

.tinymce-mobile-icon-align-center::before {
  content: "\e234"; }

.tinymce-mobile-icon-align-left::before {
  content: "\e236"; }

.tinymce-mobile-icon-align-right::before {
  content: "\e237"; }

.tinymce-mobile-icon-bold::before {
  content: "\e238"; }

.tinymce-mobile-icon-italic::before {
  content: "\e23f"; }

.tinymce-mobile-icon-unordered-list::before {
  content: "\e241"; }

.tinymce-mobile-icon-ordered-list::before {
  content: "\e242"; }

.tinymce-mobile-icon-font-size::before {
  content: "\e245"; }

.tinymce-mobile-icon-underline::before {
  content: "\e249"; }

.tinymce-mobile-icon-link::before {
  content: "\e157"; }

.tinymce-mobile-icon-unlink::before {
  content: "\eca2"; }

.tinymce-mobile-icon-color::before {
  content: "\e891"; }

.tinymce-mobile-icon-previous::before {
  content: "\e314"; }

.tinymce-mobile-icon-next::before {
  content: "\e315"; }

.tinymce-mobile-icon-large-font::before, .tinymce-mobile-icon-style-formats::before {
  content: "\e264"; }

.tinymce-mobile-icon-undo::before {
  content: "\e166"; }

.tinymce-mobile-icon-redo::before {
  content: "\e15a"; }

.tinymce-mobile-icon-removeformat::before {
  content: "\e239"; }

.tinymce-mobile-icon-small-font::before {
  content: "\e906"; }

.tinymce-mobile-format-matches::after, .tinymce-mobile-icon-readonly-back::before {
  content: "\e5ca"; }

.tinymce-mobile-icon-small-heading::before {
  content: "small"; }

.tinymce-mobile-icon-large-heading::before {
  content: "large"; }

.tinymce-mobile-icon-large-heading::before, .tinymce-mobile-icon-small-heading::before {
  font-family: sans-serif;
  font-size: 80%; }

.tinymce-mobile-mask-edit-icon::before {
  content: "\e254"; }

.tinymce-mobile-icon-back::before {
  content: "\e5c4"; }

.tinymce-mobile-icon-heading::before {
  content: "Headings";
  font-family: sans-serif;
  font-size: 80%;
  font-weight: 700; }

.tinymce-mobile-icon-h1::before {
  content: "H1";
  font-weight: 700; }

.tinymce-mobile-icon-h2::before {
  content: "H2";
  font-weight: 700; }

.tinymce-mobile-icon-h3::before {
  content: "H3";
  font-weight: 700; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background: rgba(51, 51, 51, 0.5);
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  font-family: sans-serif;
  font-size: 1em;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .mixin-menu-item {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50%;
  height: 2.1em;
  width: 2.1em; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  font-size: 1em; }

@media only screen and (min-device-width: 700px) {
  .tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section {
    font-size: 1.2em; } }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section .tinymce-mobile-mask-tap-icon {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50%;
  height: 2.1em;
  width: 2.1em;
  background-color: #fff;
  color: #207ab7; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section .tinymce-mobile-mask-tap-icon::before {
  content: "\e900";
  font-family: tinymce-mobile,sans-serif; }

.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section:not(.tinymce-mobile-mask-tap-icon-selected) .tinymce-mobile-mask-tap-icon {
  z-index: 2; }

.tinymce-mobile-android-container.tinymce-mobile-android-maximized {
  background: #fff;
  border: none;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  left: 0;
  position: fixed;
  right: 0;
  top: 0; }

.tinymce-mobile-android-container:not(.tinymce-mobile-android-maximized) {
  position: relative; }

.tinymce-mobile-android-container .tinymce-mobile-editor-socket {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1; }

.tinymce-mobile-android-container .tinymce-mobile-editor-socket iframe {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: auto !important; }

.tinymce-mobile-android-scroll-reload {
  overflow: hidden; }

:not(.tinymce-mobile-readonly-mode) > .tinymce-mobile-android-selection-context-toolbar {
  margin-top: 23px; }

.tinymce-mobile-toolstrip {
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  z-index: 1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 2.5em;
  width: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -ms-flex-negative: 1;
  flex-shrink: 1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group > div {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group.tinymce-mobile-exit-container {
  background: #f44336; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group.tinymce-mobile-toolbar-scrollable-group {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item {
  padding-left: .5em;
  padding-right: .5em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item.tinymce-mobile-toolbar-button {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 80%;
  margin-left: 2px;
  margin-right: 2px; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item.tinymce-mobile-toolbar-button.tinymce-mobile-toolbar-button-selected {
  background: #c8cbcf;
  color: #ccc; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:first-of-type, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:last-of-type {
  background: #207ab7;
  color: #eceff1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding-bottom: .4em;
  padding-top: .4em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 1.5em;
  overflow: hidden;
  padding-left: 0;
  padding-right: 0;
  position: relative;
  width: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-transition: left cubic-bezier(0.4, 0, 1, 1) 0.15s;
  transition: left cubic-bezier(0.4, 0, 1, 1) 0.15s;
  width: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen input {
  font-family: Sans-serif; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container .tinymce-mobile-input-container-x {
  -ms-grid-row-align: center;
  -ms-flex-item-align: center;
  align-self: center;
  background: inherit;
  border: none;
  border-radius: 50%;
  color: #888;
  font-size: .6em;
  font-weight: 700;
  height: 100%;
  padding-right: 2px;
  position: absolute;
  right: 0; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container.tinymce-mobile-input-container-empty .tinymce-mobile-input-container-x {
  display: none; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next::before, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous::before {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-weight: 700;
  height: 100%;
  padding-left: .5em;
  padding-right: .5em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next.tinymce-mobile-toolbar-navigation-disabled::before, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous.tinymce-mobile-toolbar-navigation-disabled::before {
  visibility: hidden; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-item {
  color: #ccc;
  font-size: 10px;
  line-height: 10px;
  margin: 0 2px;
  padding-top: 3px; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-item.tinymce-mobile-dot-active {
  color: #c8cbcf; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-large-font::before, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-large-heading::before {
  margin-left: .5em;
  margin-right: .9em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-small-font::before, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-small-heading::before {
  margin-left: .9em;
  margin-right: .5em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-left: 0;
  margin-right: 0;
  padding: .28em 0;
  position: relative; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-size-container {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-size-container .tinymce-mobile-slider-size-line {
  background: #ccc;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: .2em;
  margin-bottom: .3em;
  margin-top: .3em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container {
  padding-left: 2em;
  padding-right: 2em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-slider-gradient-container {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-slider-gradient-container .tinymce-mobile-slider-gradient {
  background: -webkit-gradient(linear, left top, right top, color-stop(0, red), color-stop(17%, #feff00), color-stop(33%, #0f0), color-stop(50%, #00feff), color-stop(67%, #00f), color-stop(83%, #ff00fe), to(red));
  background: linear-gradient(to right, red 0, #feff00 17%, #0f0 33%, #00feff 50%, #00f 67%, #ff00fe 83%, red 100%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: .2em;
  margin-bottom: .3em;
  margin-top: .3em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-hue-slider-black {
  background: #000;
  height: .2em;
  margin-bottom: .3em;
  margin-top: .3em;
  width: 1.2em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-hue-slider-white {
  background: #fff;
  height: .2em;
  margin-bottom: .3em;
  margin-top: .3em;
  width: 1.2em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-thumb {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-clip: padding-box;
  background-color: #455a64;
  border: 0.5em solid rgba(136, 136, 136, 0);
  border-radius: 3em;
  bottom: 0;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: .5em;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  left: -10px;
  margin: auto;
  position: absolute;
  top: 0;
  -webkit-transition: border 120ms cubic-bezier(0.39, 0.58, 0.57, 1);
  transition: border 120ms cubic-bezier(0.39, 0.58, 0.57, 1);
  width: .5em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-thumb.tinymce-mobile-thumb-active {
  border: 0.5em solid rgba(136, 136, 136, 0.39); }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serializer-wrapper, .tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group > div {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serializer-wrapper {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item:not(.tinymce-mobile-serialised-dialog) {
  height: 100%; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input {
  background: #fff;
  border: none;
  border-radius: 0;
  color: #455a64;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: .85em;
  padding-bottom: .1em;
  padding-left: 5px;
  padding-top: .1em; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::-webkit-input-placeholder {
  color: #888; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::-moz-placeholder {
  color: #888; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input:-ms-input-placeholder {
  color: #888; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::-ms-input-placeholder {
  color: #888; }

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::placeholder {
  color: #888; }

.tinymce-mobile-dropup {
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%; }

.tinymce-mobile-dropup.tinymce-mobile-dropup-shrinking {
  -webkit-transition: height .3s ease-out;
  transition: height .3s ease-out; }

.tinymce-mobile-dropup.tinymce-mobile-dropup-growing {
  -webkit-transition: height .3s ease-in;
  transition: height .3s ease-in; }

.tinymce-mobile-dropup.tinymce-mobile-dropup-closed {
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0; }

.tinymce-mobile-dropup.tinymce-mobile-dropup-open:not(.tinymce-mobile-dropup-growing) {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1; }

.tinymce-mobile-ios-container .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {
  min-height: 200px; }

@media only screen and (orientation: landscape) {
  .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {
    min-height: 200px; } }

@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation: landscape) {
  .tinymce-mobile-ios-container .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {
    min-height: 150px; } }

.tinymce-mobile-styles-menu {
  font-family: sans-serif;
  outline: 4px solid #000;
  overflow: hidden;
  position: relative;
  width: 100%; }

.tinymce-mobile-styles-menu [role=menu] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  position: absolute;
  width: 100%; }

.tinymce-mobile-styles-menu [role=menu].transitioning {
  -webkit-transition: -webkit-transform .5s ease-in-out;
  transition: -webkit-transform .5s ease-in-out;
  transition: transform .5s ease-in-out;
  transition: transform .5s ease-in-out, -webkit-transform .5s ease-in-out; }

.tinymce-mobile-styles-menu .tinymce-mobile-styles-item {
  border-bottom: 1px solid #ddd;
  color: #455a64;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 1em 1em;
  position: relative; }

.tinymce-mobile-styles-menu .tinymce-mobile-styles-collapser .tinymce-mobile-styles-collapse-icon::before {
  color: #455a64;
  content: "\e314";
  font-family: tinymce-mobile,sans-serif; }

.tinymce-mobile-styles-menu .tinymce-mobile-styles-item.tinymce-mobile-styles-item-is-menu::after {
  color: #455a64;
  content: "\e315";
  font-family: tinymce-mobile,sans-serif;
  padding-left: 1em;
  padding-right: 1em;
  position: absolute;
  right: 0; }

.tinymce-mobile-styles-menu .tinymce-mobile-styles-item.tinymce-mobile-format-matches::after {
  font-family: tinymce-mobile,sans-serif;
  padding-left: 1em;
  padding-right: 1em;
  position: absolute;
  right: 0; }

.tinymce-mobile-styles-menu .tinymce-mobile-styles-collapser, .tinymce-mobile-styles-menu .tinymce-mobile-styles-separator {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #fff;
  border-top: #455a64;
  color: #455a64;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 2.5em;
  padding-left: 1em;
  padding-right: 1em; }

.tinymce-mobile-styles-menu [data-transitioning-destination=before][data-transitioning-state], .tinymce-mobile-styles-menu [data-transitioning-state=before] {
  -webkit-transform: translate(-100%);
  transform: translate(-100%); }

.tinymce-mobile-styles-menu [data-transitioning-destination=current][data-transitioning-state], .tinymce-mobile-styles-menu [data-transitioning-state=current] {
  -webkit-transform: translate(0);
  transform: translate(0); }

.tinymce-mobile-styles-menu [data-transitioning-destination=after][data-transitioning-state], .tinymce-mobile-styles-menu [data-transitioning-state=after] {
  -webkit-transform: translate(100%);
  transform: translate(100%); }

@font-face {
  font-family: tinymce-mobile;
  font-style: normal;
  font-weight: 400;
  src: url(fonts/tinymce-mobile.woff?8x92w3) format("woff"); }

@media (min-device-width: 700px) {
  .tinymce-mobile-outer-container, .tinymce-mobile-outer-container input {
    font-size: 25px; } }

@media (max-device-width: 700px) {
  .tinymce-mobile-outer-container, .tinymce-mobile-outer-container input {
    font-size: 18px; } }

.tinymce-mobile-icon {
  font-family: tinymce-mobile,sans-serif; }

.mixin-flex-and-centre {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center; }

.mixin-flex-bar {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%; }

.tinymce-mobile-outer-container .tinymce-mobile-editor-socket iframe {
  background-color: #fff;
  width: 100%; }

.tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {
  background-color: #207ab7;
  border-radius: 50%;
  bottom: 1em;
  color: #fff;
  font-size: 1em;
  height: 2.1em;
  position: fixed;
  right: 2em;
  width: 2.1em;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center; }

@media only screen and (min-device-width: 700px) {
  .tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {
    font-size: 1.2em; } }

.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-editor-socket {
  height: 300px;
  overflow: hidden; }

.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-editor-socket iframe {
  height: 100%; }

.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-toolstrip {
  display: none; }

input[type=file]::-webkit-file-upload-button {
  display: none; }

@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation: landscape) {
  .tinymce-mobile-ios-container .tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {
    bottom: 50%; } }
