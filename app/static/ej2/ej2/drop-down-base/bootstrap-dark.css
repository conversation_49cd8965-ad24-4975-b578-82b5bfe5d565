.e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 10px 0 0;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon,
.e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 12px 0 0;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 8px 0 0;
}

.e-bigger .e-dropdownbase,
.e-dropdownbase.e-bigger {
  min-height: 45px;
}

.e-bigger .e-dropdownbase .e-list-item,
.e-bigger .e-dropdownbase .e-list-group-item,
.e-bigger .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-bigger .e-list-item,
.e-dropdownbase.e-bigger .e-list-group-item,
.e-dropdownbase.e-bigger .e-fixed-head {
  line-height: 48px;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-bigger .e-list-item .e-list-icon {
  font-size: 20px;
}

.e-dropdownbase {
  display: block;
  height: 100%;
  min-height: 36px;
  position: relative;
  width: 100%;
}

.e-dropdownbase .e-list-parent {
  margin: 0;
  padding: 0;
}

.e-dropdownbase .e-list-group-item,
.e-dropdownbase .e-fixed-head {
  cursor: default;
}

.e-dropdownbase .e-list-item {
  cursor: pointer;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-dropdownbase .e-list-item .e-list-icon {
  font-size: 16px;
  vertical-align: middle;
}

.e-dropdownbase .e-fixed-head {
  position: absolute;
  top: 0;
}

.e-rtl .e-dropdownbase .e-fixed-head {
  left: 33px;
}

.e-dropdownbase.e-content {
  overflow: auto;
  position: relative;
}

.e-popup.e-ddl .e-dropdownbase.e-nodata {
  color: #a4a4a4;
  cursor: default;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  padding: 14px 16px;
  text-align: center;
}

.e-rtl .e-dropdownbase.e-dd-group .e-list-item {
  padding-right: 30px;
}

.e-dropdownbase.e-dd-group .e-list-item {
  padding-left: 30px;
  text-indent: 0;
}

.e-small .e-dropdownbase.e-dd-group .e-list-item {
  text-indent: 0;
}

.e-small.e-bigger .e-dropdownbase.e-dd-group .e-list-item {
  text-indent: 0;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-group-item {
  text-indent: 0;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-group-item {
  cursor: pointer;
  font-weight: normal;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-rtl.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-item {
  padding-right: 30px;
}

.e-dropdownbase {
  border-color: #414141;
}

.e-dropdownbase .e-list-item {
  background-color: #2a2a2a;
  border-bottom: 1px;
  border-color: #6e6e6e;
  color: #f0f0f0;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 26px;
  min-height: 26px;
  padding-right: 20px;
  text-indent: 20px;
}

.e-dropdownbase .e-list-group-item, .e-fixed-head {
  background-color: #2a2a2a;
  border-color: #6e6e6e;
  color: #3e98ff;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 12px;
  font-weight: normal;
  line-height: 26px;
  min-height: 26px;
  padding-left: 20px;
  padding-right: 20px;
}

.e-dropdownbase .e-list-item.e-active,
.e-dropdownbase .e-list-item.e-active.e-hover {
  background-color: #0070f0;
  border-color: #6e6e6e;
  color: #fff;
}

.e-dropdownbase .e-list-item.e-hover {
  background-color: #414141;
  border-color: #6e6e6e;
  color: #f0f0f0;
}

.e-dropdownbase .e-list-item:last-child {
  border-bottom: 0;
}

.e-dropdownbase .e-list-item.e-item-focus {
  background-color: #414141;
}

.e-bigger .e-dropdownbase .e-list-group-item,
.e-bigger .e-dropdownbase .e-fixed-head {
  font-size: 14px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
}

.e-multi-column.e-ddl.e-popup.e-popup-open th,
.e-multi-column.e-ddl.e-popup.e-popup-open td {
  display: table-cell;
  overflow: hidden;
  padding-right: 16px;
  text-indent: 10px;
  text-overflow: ellipsis;
}

.e-multi-column.e-ddl.e-popup.e-popup-open th {
  line-height: 36px;
  text-align: left;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-ddl-header {
  background-color: #2a2a2a;
  border-color: #484848;
  border-style: solid;
  border-width: 0 0 1px 0;
  color: #3e98ff;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 12px;
  font-weight: normal;
  text-indent: 10px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-dropdownbase .e-list-item {
  padding-right: 0;
}

.e-multi-column.e-ddl.e-popup.e-popup-open.e-scroller .e-ddl-header {
  padding-right: 16px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-ddl-header,
.e-multi-column.e-ddl.e-popup.e-popup-open.e-ddl-device .e-ddl-header {
  padding-right: 0;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-center {
  text-align: center;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-right {
  text-align: right;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-left {
  text-align: left;
}

.e-small .e-dropdownbase .e-list-item,
.e-dropdownbase.e-small .e-list-item {
  color: #f0f0f0;
  line-height: 22px;
  min-height: 22px;
  text-indent: 12px;
}

.e-small .e-dropdownbase .e-list-group-item,
.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-small .e-list-group-item,
.e-dropdownbase.e-small .e-fixed-head {
  line-height: 22px;
  min-height: 22px;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-small .e-list-item .e-list-icon {
  font-size: 14px;
}

.e-bigger.e-small .e-dropdownbase .e-list-item,
.e-dropdownbase.e-small.e-bigger .e-list-item {
  color: #f0f0f0;
  line-height: 34px;
  min-height: 34px;
  text-indent: 16px;
}

.e-bigger.e-small .e-dropdownbase .e-list-group-item,
.e-bigger.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-small.e-bigger .e-list-group-item,
.e-dropdownbase.e-small.e-bigger .e-fixed-head {
  line-height: 34px;
  min-height: 34px;
}

.e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-small.e-bigger .e-list-item .e-list-icon {
  font-size: 18px;
}

.e-bigger.e-small .e-dropdownbase .e-list-group-item,
.e-bigger.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-bigger.e-small .e-list-group-item,
.e-dropdownbase.e-bigger.e-small .e-fixed-head {
  font-size: 14px;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item {
  background-color: #2a2a2a;
  border-bottom: 1px;
  border-color: #6e6e6e;
  color: #f0f0f0;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  padding-right: 20px;
  text-indent: 20px;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-item-focus {
  background-color: #414141;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-active,
.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-active.e-hover {
  background-color: #0070f0;
  border-color: #6e6e6e;
  color: #fff;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-hover {
  background-color: #414141;
  border-color: #6e6e6e;
  color: #f0f0f0;
}
