@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! accordion icons */
.e-control.e-accordion .e-tgl-collapse-icon::before {
  content: '\e916';
}

.e-control.e-accordion .e-tgl-collapse-icon.e-expand-icon {
  transform: rotate(-180deg);
}

/*! accordion layout */
.e-bigger .e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content, .e-control.e-accordion.e-bigger .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
  font-size: 16px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel {
  font-size: 14px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel.e-nested > .e-acrdn-content, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel.e-nested > .e-acrdn-content {
  padding: 0;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-acrdn-content .e-accordion, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-acrdn-content .e-accordion {
  border: 0;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header {
  padding: 0 46px 0 48px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-item.e-select .e-acrdn-header, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-item.e-select .e-acrdn-header {
  padding: 0 46px 0 32px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-header .e-acrdn-header-content, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-header .e-acrdn-header-content {
  font-size: 14px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-header, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-header {
  line-height: 47px;
  min-height: 48px;
  padding: 0 46px 0 16px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-header .e-toggle-icon, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-header .e-toggle-icon {
  height: 48px;
  min-height: 48px;
  min-width: 30px;
  right: 16px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-header .e-acrdn-header-icon, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-header .e-acrdn-header-icon {
  display: inline-block;
  padding: 0 14px 0 0;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content {
  padding: 16px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-content, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-content {
  padding: 16px 16px 16px 48px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel .e-acrdn-content, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 16px 16px 64px;
}

.e-bigger .e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-header, .e-control.e-accordion.e-bigger .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-header {
  padding: 0 46px 0 32px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel.e-nested > .e-acrdn-content, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel.e-nested > .e-acrdn-content {
  padding: 0;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-content, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-content {
  padding: 16px 48px 16px 16px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel.e-nested > .e-acrdn-content, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel.e-nested > .e-acrdn-content {
  padding: 0;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel .e-acrdn-content, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 64px 16px 16px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header {
  padding: 0 48px 0 46px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-item.e-select .e-acrdn-header, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item.e-select .e-acrdn-panel.e-nested .e-accordion .e-acrdn-item.e-select .e-acrdn-header {
  padding: 0 32px 0 46px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item .e-acrdn-header {
  padding: 0 16px 0 46px;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header .e-toggle-icon, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item .e-acrdn-header .e-toggle-icon {
  left: 16px;
  right: auto;
}

.e-bigger .e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header .e-acrdn-header-icon, .e-control.e-accordion.e-bigger.e-rtl .e-acrdn-item .e-acrdn-header .e-acrdn-header-icon {
  padding: 0 0 0 14px;
}

.e-control.e-accordion {
  display: block;
  position: relative;
}

.e-control.e-accordion .e-acrdn-item.e-select.e-selected:first-child {
  border-top: 0;
}

.e-control.e-accordion .e-acrdn-item.e-select.e-selected:last-child {
  border-bottom: 0;
}

.e-control.e-accordion .e-acrdn-item > .e-acrdn-header .e-acrdn-header-content {
  font-weight: normal;
}

.e-control.e-accordion .e-acrdn-item.e-selected {
  padding-top: 0;
}

.e-control.e-accordion .e-acrdn-item.e-selected > .e-acrdn-header .e-acrdn-header-content {
  font-weight: normal;
}

.e-control.e-accordion .e-acrdn-item {
  overflow: hidden;
  padding-top: "";
  position: relative;
}

.e-control.e-accordion .e-acrdn-item.e-hide {
  display: none;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
  font-size: 15px;
}

.e-control.e-accordion .e-acrdn-item.e-select > .e-acrdn-header {
  cursor: pointer;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header {
  line-height: 35px;
  min-height: 36px;
  overflow: hidden;
  padding: 0 40px 0 16px;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header > * {
  display: inline-block;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-toggle-icon {
  display: table;
  font-size: 12px;
  height: 36px;
  min-height: 36px;
  min-width: 24px;
  position: absolute;
  right: 16px;
  top: 0;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-toggle-icon .e-tgl-collapse-icon.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-toggle-animation {
  transition: .5s ease 0s;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-icon {
  display: inline-block;
  padding: 0 8px 0 0;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel {
  font-size: 13px;
  overflow-y: hidden;
  text-decoration: none;
  width: 100%;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested > .e-acrdn-content {
  padding: 0;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested > .e-acrdn-content .e-acrdn-panel.e-nested > .e-acrdn-content {
  padding: 0;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested > .e-acrdn-content .e-accordion {
  border: 0;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested > .e-acrdn-content .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header {
  padding: 0 40px 0 48px;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested .e-acrdn-item.e-select.e-selected .e-acrdn-header > .e-acrdn-header-content {
  font-weight: normal;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested .e-nested .e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
  font-weight: normal;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 16px 16px 48px;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 16px 16px 64px;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content {
  line-height: 1.5;
  overflow: hidden;
  padding: 16px;
  text-overflow: ellipsis;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content > * {
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content .e-acrdn-header {
  padding: 0 40px 0 32px;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content .e-acrdn-header-content {
  font-size: 14px;
}

.e-control.e-accordion .e-acrdn-item .e-content-hide {
  display: none;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-panel.e-nested > .e-acrdn-content .e-accordion .e-acrdn-panel.e-nested .e-acrdn-content .e-acrdn-header {
  padding: 0 48px 0 40px;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-panel .e-acrdn-content .e-acrdn-header {
  padding: 0 32px 0 40px;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 48px 16px 16px;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-panel .e-acrdn-panel .e-acrdn-panel .e-acrdn-content {
  padding: 16px 64px 16px 16px;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header {
  padding: 0 16px 0 40px;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header .e-toggle-icon {
  left: 16px;
  right: auto;
}

.e-control.e-accordion.e-rtl .e-acrdn-item .e-acrdn-header .e-acrdn-header-icon {
  padding: 0 0 0 8px;
}

/*! accordion theme */
.e-control.e-accordion {
  -webkit-tap-highlight-color: transparent;
  background: #fff;
  border: 1px solid #616161;
}

.e-control.e-accordion .e-active {
  background: #303030;
}

.e-control.e-accordion .e-acrdn-item.e-item-focus.e-select.e-selected.e-expand-state {
  border-top: 1px solid none;
}

.e-control.e-accordion .e-acrdn-item.e-item-focus.e-expand-state.e-select, .e-control.e-accordion .e-acrdn-item.e-item-focus.e-select.e-selected.e-expand-state {
  border-color: rgba(255, 255, 255, 0.05);
}

.e-control.e-accordion .e-acrdn-item.e-expand-state.e-select {
  border-bottom: 1px solid none;
  border-top: 1px solid none;
}

.e-control.e-accordion .e-acrdn-item.e-overlay {
  background: #fff;
}

.e-control.e-accordion .e-acrdn-item.e-selected.e-select.e-expand-state > .e-acrdn-header:focus {
  background: rgba(255, 255, 255, 0.05);
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel.e-nested .e-acrdn-header .e-acrdn-header-content {
  color: #00b0ff;
}

.e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content {
  color: #fff;
}

.e-control.e-accordion .e-acrdn-item.e-select .e-acrdn-panel .e-acrdn-content .e-content-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
  color: #00b0ff;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-accordion .e-acrdn-item.e-expand-state.e-select:not(.e-selected) > .e-acrdn-header:focus {
  background: rgba(255, 255, 255, 0.05);
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header:active {
  background: none;
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-header:focus {
  background: rgba(255, 255, 255, 0.05);
}

.e-control.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header, .e-control.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header {
  background: none;
}

.e-control.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header > .e-toggle-icon, .e-control.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header > .e-toggle-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header .e-acrdn-header-icon, .e-control.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header .e-acrdn-header-content, .e-control.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header .e-acrdn-header-icon, .e-control.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header .e-acrdn-header-content {
  color: #fff;
}

.e-control.e-accordion .e-acrdn-item.e-select {
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
}

.e-control.e-accordion .e-acrdn-item.e-select.e-item-focus {
  border-color: rgba(255, 255, 255, 0.05);
}

.e-control.e-accordion .e-acrdn-item.e-selected.e-select {
  border-bottom: 1px solid #616161;
  border-top: 1px solid #616161;
}

.e-control.e-accordion .e-acrdn-item.e-selected + .e-selected {
  border-top: 1px solid transparent;
}

.e-control.e-accordion .e-acrdn-item.e-selected .e-selected:last-child {
  border-bottom: 1px none #616161;
}

.e-control.e-accordion .e-acrdn-item.e-selected > .e-acrdn-panel .e-acrdn-content {
  color: #fff;
}

.e-control.e-accordion .e-acrdn-item.e-selected > .e-acrdn-panel .e-acrdn-header-content {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-accordion .e-acrdn-item .e-toggle-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-accordion .e-acrdn-item .e-acrdn-panel {
  font-size: 13px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
