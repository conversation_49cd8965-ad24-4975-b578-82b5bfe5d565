/*! component icons */
.e-datetime-wrapper .e-time-icon.e-icons::before {
  content: '\e97f';
  font-family: 'e-icons';
}

.e-input-group.e-control-wrapper.e-datetime-wrapper.e-non-edit.e-input-focus .e-input:focus ~ .e-clear-icon, .e-float-input.e-control-wrapper.e-input-group.e-datetime-wrapper.e-non-edit.e-input-focus input:focus ~ .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-datetime-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-datetime-wrapper .e-time-icon.e-icons::before {
  font-size: 16px;
}

.e-datetime-wrapper.e-control-wrapper {
  box-sizing: border-box;
}

.e-datetime-wrapper .e-time-icon.e-icons.e-disabled, .e-datetime-wrapper .e-date-icon.e-icons.e-disabled {
  pointer-events: none;
}

.e-datetime-wrapper .e-clear-icon {
  box-sizing: content-box;
}

.e-datetime-wrapper span {
  cursor: pointer;
}

.e-datetime-wrapper .e-input-group-icon.e-date-icon, .e-datetime-wrapper .e-input-group-icon.e-time-icon {
  font-size: 16px;
  margin: 0;
  outline: none;
}

.e-datetime-wrapper .e-input-group-icon.e-time-icon {
  border: 0;
  border-style: none;
  margin: 0;
}

.e-datetime-wrapper.e-rtl .e-input-group-icon.e-time-icon {
  margin: 0;
}

.e-control.e-datetimepicker.e-time-modal {
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-control.e-datetimepicker.e-popup {
  border-style: solid;
  border-width: 1px;
  overflow: auto;
}

.e-control.e-datetimepicker.e-popup .e-content {
  position: relative;
}

.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul {
  margin: 0;
  padding: 0 0;
}

.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item {
  color: #333;
  cursor: default;
  font-size: 14px;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item.e-hover {
  cursor: pointer;
}

.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item {
  line-height: 36px;
  text-indent: 10px;
}

.e-bigger.e-datetime-wrapper .e-time-icon.e-icons::before,
.e-control.e-bigger .e-datetime-wrapper .e-time-icon.e-icons::before {
  font-size: 20px;
}

.e-bigger.e-datetime-wrapper .e-input-group-icon.e-time-icon,
.e-control.e-bigger .e-datetime-wrapper .e-input-group-icon.e-time-icon {
  margin: 0;
}

.e-bigger.e-datetime-wrapper.e-rtl .e-input-group-icon.e-time-icon,
.e-control.e-bigger .e-datetime-wrapper.e-rtl .e-input-group-icon.e-time-icon {
  margin: 0;
}

.e-bigger .e-control.e-datetimepicker.e-popup .e-list-parent.e-ul, .e-control.e-bigger.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul {
  padding: 8px 0;
}

.e-bigger .e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-bigger.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 14px;
  line-height: 48px;
  text-indent: 12px;
}

.e-small .e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-small.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 13px;
  line-height: 26px;
  text-indent: 10px;
}

.e-small.e-bigger .e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-small.e-bigger.e-control.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 14px;
  line-height: 40px;
  text-indent: 16px;
}

.e-small.e-datetime-wrapper .e-time-icon.e-icons::before,
.e-control.e-small .e-datetime-wrapper .e-time-icon.e-icons::before {
  font-size: 14px;
}

.e-small.e-bigger.e-datetime-wrapper .e-time-icon.e-icons::before,
.e-control.e-small.e-bigger .e-datetime-wrapper .e-time-icon.e-icons::before {
  font-size: 18px;
}

.e-content-placeholder.e-datetimepicker.e-placeholder-datetimepicker {
  background-size: 250px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-datetimepicker.e-placeholder-datetimepicker, .e-bigger.e-content-placeholder.e-datetimepicker.e-placeholder-datetimepicker {
  background-size: 250px 40px;
  min-height: 40px;
}

/*! datetimepicker theme */
.e-datetime-wrapper .e-input-group-icon.e-icons.e-active {
  color: #333;
}

.e-datetime-wrapper.e-input-group:not(.e-disabled) .e-input-group-icon.e-active:active {
  color: #fff;
}

.e-datetimepicker.e-popup {
  border: 1px solid #eaeaea;
  border-radius: 0;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
}

.e-datetimepicker.e-popup .e-list-parent.e-ul {
  background-color: #fff;
}

.e-datetimepicker.e-popup .e-list-parent.e-ul li.e-list-item {
  border: none;
  color: #333;
}

.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item.e-hover, .e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item.e-navigation, .e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item:focus {
  background-color: #eaeaea;
  border: none;
  color: #333;
}

.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item.e-active {
  background-color: #0078d6;
  color: #fff;
}

.e-datetimepicker.e-popup .e-list-parent.e-ul .e-list-item.e-active.e-hover {
  background-color: #0078d6;
  color: #fff;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
