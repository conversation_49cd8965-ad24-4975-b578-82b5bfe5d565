.e-ddl.e-control.e-popup {
  border: 0;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
  margin-top: 1px;
}

.e-ddl.e-control.e-popup .e-input-group input {
  line-height: 15px;
}

.e-ddl.e-control.e-popup .e-input-group {
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.e-ddl.e-control.e-popup .e-filter-parent {
  border-left-width: 0;
  border-right-width: 0;
}

.e-bigger .e-control.e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-list-item, .e-bigger .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 15px;
  line-height: 45px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group {
  padding: 4px 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group input, .e-bigger .e-ddl.e-control.e-popup .e-input-group input.e-input {
  height: 30px;
}

.e-input-group:not(.e-disabled) .e-control.e-control.e-dropdownlist ~ .e-ddl-icon:active, .e-input-group:not(.e-disabled) .e-control.e-control.e-dropdownlist ~ .e-ddl-icon:hover, .e-ddl.e-control.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:active, .e-ddl.e-control.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:hover {
  background: transparent;
  color: #333;
}

.e-bigger .e-control.e-popup.e-ddl-device .e-input-group {
  margin: 0 0 0 55px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-list-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 14px;
  line-height: 40px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group {
  padding: 0;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group input, .e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group input.e-input {
  height: 34px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
