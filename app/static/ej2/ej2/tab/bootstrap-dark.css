/*! tab icons */
.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e219';
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e98f';
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before {
  content: '\e98f';
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  content: '\e219';
}

.e-tab .e-tab-header .e-close-icon::before {
  content: '\e7fc';
  position: relative;
}

.e-tab .e-tab-header .e-popup-up-icon::before {
  content: '\e988';
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-popup-up-icon::before {
    content: '\e936';
  }
}

.e-tab .e-tab-header .e-popup-down-icon::before {
  content: '\e968';
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-popup-down-icon::before {
    content: '\e936';
  }
}

@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon .e-tab-header .e-popup-up-icon::before {
    content: '\e918';
  }
  .e-tab.e-vertical-icon .e-tab-header .e-popup-down-icon::before {
    content: '\e968';
  }
}

.e-tab.e-vertical-tab .e-tab-header .e-scroll-nav .e-nav-up-arrow::before {
  content: '\ebba';
}

.e-tab.e-vertical-tab .e-tab-header .e-scroll-nav .e-nav-down-arrow::before {
  content: '\ebbc';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::before {
  content: 'More';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::after {
  content: '\ebb6';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::before {
  content: 'More';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::after {
  content: '\ebb6';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::before {
  content: '';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::after {
  content: '\ebb5';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::before {
  content: '';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::after {
  content: '\ebb5';
}

/*! tab layout */
.e-bigger .e-tab .e-tab-header,
.e-tab.e-bigger .e-tab-header {
  height: 50px;
  min-height: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items,
.e-tab.e-bigger .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: auto;
  height: 50px;
  min-height: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px -4px 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items .e-indicator + .e-toolbar-item:nth-last-child(1).e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-items .e-indicator + .e-toolbar-item:nth-last-child(1).e-active {
  margin: 1px 2px 0 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px -3px 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px -4px 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0 2px -5px 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 50px;
  margin: 0 2px -3px 0;
  min-height: 50px;
  min-width: auto;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
    margin: 0 2px -1px 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 50px;
  padding: 0 20px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 15px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-text {
  font-size: 15px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
  margin: 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
    margin: 1px 2px -4px 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft:not(.e-icon) .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft:not(.e-icon) .e-tab-icon::before {
  position: relative;
  top: -.5px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  margin-top: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap {
  padding: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  cursor: pointer;
  margin: 0 0 0 10px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  height: 24px;
  min-width: 24px;
  width: 24px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 20px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  padding: 0 20px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 10px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  right: 20px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 10px 0 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 10px;
}

.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 22px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 40px;
  min-height: 40px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 24px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    padding: 0 24px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 40px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'],
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
  padding-right: 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'],
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
    padding-right: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 24px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 0;
  padding-right: 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav {
  height: 50px;
  min-height: 50px;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav {
    padding: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0;
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0;
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  width: 44px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 16px;
  line-height: 44px;
  top: 0;
  vertical-align: initial;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav,
.e-tab.e-bigger .e-tab-header .e-hor-nav {
  height: 50px;
  min-height: 50px;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav,
  .e-tab.e-bigger .e-tab-header .e-hor-nav {
    padding: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  width: 44px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 16px;
  line-height: 44px;
  top: 0;
  vertical-align: initial;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  line-height: 42px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  top: 0;
  line-height: 42px;
  top: .5px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  line-height: 42px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  top: 0;
  line-height: 42px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  line-height: 42px;
  top: -2px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 15px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: -1px 2px 0 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: -1px 2px 0 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: -1px 2px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text {
  padding-top: 1px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-close-icon::before {
  top: .5px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap {
  height: initial;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-close-icon::before,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-close-icon::before {
  top: 1.5px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
  margin: -1px 2px 0 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
    margin: -1px 2px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-tab-text,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-tab-text {
  padding-top: .5px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-close-icon::before {
  top: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom {
  padding-bottom: 0;
  padding-top: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  padding: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
}

.e-bigger .e-tab .e-tab-header.e-vertical,
.e-tab.e-bigger .e-tab-header.e-vertical {
  max-width: 150px;
}

.e-bigger .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before,
.e-tab.e-bigger .e-tab-header.e-vertical[style*='overflow: hidden']::before {
  bottom: 23px;
  top: 23px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before,
  .e-tab.e-bigger .e-tab-header.e-vertical[style*='overflow: hidden']::before {
    bottom: 0;
    top: 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device),
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 24px 0;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -20px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
  min-height: 24px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 20px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  line-height: 48px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  font-size: 15px;
  line-height: 48px;
  margin: 0 0 0 8px;
}

.e-bigger .e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header,
.e-tab.e-bigger.e-vertical-icon .e-tab-header {
  height: 72px;
  min-height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-toolbar-items {
  height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-scroll-nav {
  height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav {
  height: 72px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}

.e-bigger .e-tab.e-vertical-icon.e-rtl .e-toolbar-item.e-active,
.e-tab.e-bigger.e-vertical-icon.e-rtl .e-toolbar-item.e-active {
  margin: 0 0 0 2px;
}

.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 50px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 72px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 50px;
}

.e-bigger .e-tab.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
  padding: 0;
}

.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}

.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 15px;
}

.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}

.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 13px;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-close-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-close-icon::before {
  top: 0;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 1px 0 -6px 2px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active,
.e-tab.e-bigger.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active {
  margin: 0 0 0 2px;
}

.e-bigger .e-tab.e-fill .e-tab-header,
.e-tab.e-bigger.e-fill .e-tab-header {
  height: 50px;
  min-height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header,
  .e-tab.e-bigger.e-fill .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 50px;
  margin: 0 2px 0 0;
  min-height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 50px;
  padding: 0 20px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 20px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 50px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 72px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}

.e-bigger .e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-bigger .e-tab.e-fill.e-vertical-icon .e-tab-header,
.e-tab.e-bigger.e-fill.e-vertical-icon .e-tab-header {
  height: 70px;
  min-height: 70px;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 2px 0 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 20px;
  }
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}

.e-bigger .e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 1px 0 -6px 2px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 1px 0 -6px 2px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 10px 0 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
    margin: 0 10px 0 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 10px 0 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 20px;
  right: auto;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 22px;
  margin-right: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 12px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 24px;
  right: auto;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 12px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-right-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-left-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  left: -20px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 20px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after {
  margin: 0 8px 0 0;
}

.e-tab {
  display: block;
  position: relative;
}

.e-tab.e-hidden {
  display: none;
}

.e-tab .e-tab-header {
  height: 40px;
  min-height: 40px;
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header::before {
  content: '';
  position: absolute;
  display: block;
}

.e-tab .e-tab-header:not(.e-vertical)::before {
  bottom: 0;
  top: 0;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-items {
  height: auto;
  margin: 0;
  min-height: 40px;
  position: relative;
  height: 40px;
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px -1px 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px -4px 0;
  }
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device {
  padding: 0;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device .e-scroll-right-nav {
  display: none;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll[style*='overflow: hidden'] .e-toolbar-item:not(.e-separator).e-active {
  margin-bottom: 0;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 40px;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px -2px 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px -4px 0;
  }
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0 2px -4px 0;
}

.e-tab .e-tab-header .e-hscroll-bar {
  overflow: hidden;
}

.e-tab .e-tab-header .e-indicator {
  display: none;
  position: absolute;
}

.e-tab .e-tab-header .e-indicator.e-hidden {
  display: none;
}

.e-tab .e-tab-header:not(.e-vertical) .e-indicator {
  bottom: 0;
  height: 2px;
  left: 0;
  right: 0;
  transition: left 0.125s cubic-bezier(0.35, 0, 0.25, 1), right 0.25s cubic-bezier(0.35, 0, 0.25, 1);
}

.e-tab .e-tab-header .e-toolbar-item {
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

.e-tab .e-tab-header .e-toolbar-item.e-hidden {
  display: none;
}

.e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 40px;
  margin: 0 2px -1px 0;
  min-height: 40px;
  min-width: auto;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 15px;
  width: 100%;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 15px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
  -ms-flex-line-pack: center;
      align-content: center;
  -ms-flex-align: center;
      align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-text {
  display: inherit;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-text {
    font-size: 15px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active {
  margin: 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active {
    margin: 1px 2px -4px 0;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  margin-top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-icon::before {
  top: -1px;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 40px;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  display: none;
  margin: 0 0 0 8px;
  min-width: 12px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon,
.e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 12px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}

@media screen\0 {
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
    top: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 20px;
  min-width: 20px;
  width: 20px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
    height: 20px;
    width: 20px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 16px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
    font-size: 20px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  -ms-flex-pack: center;
      justify-content: center;
  padding: 0 15px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
    padding: 0 20px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 8px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
  height: 62px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
    height: 72px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 62px;
  position: relative;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 72px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  display: block;
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  position: absolute;
  right: 15px;
  top: calc(50% - 6px);
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
    right: 12px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 8px 0 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
    margin: 10px 0 0;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 8px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0 0 10px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-bottom {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  margin: auto;
}

.e-tab .e-tab-header .e-toolbar-pop {
  overflow-y: auto;
  padding: 5px 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 26px;
  min-height: 26px;
  min-width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 26px;
  padding: 0 20px;
  text-align: initial;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 24px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 26px;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0 0 0 8px;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
  padding-right: 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
    padding-right: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-text-wrap {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  display: block;
  -ms-flex-pack: center;
      justify-content: center;
  margin: 0 0 0 10px;
  padding-left: 0;
  padding-right: 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    -ms-flex-pack: center;
        justify-content: center;
    margin: 0 0 0 12px;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 20px;
  top: initial;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-bottom {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
    display: none;
  }
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-active .e-close-icon {
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 20px;
}

.e-tab .e-tab-header .e-scroll-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0;
}

.e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
    padding: 0;
  }
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 16px;
  line-height: 34px;
  position: relative;
  top: 0;
  vertical-align: initial;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 16px;
  height: 34px;
  line-height: 34px;
  width: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}

.e-tab .e-tab-header .e-hor-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  padding: 0;
  width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav {
    height: 50px;
    min-height: 50px;
    padding: 0;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 16px;
  height: 34px;
  line-height: 34px;
  width: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 16px;
  line-height: 34px;
  position: relative;
  top: 0;
  vertical-align: initial;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  line-height: 34px;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  top: 0;
  line-height: 34px;
  top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
    line-height: 43px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
    transform: none;
    transition: none;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  line-height: 32px;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  top: 0;
  line-height: 32px;
  top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
  .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
    line-height: 43px;
  }
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  line-height: 32px;
  top: -.5px;
}

.e-tab .e-tab-header.e-horizontal-bottom::before {
  bottom: auto;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-hscroll-bar {
  margin-top: -1px;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: -1px 2px 0 0;
  padding-bottom: 0;
  padding-top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: -1px 2px 0 0;
  }
}

.e-tab .e-tab-header.e-horizontal-bottom .e-indicator {
  bottom: auto;
  top: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: -1px 2px 0 0;
  padding-bottom: 0;
  padding-top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: -1px 2px 0 0;
  }
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  padding: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: initial;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
  padding: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap::before, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap::before {
  bottom: auto;
  top: 0;
}

.e-tab .e-tab-header.e-vertical {
  max-width: 150px;
  z-index: 1;
}

.e-tab .e-tab-header.e-vertical::before {
  bottom: 0;
  height: 100%;
  left: 0;
  top: 0;
}

.e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before {
  bottom: 15px;
  height: auto;
  top: 15px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before {
    bottom: 0;
    top: 0;
  }
}

.e-tab .e-tab-header.e-vertical .e-indicator {
  display: none;
  transition: top 0.125s cubic-bezier(0.35, 0, 0.25, 1), bottom 0.25s cubic-bezier(0.35, 0, 0.25, 1);
  width: 2px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 16px 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item:last-child {
  margin: 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-text-wrap {
  position: relative;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text,
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-icon::before {
  text-align: center;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap::before {
  display: none;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -15px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  top: initial !important;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left {
  float: left;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left::before {
  right: 0;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left .e-indicator {
  left: auto;
  right: 0;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right {
  float: right;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right::before {
  right: auto;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-indicator {
  left: 0;
  right: auto;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-tab-wrap {
  text-align: right;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-pop .e-tab-text {
  width: auto;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
  -ms-flex-pack: center;
      justify-content: center;
  min-height: 16px;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  margin: 0 auto;
  width: 16px;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 15px;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  height: 100%;
  transform: none;
  transition: none;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: left;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  line-height: 36px;
  text-align: left;
  text-transform: capitalize;
  transform: none;
  transition: none;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: left;
  font-size: 12px;
  line-height: 36px;
  margin: 0 0 0 8px;
  vertical-align: initial;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
    font-size: 15px;
  }
}

.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-nav,
.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-overlay {
  display: none;
}

.e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: -15px;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 15px;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: right;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: right;
  margin: 0 8px 0 0;
}

.e-tab .e-content {
  position: relative;
}

.e-tab .e-content .e-item.e-view {
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}

.e-tab .e-content > .e-item {
  display: none;
}

.e-tab .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}

.e-tab .e-content.e-progress {
  overflow-x: hidden;
}

.e-tab.e-vertical-tab .e-content {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-vertical-tab .e-content .e-item.e-active {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: none;
      flex: none;
}

.e-tab.e-vertical-tab .e-content .e-item,
.e-tab.e-vertical-tab .e-content .e-item > :first-child {
  width: 100%;
}

.e-tab.e-vertical-icon .e-tab-header {
  height: 62px;
  min-height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-toolbar-items {
  height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-scroll-nav {
  height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-hor-nav {
  height: 62px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  display: none;
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap {
  position: relative;
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
}

.e-tab.e-vertical-icon.e-rtl .e-tab-header .e-toolbar-item.e-active {
  margin: 0 0 0 2px;
}

.e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 62px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 26px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 26px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 40px;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
  padding: 0;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-template .e-content > .e-item {
  display: none;
}

.e-tab.e-template .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 15px;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 14px;
}

.e-tab.e-ie .e-tab-header .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-popup-down-icon {
  transform: none;
  transition: none;
}

.e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 14px;
  position: relative;
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 11px;
  position: relative;
}

.e-tab.e-edge .e-tab-header .e-popup-up-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-down-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

@media screen and (max-width: 480px) and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-safari .e-tab-header .e-close-icon::before {
  top: -1px;
}

.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}

.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}

.e-tab.e-disable {
  pointer-events: none;
}

.e-tab.e-fill .e-tab-header {
  height: 40px;
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab.e-fill .e-tab-header::before {
  display: none;
}

.e-tab.e-fill .e-tab-header .e-indicator {
  display: none;
}

.e-tab.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 40px;
  margin: 0 2px 0 0;
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 62px;
  min-height: 62px;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 15px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 15px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 62px;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-itop {
  padding-top: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin-right: 0;
  padding: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 44px;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 62px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}

.e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-tab.e-fill.e-vertical-icon .e-tab-header {
  height: 60px;
  min-height: 60px;
}

.e-tab.e-background .e-tab-header::before {
  display: none;
}

.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 2px 0 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 20px;
  }
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}

.e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}

.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active,
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}

.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0;
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item.e-active .e-text-wrap {
  height: 26px;
}

.e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 1px 0 -4px 2px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 1px 0 -4px 2px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 1px 0 -4px 2px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 8px 0 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 8px 0 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 15px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 20px;
  margin-right: 0;
}

.e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}

.e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active {
  margin: 0 0 0 2px;
}

.e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active .e-tab-text {
  margin-bottom: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 10px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  left: 16px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 24px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 10px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 10px;
    padding-right: 0;
  }
}

.e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0;
}

.e-tab.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0;
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  display: none;
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  margin: 0;
  width: 100%;
}

/*! tab theme */
.e-tab {
  background: transparent;
  border: none;
}

.e-tab .e-tab-header {
  background: inherit;
  border: 0;
  border-radius: 0;
}

.e-tab .e-tab-header::before {
  border-color: #484848;
  border-style: solid;
}

.e-tab .e-tab-header:not(.e-vertical)::before {
  border-width: 0 0 1px;
}

.e-tab .e-tab-header[style*='overflow: hidden'] {
  border: 0;
}

.e-tab .e-tab-header:not(.e-vertical) .e-toolbar-item.e-active {
  border-bottom: 1px solid #fff;
}

.e-tab .e-tab-header .e-toolbar-items {
  background: inherit;
}

.e-tab .e-tab-header .e-indicator {
  background: #505050;
}

.e-tab .e-tab-header .e-toolbar-item {
  background: inherit;
  border: 1px solid transparent;
}

.e-tab .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(49, 49, 49, 0);
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  border-radius: 4px;
  color: #3e98ff;
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #3e98ff;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
    color: #3e98ff;
  }
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
    color: #3e98ff;
  }
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-close-icon {
    color: #f0f0f0;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: #313131;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
    background: initial;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #248aff;
}

.e-tab .e-tab-header .e-toolbar-item.e-active {
  border: 2px Solid #505050;
  border-radius: 4px 4px 0 0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-icon, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-icon {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-text, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-text {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon:hover {
  color: #fff;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon:active {
  color: #fff;
}

.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay {
  background: inherit;
  opacity: 1;
  pointer-events: none;
}

.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-icon {
  color: #393939;
}

.e-tab .e-tab-header .e-toolbar-pop {
  background: #2a2a2a;
  border: 1px solid #1a1a1a;
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  overflow-y: auto;
}

.e-tab .e-tab-header .e-toolbar-pop[e-animate='true'] {
  overflow-y: hidden;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #fff;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #313131;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #248aff;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active {
  background: grey-200;
}

.e-tab .e-tab-header .e-scroll-nav,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:hover {
  background: #313131;
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:active {
  box-shadow: none;
}

.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow {
  background: #2a2a2a;
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:hover {
  background: #313131;
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-scroll-nav:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  box-shadow: none;
}

.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow {
  color: rgba(240, 240, 240, 0.65);
}

.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow:hover {
  color: rgba(240, 240, 240, 0.65);
}

.e-tab .e-tab-header .e-hor-nav,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) {
  background: transparent;
  border: 0;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  background: #313131;
  border: 1px solid #313131;
  border-radius: 4px;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  color: #f0f0f0;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon {
  background: #2a2a2a;
  border: 1px solid none;
  border-radius: 4px;
  color: #f0f0f0;
  background: #2a2a2a;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:hover {
  background: #313131;
  border: 1px solid #313131;
  border-radius: 4px;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:active {
  background: #313131;
  border: 1px solid none;
  border-radius: 4px;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-tab .e-tab-header .e-hor-nav:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover {
  border: 0;
}

.e-tab .e-tab-header .e-hor-nav:hover:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover:active {
  background: transparent;
}

.e-tab .e-tab-header .e-hor-nav:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):active {
  box-shadow: none;
}

.e-tab .e-tab-header .e-hor-nav.e-nav-active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active {
  background: inherit;
  border: 0;
  box-shadow: none;
}

.e-tab .e-tab-header.e-horizontal-bottom[style*='overflow: hidden'] {
  border: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom[style*='overflow: hidden'] .e-toolbar-items {
  border-bottom: 0;
  border-top: 1px solid #484848;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  border-bottom: 0;
  border-radius: 0 0 4px 4px;
  border-top: 1px solid #fff;
  border-top-color: #505050;
  border-bottom: 1px solid #9c9c9c;
}

.e-tab .e-tab-header.e-vertical::before {
  border-width: 0 1px 0 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active {
  border-bottom: 1px solid #484848;
  border-top: 1px solid #484848;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item.e-active {
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #fff;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item.e-active {
  border-left: 1px solid #fff;
  border-radius: 0 4px 4px 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  background: inherit;
  border: 1px solid transparent;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-up-nav {
  border-bottom-color: #9c9c9c;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-down-nav {
  border-top-color: #9c9c9c;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow:hover {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover {
  background: #767676;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow:hover {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus {
  background: #767676;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active:hover {
  background: inherit;
  border: 0;
  box-shadow: none;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-down-icon {
  border: 0;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  background: #414141;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text {
  color: #f0f0f0;
}

.e-tab.e-focused .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab.e-focused .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-focused .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #f0f0f0;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav:focus {
  outline: 0;
}

.e-tab.e-disable {
  pointer-events: none;
}

.e-tab.e-disable .e-tab-header .e-indicator {
  background: rgba(80, 80, 80, 0.38);
}

.e-tab.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #393939;
}

.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #393939;
}

.e-tab.e-disable .e-content {
  opacity: 0.38;
}

.e-tab.e-fill .e-tab-header {
  border-bottom: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-items.e-hscroll {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(49, 49, 49, 0);
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #3e98ff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #248aff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #0070f0;
  border-radius: 4px;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover {
  color: #fff;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header.e-vertical {
  border-bottom: 0;
}

.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-left {
  border-right: 0;
}

.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-right {
  border-left: 0;
}

.e-tab.e-fill.e-corner .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 3px 3px 0 0;
}

.e-tab.e-fill.e-corner .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 0 0 3px 3px;
}

.e-tab.e-fill.e-disable .e-tab-header {
  border-bottom: 0;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #393939;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 0.65;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #0070f0;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill.e-disable .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 0;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #248aff;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #414141;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon {
  color: #002957;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header {
  background: inherit;
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-items {
  background: inherit;
}

.e-tab.e-background .e-tab-header .e-toolbar-items.e-hscroll {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-indicator {
  background: #505050;
}

.e-tab.e-background .e-tab-header .e-toolbar-item {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(49, 49, 49, 0);
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  background: inherit;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: #313131;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #002957;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon {
  color: #3e98ff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon:hover {
  color: #248aff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #0070f0;
  border-radius: 4px;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon {
  color: #393939;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #0070f0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-close-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #0070f0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-close-icon {
  color: #393939;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item {
  background: inherit;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #3e98ff;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #313131;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #fff;
  color: #0070f0;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:hover {
  background: inherit;
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover {
  background: inherit;
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:active::after {
  animation: none;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  color: #f0f0f0;
  background: #959595;
}

.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header .e-hor-nav:active::after {
  animation: none;
}

.e-tab.e-background .e-tab-header .e-hor-nav.e-nav-active .e-popup-up-icon {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background.e-disable .e-tab-header .e-indicator {
  background: rgba(80, 80, 80, 0.38);
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #393939;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #393939;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon {
  color: #393939;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 0.65;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #0070f0;
  color: #fff;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background.e-accent .e-tab-header .e-indicator {
  background: #505050;
}

.e-tab.e-background.e-accent .e-tab-header.e-disable .e-indicator {
  background: rgba(80, 80, 80, 0.38);
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  background: #313131;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #3e98ff;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #414141;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon {
  color: white;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-close-icon {
  color: #002957;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #0070f0;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap:focus,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap:focus {
  background: #414141;
}

.e-tab.e-background.e-focused .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #f0f0f0;
}

.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #f0f0f0;
}

.e-tab.e-rtl .e-tab-header .e-hscroll.e-rtl .e-scroll-nav {
  border: 0;
}

.e-tab .e-content {
  background: inherit;
}

.e-tab .e-content .e-item {
  background: inherit;
  color: #f0f0f0;
}
