.e-popup.e-ddl {
  border-radius: 4px;
  box-shadow: none;
  margin-top: 3px;
  overflow: auto;
}

.e-popup.e-ddl .e-input-group {
  width: auto;
}

.e-popup.e-ddl .e-input-group input {
  line-height: 15px;
}

.e-popup.e-ddl .e-dropdownbase {
  min-height: 26px;
}

.e-popup.e-ddl .e-filter-parent .e-input-group {
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.e-popup.e-ddl .e-filter-parent .e-input-group .e-back-icon {
  border: 0;
}

.e-bigger .e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-popup.e-ddl .e-list-item {
  font-size: 16px;
  line-height: 32px;
  padding-left: 0;
  text-indent: 24px;
}

.e-bigger .e-popup.e-ddl .e-list-group-item,
.e-bigger .e-popup.e-ddl .e-fixed-head {
  font-size: 14px;
  line-height: 32px;
  padding-left: 0;
  text-indent: 24px;
}

.e-bigger .e-popup.e-ddl .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger .e-popup.e-ddl .e-input-group {
  padding: 4px 0;
}

.e-bigger .e-popup.e-ddl .e-input-group input,
.e-bigger .e-popup.e-ddl .e-input-group input.e-input {
  font-size: 16px;
  height: 30px;
}

.e-bigger .e-popup.e-ddl .e-dropdownbase {
  min-height: 40px;
}

.e-input-group.e-control-wrapper.e-ddl .e-input[readonly],
.e-float-input.e-control-wrapper.e-ddl input[readonly] {
  background: transparent;
}

.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-ddl-icon:active,
.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-ddl-icon:hover,
.e-control.e-dropdownlist .e-input-group:not(.e-disabled) .e-ddl-icon:active,
.e-control.e-dropdownlist .e-input-group:not(.e-disabled) .e-ddl-icon:hover,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:active,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:hover,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-back-icon:active,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-back-icon:hover {
  background: transparent;
  color: #495057;
}

.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-ddl-icon,
.e-input-group.e-disabled.e-ddl .e-control.e-dropdownlist ~ .e-input-group-icon,
.e-control.e-dropdownlist .e-input-group.e-disabled.e-ddl .e-input-group-icon,
.e-control.e-dropdownlist .e-input-group.e-ddl .e-input-group-icon {
  border: 0;
}

.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-input-group-icon:active,
.e-control.e-dropdownlist .e-input-group:not(.e-disabled) .e-input-group-icon:active,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-back-icon:active,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:active {
  box-shadow: none;
}

.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon {
  background: transparent;
}

.e-ddl.e-popup .e-filter-parent .e-input-group,
.e-ddl.e-popup .e-filter-parent {
  background: rgba(0, 0, 0, 0.03);
}

.e-bigger .e-popup.e-ddl-device .e-input-group {
  margin: 0 0 0 55px;
}

.e-input-group .e-control.e-dropdownlist ~ .e-ddl-icon {
  font-size: 8px;
}

.e-bigger .e-input-group .e-control.e-dropdownlist ~ .e-ddl-icon {
  font-size: 10px;
}

.e-bigger.e-small .e-ddl.e-popup .e-list-item,
.e-bigger.e-small .e-ddl.e-popup .e-list-group-item,
.e-bigger.e-small .e-ddl.e-popup .e-fixed-head {
  font-size: 14px;
  line-height: 34px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger.e-small .e-ddl.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger.e-small .e-ddl.e-popup .e-input-group {
  padding: 4px 0;
}

.e-bigger.e-small .e-ddl.e-popup .e-input-group input,
.e-bigger.e-small .e-ddl.e-popup .e-input-group input.e-input {
  height: 30px;
}

.e-bigger.e-small .e-popup.e-ddl .e-dropdownbase {
  min-height: 34px;
}

.e-input-group.e-ddl .e-control.e-autocomplete ~ .e-ddl-icon {
  font-size: 8px;
}

.e-bigger .e-input-group.e-ddl .e-control.e-autocomplete ~ .e-ddl-icon {
  font-size: 10px;
}
