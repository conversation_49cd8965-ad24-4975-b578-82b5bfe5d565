import os
from collections import namedtuple
from sqlalchemy import create_engine, text

SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
if SQLALCHEMY_DATABASE_URI.startswith('postgres:'):
    SQLALCHEMY_DATABASE_URI = SQLALCHEMY_DATABASE_URI.replace('postgres:/', 'postgresql:/')
engine = create_engine(SQLALCHEMY_DATABASE_URI)

settings = namedtuple("Setting", "name description value")
VALUE_SETTINGS = [settings('LOCATION_SLOT', 'Site uses a Location Based Slot', 'Y'),
                  settings('BOOKING_URL', 'Booking URL for the site', 'https://bookings.rezibase.com/book'),
                  settings('CALENDLY_SLOTS', 'Slots for the site', None),
                  settings('REPORT_HEADER', 'Header for the site report', None),
                  settings('SITE_SMS_ENABLED', 'Enable SMS for this site', 'N'),
                  settings('WORKING_HOURS', 'Working Hours for this site', None),
                  settings('BADGE_HEADER', 'Header for the test badge', None),
                  settings('APPOINTMENT_TEMPLATE', 'SMS template for appointment reminder', None),
                  settings('BOOKING_TEMPLATE', 'SMS template for booking reminder', None)]


class GenerateSite:
    @staticmethod
    def update_site_settings(site_id):
        with engine.begin() as con:
            for setting in VALUE_SETTINGS:
                query = text(
                    "INSERT INTO site_settings(site_id, name, description, value, created, updated) "
                    "VALUES (:site_id, :name, :description, :value, now(), now()) ON CONFLICT DO NOTHING"
                )
                con.execute(query, {
                    'site_id': site_id,
                    'name': setting.name,
                    'description': setting.description,
                    'value': setting.value
                })

    def update_all_settings(self):
        with engine.connect() as con:
            sites = con.execute("Select id from SITE")
            for site_id in sites:
                self.update_site_settings(site_id[0])

    def create_site(self):
        site_name = input("Insert Site name")
        site_id = input("Insert id")
        confirm = input(f"Warning creating site with id={site_id} and name= {site_name} press Y to continue")
        if confirm == 'Y':
            with engine.connect() as con:
                con.execute(
                    text("INSERT INTO SITE (id, name, created, updated) VALUES (:id, :name, now(), now())"),
                    {"id": site_id, "name": site_name}
                )
        self.update_site_settings(site_id)
