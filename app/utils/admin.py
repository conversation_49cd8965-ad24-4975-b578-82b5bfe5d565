from flask import session
from flask_admin.contrib.sqla import ModelView
from flask_admin.contrib.sqla.filters import FilterEqual

from app import db
from common import TrialModelView
from .models import Site


def site_filtered_admin(model, admin, category='Settings', columns=None):
    def get_all_sites():
        sites = Site.query.with_entities(Site.id, Site.name).order_by(Site.name.asc()).all()
        return [(site.id, site.name) for site in sites]

    class SiteFilterAdmin(TrialModelView):
        column_filters = [
            FilterEqual(column=model.site_id, name='Site', options=get_all_sites),
        ]
        if columns:
            column_list = ['id', 'site', 'is_expired']

    admin.add_view(SiteFilterAdmin(model, db.session, category=category))


class SiteSpecificAdminMixin(ModelView):
    def col_style(self, col_name):
        return ''

    site_id_field = 'site_id'
    set_site_id = True

    def get_current_site_id(self):
        return session.get('site_id')

    def site_filter(self):
        return self.model.site_id == self.get_current_site_id()

    def get_query(self):
        return super().get_query().filter(self.site_filter())

    def get_count_query(self):
        return super().get_count_query().filter(self.site_filter())

    def on_model_change(self, form, model, is_created):
        if self.site_id_field and self.set_site_id:
            setattr(model, self.site_id_field, self.get_current_site_id())
        super().on_model_change(form, model, is_created)


class GlobalSiteAdminMixin(ModelView):
    def col_style(self, col_name):
        return ''

    def get_current_site_id(self):
        return session.get('site_id')

    def is_accessible(self):
        return self.get_current_site_id() == 1

    def get_query(self):
        site_id = self.get_current_site_id()
        if site_id == 1:
            return super().get_query()
        return None

    def get_count_query(self):
        site_id = self.get_current_site_id()
        if site_id == 1:
            return super().get_count_query()
        return None
