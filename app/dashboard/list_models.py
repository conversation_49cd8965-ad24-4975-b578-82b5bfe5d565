from app import db

class ListAboriginalStatus(db.Model):
    __tablename__ = 'list_aboriginalstatus'
    __table_args__ = {'extend_existing': True}

    hl7_code = db.Column('hl7_code', db.String(50), primary_key=True)
    hl7_description = db.Column('hl7_description', db.String(255))
    code = db.Column('code', db.String(50))
    description = db.Column('description', db.String(255))
    enabled = db.Column('enabled', db.<PERSON>)

class ListAccessTypes(db.Model):
    __tablename__ = 'list_accesstypes'
    __table_args__ = {'extend_existing': True}
    
    code = db.Column('code', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(50))
    enabled = db.Column('enabled', db.<PERSON>)

class ListAddressType(db.Model):
    __tablename__ = 'list_addresstype'
    __table_args__ = {'extend_existing': True}
    
    code = db.Column('code', db.String(2), primary_key=True)
    description = db.Column('description', db.String(50))

class ListDeathIndicator(db.Model):
    __tablename__ = 'list_deathindicator'
    __table_args__ = {'extend_existing': True}
    
    hl7_code = db.Column('hl7_code', db.String(50), primary_key=True, nullable=False)
    hl7_description = db.Column('hl7_description', db.String(255))
    code = db.Column('code', db.String(50))
    description = db.Column('description', db.String(255))

class ListEquipCategories(db.Model):
    __tablename__ = 'list_equip_categories'
    __table_args__ = {'extend_existing': True}
    
    category_id = db.Column('category_id', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(255), nullable=True)
    enabled = db.Column('enabled', db.Boolean, nullable=False)


class ListEquipDistributors(db.Model):
    __tablename__ = 'list_equip_distributors'
    __table_args__ = {'extend_existing': True}
    
    distributor_id = db.Column('distributor_id', db.Integer, primary_key=True)
    description = db.Column('description', db.String(255))
    contact_details = db.Column('contactdetails', db.Text)
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListEquipManufacturers(db.Model):
    __tablename__ = 'list_equip_manufacturers'
    __table_args__ = {'extend_existing': True}
    
    manufacturer_id = db.Column('manufacturer_id', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(255), nullable=True)
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListEquipModels(db.Model):
    __tablename__ = 'list_equip_models'
    __table_args__ = {'extend_existing': True}
    
    model_id = db.Column('model_id', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(255), nullable=True)
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListEquipModes(db.Model):
    __tablename__ = 'list_equip_modes'
    __table_args__ = {'extend_existing': True}
    
    mode_id = db.Column('mode_id', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(255), nullable=True)
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListEquipSettings(db.Model):
    __tablename__ = 'list_equip_settings'
    __table_args__ = {'extend_existing': True}
    
    setting_id = db.Column('setting_id', db.Integer, primary_key=True, autoincrement=True)
    description = db.Column('description', db.String(255), nullable=True)
    units = db.Column('units', db.String(255), nullable=True)
    longname = db.Column('longname', db.String(255), nullable=True)
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListFitExercises(db.Model):
    __tablename__ = 'list_fit_exercises'
    __table_args__ = {'extend_existing': True}
    
    fitexercise_id = db.Column('fitexerciseid', db.Integer, primary_key=True)
    description = db.Column('description', db.String(100))
    enabled = db.Column('enabled', db.Boolean)
    duration_sec_notused = db.Column('duration_sec_notused', db.String(5))
    pass_threshold_notused = db.Column('pass_threshold_notused', db.String(5))

class ListHealthServices(db.Model):
    __tablename__ = 'list_healthservices'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    description = db.Column('description', db.String(50))
    hs_enum_text = db.Column('hs_enum_text', db.String(50))
    code = db.Column('code', db.Integer)
    state = db.Column('state', db.String(3))
    owner_site = db.Column('owner_site', db.String(10))
    enabled_global = db.Column('enabled_global', db.Boolean)
    enabled_site = db.Column('enabled_site', db.Boolean)
    austin_vrss_satellite = db.Column('austin_vrss_satellite', db.Boolean)
    country = db.Column('country', db.String(255))
    enabled = db.Column('enabled', db.Boolean)

class ListLanguage(db.Model):
    __tablename__ = 'list_language'
    __table_args__ = {'extend_existing': True}
    
    list_language_id = db.Column('list_language_id', db.Integer, primary_key=True)
    hl7_code = db.Column('hl7_code', db.String(50), nullable=False)
    hl7_description = db.Column('hl7_description', db.String(255))
    code = db.Column('code', db.String(50))
    description = db.Column('description', db.String(255))
    enabled = db.Column('enabled', db.Boolean)
    site_id = db.Column('site_id', db.Integer, nullable=False, default=1)

class ListNationality(db.Model):
    __tablename__ = 'list_nationality'
    __table_args__ = {'extend_existing': True}
    
    hl7_code = db.Column('hl7_code', db.String(50), primary_key=True)
    hl7_description = db.Column('hl7_description', db.String(255))
    code = db.Column('code', db.String(50))
    description = db.Column('description', db.String(255))
    site_id = db.Column('site_id', db.Integer, nullable=False, default=1)

class ListPermissionTypes(db.Model):
    __tablename__ = 'list_permissiontypes'
    __table_args__ = {'extend_existing': True}
    
    perm_type_id = db.Column('permtypeid', db.Integer, primary_key=True)
    code = db.Column('code', db.String(50))
    category = db.Column('category', db.String(20))
    description = db.Column('description', db.String(100))
    displaytext = db.Column('displaytext', db.String(100))
    helptext = db.Column('helptext', db.String(255))
    datatype = db.Column('datatype', db.String(20))
    tablename_list = db.Column('tablename_list', db.String(50))
    defaultvalue = db.Column('defaultvalue', db.String(100))
    enabled = db.Column('enabled', db.Boolean)

class ListPsgTypes(db.Model):
    __tablename__ = 'list_psg_types'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    description = db.Column('description', db.String(255))
    code = db.Column('code', db.String(50))
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class ListReportStatuses(db.Model):
    __tablename__ = 'list_reportstatuses'
    __table_args__ = {'extend_existing': True}
    
    status_id = db.Column('statusid', db.Integer, primary_key=True)
    description = db.Column('description', db.String(45))
    verified_status = db.Column('verified_status', db.Boolean)
    enabled = db.Column('enabled', db.Boolean, nullable=False)
    display_order = db.Column('display_order', db.Integer)

class ListReportStatusesSleep(db.Model):
    __tablename__ = 'list_reportstatuses_sleep'
    __table_args__ = {'extend_existing': True}
    
    status_id = db.Column('statusid', db.Integer, primary_key=True)
    description = db.Column('description', db.String(45))
    verified_status = db.Column('verified_status', db.Boolean)
    enabled = db.Column('enabled', db.Boolean, nullable=False)
    display_order = db.Column('display_order', db.Integer)

class ListReportStyles(db.Model):
    __tablename__ = 'list_reportstyles'
    __table_args__ = {'extend_existing': True}
    
    style_id = db.Column('styleid', db.Integer, primary_key=True)
    code = db.Column('code', db.String(45))
    description = db.Column('description', db.String(250))

class ListTables(db.Model):
    __tablename__ = 'list_tables'
    __table_args__ = {'extend_existing': True}
    
    table_name = db.Column('table_name', db.Text, primary_key=True)

class ListUnits(db.Model):
    __tablename__ = 'list_units'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    unit_id = db.Column('unitid', db.Integer)
    description = db.Column('description', db.String(50))
    shortname = db.Column('shortname', db.String(20))
    enabled = db.Column('enabled', db.Boolean, nullable=False)

class PredRefAgeGroups(db.Model):
    __tablename__ = 'pred_ref_agegroups'
    __table_args__ = {'extend_existing': True}
    
    agegroup_id = db.Column('agegroupid', db.Integer, primary_key=True)
    age_group = db.Column('agegroup', db.String(255))
    description = db.Column('description', db.String(255))

class PredRefClipMethods(db.Model):
    __tablename__ = 'pred_ref_clipmethods'
    __table_args__ = {'extend_existing': True}
    
    clipmethod_id = db.Column('clipmethodid', db.Integer, primary_key=True)
    description = db.Column('description', db.String(255))

class PredRefEquationTypes(db.Model):
    __tablename__ = 'pred_ref_equationtypes'
    __table_args__ = {'extend_existing': True}
    
    equation_type_id = db.Column('equationtypeid', db.Integer, primary_key=True)
    equationtype = db.Column('equationtype', db.String(255))
    description = db.Column('description', db.String(255))

class PredRefEthnicities(db.Model):
    __tablename__ = 'pred_ref_ethnicities'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    ethnicity_id = db.Column('ethnicityid', db.Integer)
    ethnicity = db.Column('ethnicity', db.String(255))
    description = db.Column('description', db.String(255))
    list_option_for_demographics = db.Column('list_option_for_demographics', db.Boolean)

class PredRefEthnicityCorrectionFactors(db.Model):
    __tablename__ = 'pred_ref_ethnicity_correctionfactors'
    __table_args__ = {'extend_existing': True}
    
    factor_id = db.Column('factorid', db.Integer, primary_key=True)
    correction_method_id = db.Column('correctionmethodid', db.Integer)
    parameter_id = db.Column('parameterid', db.Integer)
    parameter = db.Column('parameter', db.String(45))
    factor = db.Column('factor', db.Float)

class PredRefEthnicityCorrectionMethods(db.Model):
    __tablename__ = 'pred_ref_ethnicity_correctionmethods'
    __table_args__ = {'extend_existing': True}
    
    correction_method_id = db.Column('correctionmethodid', db.Integer, primary_key=True)
    description = db.Column('description', db.String(255))
    reference = db.Column('reference', db.String(255))

class PredRefGenders(db.Model):
    __tablename__ = 'pred_ref_genders'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    gender_id = db.Column('genderid', db.Integer)
    gender_code = db.Column('gender_code', db.String(10))
    gender = db.Column('gender', db.String(255))
    description = db.Column('description', db.String(255))
    list_option_for_demographics = db.Column('list_option_for_demographics', db.Boolean)
    list_option_for_rfts = db.Column('list_option_for_rfts', db.Boolean)
    list_option_for_preds = db.Column('list_option_for_preds', db.Boolean)

class PredRefLimitTypes(db.Model):
    __tablename__ = 'pred_ref_limittypes'
    __table_args__ = {'extend_existing': True}
    
    limit_type_id = db.Column('limittypeid', db.Integer, primary_key=True)
    limit_type = db.Column('limittype', db.String(255))
    description = db.Column('description', db.String(255))

class PredRefParameterMapping(db.Model):
    __tablename__ = 'pred_ref_parameter_mapping'
    __table_args__ = {'extend_existing': True}
    
    parameter_mapping_id = db.Column('parameter_mapping_id', db.Integer, primary_key=True)
    healthservice_code_primary = db.Column('healthservice_code_primary', db.String(255))
    table_name = db.Column('table_name', db.String(255))
    table_field = db.Column('table_field', db.String(255))
    parameter_description_on_site = db.Column('parameter_description_on_site', db.String(255))
    site_units = db.Column('site_units', db.String(255))
    conversion_factor_to_rezibase_units = db.Column('conversion_factor_to_rezibase_units', db.Numeric(14, 6))
    test_type = db.Column('test_type', db.String(255))
    test_method_column = db.Column('test_method_column', db.String(255))
    test_condition_column = db.Column('test_condition_column', db.String(255))
    parameter_id = db.Column('parameterid', db.Integer)

class PredRefParameters(db.Model):
    __tablename__ = 'pred_ref_parameters'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    parameter_id = db.Column('parameterid', db.Integer)
    description = db.Column('description', db.String(255))
    test_id = db.Column('testid', db.Integer)
    reporttype_id_notused = db.Column('reporttypeid_notused', db.Integer)
    long_name = db.Column('longname', db.String(255))
    units = db.Column('units', db.String(255))
    units_si = db.Column('units_si', db.String(255))
    units_convert_trad_to_si = db.Column('units_convert_trad_to_si', db.Float)
    decimal_places = db.Column('decimalplaces', db.Integer)
    valid_low = db.Column('valid_low', db.Float)
    valid_high = db.Column('valid_high', db.Float)
    decimal_places_si = db.Column('decimalplaces_si', db.Integer)
    valid_low_si = db.Column('valid_low_si', db.Float)
    valid_high_si = db.Column('valid_high_si', db.Float)

class PredRefSources(db.Model):
    __tablename__ = 'pred_ref_sources'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    source_id = db.Column('sourceid', db.Integer)
    source = db.Column('source', db.String(255))
    pub_reference = db.Column('pub_reference', db.String(255))
    pub_year = db.Column('pub_year', db.String(10))
    last_edit = db.Column('lastedit', db.DateTime)
    last_edit_by = db.Column('lasteditby', db.String(50))
    description = db.Column('description', db.String(255))

class PredRefStatTypes(db.Model):
    __tablename__ = 'pred_ref_stattypes'
    __table_args__ = {'extend_existing': True}
    
    stat_type_id = db.Column('stattypeid', db.Integer, primary_key=True)
    stat_type = db.Column('stattype', db.String(255))
    description = db.Column('description', db.String(255))

class PredRefTests(db.Model):
    __tablename__ = 'pred_ref_tests'
    __table_args__ = {'extend_existing': True}
    
    test_id = db.Column('testid', db.Integer, primary_key=True)
    test = db.Column('test', db.String(255))
    description = db.Column('description', db.String(255))
    ganshorn_test_type = db.Column('ganshorn_testtype', db.String(255))

class PredRefVariables(db.Model):
    __tablename__ = 'pred_ref_variables'
    __table_args__ = {'extend_existing': True}
    
    variable_id = db.Column('variableid', db.Integer, primary_key=True)
    variable = db.Column('variable', db.String(255))
    units = db.Column('units', db.String(255))
    notes = db.Column('notes', db.String(255))
    description = db.Column('description', db.String(255))

class PrefsFields(db.Model):
    __tablename__ = 'prefs_fields'
    __table_args__ = {'extend_existing': True}

    field_id = db.Column('field_id', db.Integer, primary_key=True)
    fieldname = db.Column('fieldname', db.String(50))
    default_fielditem_id = db.Column('default_fielditem_id', db.Integer)
    site_id = db.Column('site_id', db.Integer, nullable=False, default=1)

    # Relationship with PrefsFieldItems
    fielditems = db.relationship('PrefsFieldItems', backref='field', lazy=True)


class PrefsFieldItems(db.Model):
    __tablename__ = 'prefs_fielditems'
    __table_args__ = {'extend_existing': True}

    prefs_id = db.Column('prefs_id', db.Integer, primary_key=True, autoincrement=True)
    field_id = db.Column('field_id', db.Integer, db.ForeignKey('prefs_fields.field_id'))
    fielditem = db.Column('fielditem', db.String(255))

class PredEquations(db.Model):
    __tablename__ = 'pred_equations'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    equationid = db.Column('equationid', db.Integer)
    test = db.Column('test', db.String(255))
    testid = db.Column('testid', db.Integer, db.ForeignKey('pred_ref_tests.testid'))
    source = db.Column('source', db.String(50))
    sourceid = db.Column('sourceid', db.Integer)
    parameter = db.Column('parameter', db.String(50))
    parameterid = db.Column('parameterid', db.Integer)
    equation_mpv = db.Column('equation_mpv', db.String(255))
    equation_range = db.Column('equation_range', db.String(255))
    equation_zscore = db.Column('equation_zscore', db.String(255))
    stattype_range = db.Column('stattype_range', db.String(20))
    stattype_rangeid = db.Column('stattype_rangeid', db.Integer, db.ForeignKey('pred_ref_stattypes.stattypeid'))
    equationtype = db.Column('equationtype', db.String(20))
    equationtypeid = db.Column('equationtypeid', db.Integer, db.ForeignKey('pred_ref_equationtypes.equationtypeid'))
    ethnicitycorrectiontype = db.Column('ethnicitycorrectiontype', db.String(255))
    ethnicitycorrectiontypeid = db.Column('ethnicitycorrectiontypeid', db.Integer)
    ethnicity = db.Column('ethnicity', db.String(255))
    ethnicityid = db.Column('ethnicityid', db.Integer)
    gender = db.Column('gender', db.String(255))
    genderid = db.Column('genderid', db.Integer)
    agegroup = db.Column('agegroup', db.String(15))
    agegroupid = db.Column('agegroupid', db.Integer, db.ForeignKey('pred_ref_agegroups.agegroupid'))
    age_lower = db.Column('age_lower', db.String(10))
    age_upper = db.Column('age_upper', db.String(10))
    ht_lower = db.Column('ht_lower', db.String(10))
    ht_upper = db.Column('ht_upper', db.String(10))
    wt_lower = db.Column('wt_lower', db.String(10))
    wt_upper = db.Column('wt_upper', db.String(10))
    lastedit = db.Column('lastedit', db.DateTime)
    lasteditby = db.Column('lasteditby', db.String(50))

class PredGliLookup(db.Model):
    __tablename__ = 'pred_gli_lookup'
    __table_args__ = {'extend_existing': True}
    
    age = db.Column('age', db.Float, primary_key=True)
    fev1_lspline_male = db.Column('fev1_lspline_male', db.Float)
    fev1_mspline_male = db.Column('fev1_mspline_male', db.Float)
    fev1_sspline_male = db.Column('fev1_sspline_male', db.Float)
    fvc_lspline_male = db.Column('fvc_lspline_male', db.Float)
    fvc_mspline_male = db.Column('fvc_mspline_male', db.Float)
    fvc_sspline_male = db.Column('fvc_sspline_male', db.Float)
    fer_lspline_male = db.Column('fer_lspline_male', db.Float)
    fer_mspline_male = db.Column('fer_mspline_male', db.Float)
    fer_sspline_male = db.Column('fer_sspline_male', db.Float)
    fef2575_lspline_male = db.Column('fef2575_lspline_male', db.Float)
    fef2575_mspline_male = db.Column('fef2575_mspline_male', db.Float)
    fef2575_sspline_male = db.Column('fef2575_sspline_male', db.Float)
    fef75_lspline_male = db.Column('fef75_lspline_male', db.Float)
    fef75_mspline_male = db.Column('fef75_mspline_male', db.Float)
    fef75_sspline_male = db.Column('fef75_sspline_male', db.Float)
    fev1_lspline_female = db.Column('fev1_lspline_female', db.Float)
    fev1_mspline_female = db.Column('fev1_mspline_female', db.Float)
    fev1_sspline_female = db.Column('fev1_sspline_female', db.Float)
    fvc_lspline_female = db.Column('fvc_lspline_female', db.Float)
    fvc_mspline_female = db.Column('fvc_mspline_female', db.Float)
    fvc_sspline_female = db.Column('fvc_sspline_female', db.Float)
    fer_lspline_female = db.Column('fer_lspline_female', db.Float)
    fer_mspline_female = db.Column('fer_mspline_female', db.Float)
    fer_sspline_female = db.Column('fer_sspline_female', db.Float)
    fef2575_lspline_female = db.Column('fef2575_lspline_female', db.Float)
    fef2575_mspline_female = db.Column('fef2575_mspline_female', db.Float)
    fef2575_sspline_female = db.Column('fef2575_sspline_female', db.Float)
    fef75_lspline_female = db.Column('fef75_lspline_female', db.Float)
    fef75_mspline_female = db.Column('fef75_mspline_female', db.Float)
    fef75_sspline_female = db.Column('fef75_sspline_female', db.Float)

class PredGliLvLookup(db.Model):
    __tablename__ = 'pred_gli_lv_lookup'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    parameter = db.Column('parameter', db.String(50))
    sex = db.Column('sex', db.String(50))
    age = db.Column('age', db.String(50))
    mspline = db.Column('mspline', db.String(50))
    sspline = db.Column('sspline', db.String(50))
    lspline = db.Column('lspline', db.String(50))

class PredGliTlcoLookup(db.Model):
    __tablename__ = 'pred_gli_tlco_lookup'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    parameter = db.Column('parameter', db.String(50))
    sex = db.Column('sex', db.String(50))
    age = db.Column('age', db.String(50))
    mspline = db.Column('mspline', db.String(50))
    sspline = db.Column('sspline', db.String(50))
    lspline = db.Column('lspline', db.String(50))

class PredLmsCoeff(db.Model):
    __tablename__ = 'pred_lms_coeff'
    __table_args__ = {'extend_existing': True}
    
    coefficientid = db.Column('coefficientid', db.Integer, primary_key=True)
    sourceid = db.Column('sourceid', db.Integer)
    parameter = db.Column('parameter', db.String(255))
    gender = db.Column('gender', db.String(255))
    intercept_l = db.Column('intercept_l', db.Float)
    age_l = db.Column('age_l', db.Float)
    intercept_m = db.Column('intercept_m', db.Float)
    height_m = db.Column('height_m', db.Float)
    age_m = db.Column('age_m', db.Float)
    afram_m = db.Column('afram_m', db.Float)
    neastasia_m = db.Column('neastasia_m', db.Float)
    seastasia_m = db.Column('seastasia_m', db.Float)
    othermixed_m = db.Column('othermixed_m', db.Float)
    intercept_s = db.Column('intercept_s', db.Float)
    age_s = db.Column('age_s', db.Float)
    afram_s = db.Column('afram_s', db.Float)
    neastasia_s = db.Column('neastasia_s', db.Float)
    seastasia_s = db.Column('seastasia_s', db.Float)
    othermixed_s = db.Column('othermixed_s', db.Float)

class PredLmsEquations(db.Model):
    __tablename__ = 'pred_lms_equations'
    __table_args__ = {'extend_existing': True}
    
    gliid = db.Column('gliid', db.Integer, primary_key=True)
    equationid = db.Column('equationid', db.Integer)
    sourceid = db.Column('sourceid', db.Integer)
    l_equation = db.Column('l_equation', db.String(250))
    m_equation = db.Column('m_equation', db.String(250))
    s_equation = db.Column('s_equation', db.String(250))
    lln_equation = db.Column('lln_equation', db.String(250))
    zscore_equation = db.Column('zscore_equation', db.String(250))

class PredNoncaucasianCorrections(db.Model):
    __tablename__ = 'pred_noncaucasian_corrections'
    __table_args__ = {'extend_existing': True}
    
    correctionid = db.Column('correctionid', db.Integer, primary_key=True)
    parameterid = db.Column('parameterid', db.Integer)
    parameter = db.Column('parameter', db.String(45))
    factor = db.Column('factor', db.Float)
    reference = db.Column('reference', db.String(100))

class PredSourcexEthnicity(db.Model):
    __tablename__ = 'pred_sourcexethnicity'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    sourceid = db.Column('sourceid', db.Integer)
    ethnicityid = db.Column('ethnicityid', db.Integer)

class PredSourcexGender(db.Model):
    __tablename__ = 'pred_sourcexgender'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    sourceid = db.Column('sourceid', db.Integer)
    genderid = db.Column('genderid', db.Integer, db.ForeignKey('pred_ref_genders.id'))

class PredSourcexParameter(db.Model):
    __tablename__ = 'pred_sourcexparameter'
    __table_args__ = {'extend_existing': True}
    
    sxpid = db.Column('sxpid', db.Integer, primary_key=True)
    paramid = db.Column('paramid', db.Integer)
    sourceid = db.Column('sourceid', db.Integer)

class PredSourcexTest(db.Model):
    __tablename__ = 'pred_sourcextest'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column('id', db.Integer, primary_key=True)
    sourceid = db.Column('sourceid', db.Integer)
    testid = db.Column('testid', db.Integer, db.ForeignKey('pred_ref_tests.testid'))


class PrefsPredNew(db.Model):
    __tablename__ = 'prefs_pred_new'
    __table_args__ = {'extend_existing': True}

    prefid = db.Column('prefid', db.Integer, primary_key=True)
    equationid = db.Column('equationid', db.Integer)
    age_clipmethod = db.Column('age_clipmethod', db.String(45))
    age_clipmethodid = db.Column('age_clipmethodid', db.Integer, db.ForeignKey('pred_ref_clipmethods.clipmethodid'))
    ht_clipmethod = db.Column('ht_clipmethod', db.String(45))
    ht_clipmethodid = db.Column('ht_clipmethodid', db.Integer, db.ForeignKey('pred_ref_clipmethods.clipmethodid'))
    wt_clipmethod = db.Column('wt_clipmethod', db.String(45))
    wt_clipmethodid = db.Column('wt_clipmethodid', db.Integer, db.ForeignKey('pred_ref_clipmethods.clipmethodid'))
    active = db.Column('active', db.String(3))
    startdate = db.Column('startdate', db.Date)
    enddate = db.Column('enddate', db.Date)
    markfordeletion = db.Column('markfordeletion', db.Integer)
    lastedit = db.Column('lastedit', db.DateTime)
    lasteditby = db.Column('lasteditby', db.String(50))
    site_id = db.Column('site_id', db.Integer, nullable=False, default=1)

    # Relationships
    age_clipmethod_rel = db.relationship('PredRefClipMethods', foreign_keys=[age_clipmethodid])
    ht_clipmethod_rel = db.relationship('PredRefClipMethods', foreign_keys=[ht_clipmethodid])
    wt_clipmethod_rel = db.relationship('PredRefClipMethods', foreign_keys=[wt_clipmethodid])
