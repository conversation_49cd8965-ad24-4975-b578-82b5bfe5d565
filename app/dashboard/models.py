from app import db
from audit_mixin import AuditableMixin
from sqlalchemy_utils import Timestamp


class SidebarModule(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'sidebar_modules'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    url = db.Column(db.String(255), nullable=False)
    icon = db.Column(db.String(100), nullable=False)
    enabled = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    order = db.Column(db.Integer, nullable=False, default=0)
    site_id = db.Column(db.Integer, db.<PERSON>ey('site.id'), nullable=False)

    def __repr__(self):
        return f"<SidebarModule(title='{self.title}', url='{self.url}', enabled={self.enabled})>"
