from app import db
from app.dashboard.admin import (
    SidebarModuleAdmin, ListAboriginalStatusAdmin,
    ListAccessTypesAdmin, ListAddressTypeAdmin, ListDeathIndicatorAdmin,
    ListEquipCategoriesAdmin, ListEquipDistributorsAdmin, ListEquipManufacturersAdmin,
    ListEquipModelsAdmin, ListEquipModesAdmin, ListEquipSettingsAdmin,
    ListFitExercisesAdmin, ListHealthServicesAdmin, ListLanguageAdmin,
    ListNationalityAdmin, ListPsgTypesAdmin,
    ListReportStatusesAdmin, ListReportStatusesSleepAdmin, ListReportStylesAdmin,
    ListTablesAdmin, ListUnitsAdmin, PredRefAgeGroupsAdmin, PredRefClipMethodsAdmin,
    PredRefEquationTypesAdmin, PredRefSourcesAdmin,
    PredRefStatTypesAdmin, PredRefTestsAdmin, PredRefVariablesAdmin,
    PredRefEthnicitiesAdmin, PredRefEthnicityCorrectionFactorsAdmin,
    PredRefEthnicityCorrectionMethodsAdmin, PredRefGendersAdmin,
    PredRefParameterMappingAdmin, PredRefParametersAdmin, PrefsFieldsAdmin,
    PrefsFieldItemsAdmin, PredRefLimitTypeAdmin,
    # New admin views
    PredEquationsAdmin, PredGliLookupAdmin, PredGliLvLookupAdmin,
    PredGliTlcoLookupAdmin, PredLmsCoeffAdmin, PredLmsEquationsAdmin,
    PredNoncaucasianCorrectionsAdmin, PredSourcexEthnicityAdmin,
    PredSourcexGenderAdmin, PredSourcexParameterAdmin, PredSourcexTestAdmin
)
from app.dashboard.list_models import (
    ListAboriginalStatus, ListAccessTypes, ListAddressType, ListDeathIndicator,
    ListEquipCategories, ListEquipDistributors, ListEquipManufacturers,
    ListEquipModels, ListEquipModes, ListEquipSettings, ListFitExercises,
    ListHealthServices, ListLanguage, ListNationality, ListPermissionTypes,
    ListPsgTypes, ListReportStatuses, ListReportStatusesSleep, ListReportStyles,
    ListTables, ListUnits, PredRefAgeGroups, PredRefClipMethods, PredRefEquationTypes,
    PredRefSources, PredRefStatTypes, PredRefTests, PredRefVariables,
    PredRefEthnicities, PredRefEthnicityCorrectionFactors,
    PredRefEthnicityCorrectionMethods, PredRefGenders,
    PredRefParameterMapping, PredRefParameters, PrefsFields, PrefsFieldItems, PredRefLimitTypes,
    # New models
    PredEquations, PredGliLookup, PredGliLvLookup, PredGliTlcoLookup,
    PredLmsCoeff, PredLmsEquations, PredNoncaucasianCorrections,
    PredSourcexEthnicity, PredSourcexGender, PredSourcexParameter, PredSourcexTest
)
from app.dashboard.models import SidebarModule


def add_admins(admin):
    admin.add_view(SidebarModuleAdmin(SidebarModule, db.session))

    admin.add_view(ListAboriginalStatusAdmin(ListAboriginalStatus, db.session, category="Configure Fields"))
    admin.add_view(ListAccessTypesAdmin(ListAccessTypes, db.session, category="Configure Fields"))
    admin.add_view(ListAddressTypeAdmin(ListAddressType, db.session, category="Configure Fields"))
    admin.add_view(ListDeathIndicatorAdmin(ListDeathIndicator, db.session, category="Configure Fields"))
    admin.add_view(ListEquipCategoriesAdmin(ListEquipCategories, db.session, category="Configure Fields"))
    admin.add_view(ListEquipDistributorsAdmin(ListEquipDistributors, db.session, category="Configure Fields"))
    admin.add_view(ListEquipManufacturersAdmin(ListEquipManufacturers, db.session, category="Configure Fields"))
    admin.add_view(ListEquipModelsAdmin(ListEquipModels, db.session, category="Configure Fields"))
    admin.add_view(ListEquipModesAdmin(ListEquipModes, db.session, category="Configure Fields"))
    admin.add_view(ListEquipSettingsAdmin(ListEquipSettings, db.session, category="Configure Fields"))
    admin.add_view(ListFitExercisesAdmin(ListFitExercises, db.session, category="Configure Fields"))
    admin.add_view(ListHealthServicesAdmin(ListHealthServices, db.session, category="Configure Fields"))
    admin.add_view(ListLanguageAdmin(ListLanguage, db.session, category="Configure Fields"))
    admin.add_view(ListNationalityAdmin(ListNationality, db.session, category="Configure Fields"))
    admin.add_view(ListPsgTypesAdmin(ListPsgTypes, db.session, category="Configure Fields"))
    admin.add_view(ListReportStatusesAdmin(ListReportStatuses, db.session, category="Configure Fields"))
    admin.add_view(ListReportStatusesSleepAdmin(ListReportStatusesSleep, db.session, category="Configure Fields"))
    admin.add_view(ListReportStylesAdmin(ListReportStyles, db.session, category="Configure Fields"))
    admin.add_view(ListTablesAdmin(ListTables, db.session, category="Configure Fields"))
    admin.add_view(ListUnitsAdmin(ListUnits, db.session, category="Configure Fields"))

    admin.add_view(PredRefAgeGroupsAdmin(PredRefAgeGroups, db.session, category="Configure Fields"))
    admin.add_view(PredRefEquationTypesAdmin(PredRefEquationTypes, db.session, category="Configure Fields"))
    admin.add_view(PredRefEthnicitiesAdmin(PredRefEthnicities, db.session, category="Configure Fields"))
    admin.add_view(
        PredRefEthnicityCorrectionFactorsAdmin(
            PredRefEthnicityCorrectionFactors, db.session, category="Configure Fields"
        )
    )
    admin.add_view(
        PredRefEthnicityCorrectionMethodsAdmin(
            PredRefEthnicityCorrectionMethods, db.session, category="Configure Fields"
        )
    )
    admin.add_view(PredRefGendersAdmin(PredRefGenders, db.session, category="Configure Fields"))
    admin.add_view(PredRefLimitTypeAdmin(PredRefLimitTypes, db.session, category="Configure Fields"))
    admin.add_view(PredRefParameterMappingAdmin(PredRefParameterMapping, db.session, category="Configure Fields"))
    admin.add_view(PredRefParametersAdmin(PredRefParameters, db.session, category="Configure Fields"))
    admin.add_view(PredRefStatTypesAdmin(PredRefStatTypes, db.session, category="Configure Fields"))

    # Add PrefsFields admin
    admin.add_view(PrefsFieldsAdmin(PrefsFields, db.session, category="Configure Fields"))
    admin.add_view(PrefsFieldItemsAdmin(PrefsFieldItems, db.session, category="Configure Fields"))

    # Add Normal values admin views
    admin.add_view(PredEquationsAdmin(PredEquations, db.session, category="Normal Values"))
    admin.add_view(PredGliLookupAdmin(PredGliLookup, db.session, category="Normal Values"))
    admin.add_view(PredGliLvLookupAdmin(PredGliLvLookup, db.session, category="Normal Values"))
    admin.add_view(PredGliTlcoLookupAdmin(PredGliTlcoLookup, db.session, category="Normal Values"))
    admin.add_view(PredRefTestsAdmin(PredRefTests, db.session, category="Normal Values"))
    admin.add_view(PredRefSourcesAdmin(PredRefSources, db.session, category="Normal Values"))
    admin.add_view(PredRefVariablesAdmin(PredRefVariables, db.session, category="Normal Values"))
    admin.add_view(PredRefClipMethodsAdmin(PredRefClipMethods, db.session, category="Normal Values"))
    admin.add_view(PredLmsCoeffAdmin(PredLmsCoeff, db.session, category="Normal Values"))
    admin.add_view(PredLmsEquationsAdmin(PredLmsEquations, db.session, category="Normal Values"))
    admin.add_view(PredNoncaucasianCorrectionsAdmin(PredNoncaucasianCorrections, db.session, category="Normal Values"))
    admin.add_view(PredSourcexEthnicityAdmin(PredSourcexEthnicity, db.session, category="Normal Values"))
    admin.add_view(PredSourcexGenderAdmin(PredSourcexGender, db.session, category="Normal Values"))
    admin.add_view(PredSourcexParameterAdmin(PredSourcexParameter, db.session, category="Normal Values"))
    admin.add_view(PredSourcexTestAdmin(PredSourcexTest, db.session, category="Normal Values"))
