from flask import session
from flask_admin.contrib.sqla import <PERSON><PERSON>ie<PERSON>
from flask_admin.model.form import InlineFormAdmin
from wtforms import Form, StringField, IntegerField, SelectField
from wtforms import validators

from app.dashboard.list_models import PrefsFieldItems, PrefsFields
from app.utils.admin import GlobalSiteAdminMixin, SiteSpecificAdminMixin
from common import TrialModelView


class SidebarModuleAdmin(SiteSpecificAdminMixin, ModelView):
    column_list = ['title', 'url', 'icon', 'enabled', 'order']
    column_searchable_list = ['title', 'url']
    column_filters = ['enabled', 'order']
    form_columns = ['title', 'url', 'icon', 'enabled', 'order']
    column_default_sort = 'order'


class ListAboriginalStatusAdmin(GlobalSiteAdminMixin):
    column_list = ['hl7_code', 'hl7_description', 'code', 'description', 'enabled']
    column_filters = ['hl7_code', 'hl7_description', 'code', 'description', 'enabled']


class ListAccessTypesAdmin(GlobalSiteAdminMixin):
    column_list = ['code', 'description', 'enabled']
    form_columns = ['description', 'enabled']
    column_default_sort = 'code'
    column_filters = ['description', 'enabled']
    column_searchable_list = ['description']


class ListAddressTypeAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'code', 'description']
    form_columns = ['code', 'description']
    column_default_sort = 'code'
    column_filters = ['code', 'description']
    column_searchable_list = ['code', 'description']


class ListDeathIndicatorAdmin(GlobalSiteAdminMixin):
    column_list = ['hl7_code', 'hl7_description', 'code', 'description']
    form_columns = ['hl7_code', 'hl7_description', 'code', 'description']
    column_default_sort = 'hl7_code'
    column_filters = ['hl7_code', 'code']
    column_searchable_list = ['hl7_code', 'hl7_description', 'code', 'description']


class ListEquipCategoriesAdmin(GlobalSiteAdminMixin):
    column_list = ['category_id', 'description', 'enabled']
    form_columns = ['description', 'enabled']
    column_default_sort = 'category_id'
    column_filters = ['description', 'enabled']
    column_searchable_list = ['description']


class ListEquipDistributorsAdmin(GlobalSiteAdminMixin):
    column_list = ['distributor_id', 'description', 'contact_details', 'enabled']
    form_columns = ['distributor_id', 'description', 'contact_details', 'enabled']
    column_default_sort = 'distributor_id'
    column_filters = ['distributor_id', 'description', 'enabled']
    column_searchable_list = ['distributor_id', 'description']


class ListEquipManufacturersAdmin(GlobalSiteAdminMixin):
    column_list = ['manufacturer_id', 'description', 'enabled']
    form_columns = ['manufacturer_id', 'description', 'enabled']
    column_default_sort = 'manufacturer_id'
    column_filters = ['manufacturer_id', 'description', 'enabled']
    column_searchable_list = ['manufacturer_id', 'description']


class ListEquipModelsAdmin(GlobalSiteAdminMixin):
    column_list = ['model_id', 'description', 'enabled']
    form_columns = ['model_id', 'description', 'enabled']
    column_default_sort = 'model_id'
    column_filters = ['model_id', 'description', 'enabled']
    column_searchable_list = ['model_id', 'description']


class ListEquipModesAdmin(GlobalSiteAdminMixin):
    column_list = ['mode_id', 'description', 'enabled']
    form_columns = ['mode_id', 'description', 'enabled']
    column_default_sort = 'mode_id'
    column_filters = ['mode_id', 'description', 'enabled']
    column_searchable_list = ['mode_id', 'description']


class ListEquipSettingsAdmin(GlobalSiteAdminMixin):
    column_list = ['setting_id', 'description', 'units', 'longname', 'enabled']
    form_columns = ['setting_id', 'description', 'units', 'longname', 'enabled']
    column_default_sort = 'setting_id'
    column_filters = ['setting_id', 'description', 'enabled']
    column_searchable_list = ['setting_id', 'description']


class ListFitExercisesAdmin(GlobalSiteAdminMixin):
    column_list = ['fitexercise_id', 'description', 'enabled']
    form_columns = ['fitexercise_id', 'description', 'enabled']
    column_default_sort = 'fitexercise_id'
    column_filters = ['fitexercise_id', 'description', 'enabled']
    column_searchable_list = ['fitexercise_id', 'description']


class ListHealthServicesAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'description', 'hs_enum_text', 'code', 'state', 'owner_site', 'enabled_global', 'enabled_site',
                   'austin_vrss_satellite', 'country', 'enabled']
    form_columns = ['id', 'description', 'hs_enum_text', 'code', 'state', 'owner_site', 'enabled_global',
                    'enabled_site', 'austin_vrss_satellite', 'country', 'enabled']
    column_default_sort = 'id'
    column_filters = ['id', 'description', 'code', 'state', 'enabled']
    column_searchable_list = ['id', 'description', 'code']


class ListLanguageAdmin(SiteSpecificAdminMixin, TrialModelView):
    column_list = ['list_language_id', 'hl7_code', 'hl7_description', 'code', 'description', 'enabled']
    form_columns = ['list_language_id', 'hl7_code', 'hl7_description', 'code', 'description', 'enabled']
    column_default_sort = 'list_language_id'
    column_filters = ['list_language_id', 'hl7_code', 'code', 'enabled']
    column_searchable_list = ['list_language_id', 'hl7_code', 'code', 'description']


class ListNationalityAdmin(SiteSpecificAdminMixin, TrialModelView):
    column_list = ['hl7_code', 'hl7_description', 'code', 'description']
    form_columns = ['hl7_code', 'hl7_description', 'code', 'description']
    column_default_sort = 'hl7_code'
    column_filters = ['hl7_code', 'code']
    column_searchable_list = ['hl7_code', 'hl7_description', 'code', 'description']


class ListPsgTypesAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'code', 'description', 'enabled']
    form_columns = ['id', 'code', 'description', 'enabled']
    column_default_sort = 'id'
    column_filters = ['id', 'code', 'enabled']
    column_searchable_list = ['id', 'code', 'description']


class ListReportStatusesAdmin(GlobalSiteAdminMixin):
    column_list = ['status_id', 'description', 'verified_status', 'enabled', 'display_order']
    form_columns = ['status_id', 'description', 'verified_status', 'enabled', 'display_order']
    column_default_sort = 'status_id'
    column_filters = ['status_id', 'verified_status', 'enabled']
    column_searchable_list = ['status_id', 'description']


class ListReportStatusesSleepAdmin(GlobalSiteAdminMixin):
    column_list = ['status_id', 'description', 'verified_status', 'enabled', 'display_order']
    form_columns = ['status_id', 'description', 'verified_status', 'enabled', 'display_order']
    column_default_sort = 'status_id'
    export_types = ['csv', 'xlsx']
    column_filters = ['status_id', 'verified_status', 'enabled']
    column_searchable_list = ['status_id', 'description']


class ListReportStylesAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'code', 'description']
    form_columns = ['code', 'description']
    column_default_sort = 'code'
    column_filters = ['code', 'description']
    column_searchable_list = ['code', 'description']


class ListTablesAdmin(GlobalSiteAdminMixin):
    column_list = ['table_name']
    form_columns = ['table_name']
    column_default_sort = 'table_name'
    column_filters = ['table_name']
    column_searchable_list = ['table_name']


class ListUnitsAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'unit_id', 'description', 'shortname', 'enabled']
    form_columns = ['unit_id', 'description', 'shortname', 'enabled']
    column_default_sort = 'id'
    column_filters = ['unit_id', 'enabled']
    column_searchable_list = ['description', 'shortname']


class PredRefAgeGroupsAdmin(GlobalSiteAdminMixin):
    column_list = ['agegroup_id', 'age_group', 'description']
    form_columns = ['age_group', 'description']
    column_default_sort = 'agegroup_id'
    column_filters = ['age_group']
    column_searchable_list = ['age_group', 'description']


class PredRefClipMethodsAdmin(GlobalSiteAdminMixin):
    column_list = ['clipmethod_id', 'description']
    form_columns = ['description']
    column_default_sort = 'clipmethod_id'
    column_filters = ['description']
    column_searchable_list = ['description']


class PredRefEquationTypesAdmin(GlobalSiteAdminMixin):
    column_list = ['equation_type_id', 'equationtype', 'description']
    form_columns = ['equationtype', 'description']
    column_default_sort = 'equation_type_id'
    column_filters = ['equationtype']
    column_searchable_list = ['equationtype', 'description']


class PredRefEthnicitiesAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'ethnicity_id', 'ethnicity', 'description', 'list_option_for_demographics']
    form_columns = ['ethnicity_id', 'ethnicity', 'description', 'list_option_for_demographics']
    column_default_sort = 'id'
    column_filters = ['ethnicity', 'list_option_for_demographics']
    column_searchable_list = ['ethnicity', 'description']


class PredRefEthnicityCorrectionFactorsAdmin(GlobalSiteAdminMixin):
    column_list = ['factor_id', 'correction_method_id', 'parameter_id', 'parameter', 'factor']
    form_columns = ['correction_method_id', 'parameter_id', 'parameter', 'factor']
    column_default_sort = 'factor_id'
    column_filters = ['correction_method_id', 'parameter_id', 'parameter']
    column_searchable_list = ['parameter']


class PredRefEthnicityCorrectionMethodsAdmin(GlobalSiteAdminMixin):
    column_list = ['correction_method_id', 'description', 'reference']
    form_columns = ['description', 'reference']
    column_default_sort = 'correction_method_id'
    column_filters = ['description']
    column_searchable_list = ['description', 'reference']


class PredRefGendersAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'gender_id', 'gender_code', 'gender', 'description', 'list_option_for_demographics',
                   'list_option_for_rfts', 'list_option_for_preds']
    form_columns = ['gender_id', 'gender_code', 'gender', 'description', 'list_option_for_demographics',
                    'list_option_for_rfts', 'list_option_for_preds']
    column_default_sort = 'id'
    column_filters = ['gender', 'list_option_for_demographics', 'list_option_for_rfts', 'list_option_for_preds']
    column_searchable_list = ['gender', 'description']


class PredRefLimitTypeAdmin(GlobalSiteAdminMixin):
    column_list = ['limit_type_id', 'limit_type', 'description']
    form_columns = ['limit_type_id', 'limit_type', 'description']
    column_default_sort = 'limit_type_id'
    column_filters = ['limit_type_id', 'limit_type', 'description']
    column_searchable_list = ['description']


class PredRefParameterMappingAdmin(GlobalSiteAdminMixin):
    column_list = ['parameter_mapping_id', 'healthservice_code_primary', 'table_name', 'table_field',
                   'parameter_description_on_site', 'site_units', 'conversion_factor_to_rezibase_units', 'test_type',
                   'test_method_column', 'test_condition_column', 'parameter_id']
    form_columns = ['healthservice_code_primary', 'table_name', 'table_field', 'parameter_description_on_site',
                    'site_units', 'conversion_factor_to_rezibase_units', 'test_type', 'test_method_column',
                    'test_condition_column', 'parameter_id']
    column_default_sort = 'parameter_mapping_id'
    column_filters = ['healthservice_code_primary', 'table_name', 'parameter_id']
    column_searchable_list = ['healthservice_code_primary', 'table_name', 'table_field',
                              'parameter_description_on_site']


class PredRefParametersAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'parameter_id', 'description', 'test_id', 'long_name', 'units', 'units_si',
                   'units_convert_trad_to_si', 'decimal_places', 'valid_low', 'valid_high', 'decimal_places_si',
                   'valid_low_si', 'valid_high_si']
    form_columns = ['parameter_id', 'description', 'test_id', 'reporttype_id_notused', 'long_name', 'units', 'units_si',
                    'units_convert_trad_to_si', 'decimal_places', 'valid_low', 'valid_high', 'decimal_places_si',
                    'valid_low_si', 'valid_high_si']
    column_default_sort = 'id'
    column_filters = ['parameter_id', 'test_id']
    column_searchable_list = ['description', 'long_name', 'units']


class PredRefSourcesAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'source_id', 'source', 'pub_reference', 'pub_year', 'last_edit', 'last_edit_by', 'description']
    form_columns = ['source_id', 'source', 'pub_reference', 'pub_year', 'last_edit', 'last_edit_by', 'description']
    column_default_sort = 'id'
    column_filters = ['source_id', 'pub_year']
    column_searchable_list = ['source', 'pub_reference', 'description']


class PredRefStatTypesAdmin(GlobalSiteAdminMixin):
    column_list = ['stat_type_id', 'stat_type', 'description']
    form_columns = ['stat_type', 'description']
    column_default_sort = 'stat_type_id'
    column_filters = ['stat_type']
    column_searchable_list = ['stat_type', 'description']


class PredRefTestsAdmin(GlobalSiteAdminMixin):
    column_list = ['test_id', 'test', 'description', 'ganshorn_test_type']
    form_columns = ['test', 'description', 'ganshorn_test_type']
    column_default_sort = 'test_id'
    column_filters = ['test', 'ganshorn_test_type']
    column_searchable_list = ['test', 'description']


class PredRefVariablesAdmin(GlobalSiteAdminMixin):
    column_list = ['variable_id', 'variable', 'units', 'notes', 'description']
    form_columns = ['variable', 'units', 'notes', 'description']
    column_default_sort = 'variable_id'
    column_filters = ['variable']
    column_searchable_list = ['variable', 'description']


class PrefsFieldItemsForm(Form):
    field_id = SelectField('Preference Field', coerce=int, validators=[validators.DataRequired()])
    fielditem = StringField('Field Item', [validators.DataRequired(), validators.Length(max=255)])

    def __init__(self, *args, **kwargs):
        super(PrefsFieldItemsForm, self).__init__(*args, **kwargs)
        # Populate the field_id choices with existing PrefsFields
        from app.dashboard.list_models import PrefsFields
        self.field_id.choices = [(field.field_id, field.fieldname) for field in PrefsFields.query.all()]


class PrefsFieldItemsInline(InlineFormAdmin):
    form_columns = ['prefs_id', 'fielditem', 'delete']
    column_labels = {
        'fielditem': 'Field Item Value'
    }
    form_widget_args = {
        'prefs_id': {'class': 'form-control'},
        'fielditem': {'class': 'form-control'}
    }
    form_args = {
        'delete': {'default': False}
    }
    can_delete = True


class PrefsFieldsForm(Form):
    fieldname = StringField('Field Name', [validators.DataRequired(), validators.Length(max=50)])
    default_fielditem_id = IntegerField('Default Field Item ID', [validators.Optional()])


class PrefsFieldsAdmin(SiteSpecificAdminMixin, ModelView):
    def col_style(self, col_name):
        return ''

    column_list = ['field_id', 'fieldname', 'default_fielditem_id']
    column_labels = {
        'field_id': 'Field ID',
        'fieldname': 'Field Name',
        'default_fielditem_id': 'Default Field Item ID'
    }
    form_columns = ['fieldname', 'default_fielditem_id', 'fielditems']
    column_default_sort = 'field_id'

    column_descriptions = {
        'field_id': 'Field ID',
        'fieldname': 'Name of the preference field',
        'default_fielditem_id': 'Default field item ID'
    }

    form_widget_args = {
        'fieldname': {'class': 'form-control'},
        'default_fielditem_id': {'class': 'form-control'}
    }

    inline_models = [(PrefsFieldItems, dict(
        can_delete=True,
        form_widget_args={'prefs_id': {'type': 'hidden'}, 'fielditem': {'class': 'form-control'}}
    ))]

    can_create = True
    can_edit = True
    can_delete = True
    can_view_details = True
    can_export = True
    export_types = ['csv', 'xlsx']
    column_filters = ['fieldname']
    column_searchable_list = ['fieldname']


class PrefsFieldItemsAdmin(TrialModelView):
    def get_query(self):
        return (super().get_query()
                .join(PrefsFields, PrefsFields.field_id == PrefsFieldItems.field_id)
                .filter(PrefsFields.site_id == session.get('site_id')))

    def get_count_query(self):
        return (super().get_count_query()
                .join(PrefsFields, PrefsFields.field_id == PrefsFieldItems.field_id)
                .filter(PrefsFields.site_id == session.get('site_id')))

    column_list = ['prefs_id', 'field_id', 'fielditem']
    form_columns = ['field_id', 'fielditem']
    column_labels = {
        'prefs_id': 'ID',
        'field_id': 'Field ID',
        'fielditem': 'Field Item'
    }


class PredEquationsAdmin(GlobalSiteAdminMixin):
    column_list = ['test', 'source', 'parameter',
                  'stattype_range',
                   'equationtype', 'ethnicitycorrectiontype',
                   'ethnicity', 'gender', 'agegroup', 'age_lower',
                   'age_upper', 'ht_lower', 'ht_upper', 'wt_lower', 'wt_upper', 'lastedit', 'lasteditby']
    form_columns = ['equationid', 'test', 'testid', 'source', 'sourceid', 'parameter', 'parameterid',
                    'equation_mpv', 'equation_range', 'equation_zscore', 'stattype_range', 'stattype_rangeid',
                    'equationtype', 'equationtypeid', 'ethnicitycorrectiontype', 'ethnicitycorrectiontypeid',
                    'ethnicity', 'ethnicityid', 'gender', 'genderid', 'agegroup', 'agegroupid', 'age_lower',
                    'age_upper', 'ht_lower', 'ht_upper', 'wt_lower', 'wt_upper', 'lastedit', 'lasteditby']
    column_default_sort = 'id'
    column_filters = ['test', 'source', 'parameter', 'gender', 'agegroup']
    column_searchable_list = ['test', 'source', 'parameter', 'equation_mpv', 'equation_range', 'equation_zscore']


class PredGliLookupAdmin(GlobalSiteAdminMixin):
    column_list = ['age', 'fev1_lspline_male', 'fev1_mspline_male', 'fev1_sspline_male', 'fvc_lspline_male',
                   'fvc_mspline_male', 'fvc_sspline_male', 'fer_lspline_male', 'fer_mspline_male', 'fer_sspline_male',
                   'fef2575_lspline_male', 'fef2575_mspline_male', 'fef2575_sspline_male', 'fef75_lspline_male',
                   'fef75_mspline_male', 'fef75_sspline_male', 'fev1_lspline_female', 'fev1_mspline_female',
                   'fev1_sspline_female', 'fvc_lspline_female', 'fvc_mspline_female', 'fvc_sspline_female',
                   'fer_lspline_female', 'fer_mspline_female', 'fer_sspline_female', 'fef2575_lspline_female',
                   'fef2575_mspline_female', 'fef2575_sspline_female', 'fef75_lspline_female', 'fef75_mspline_female',
                   'fef75_sspline_female']
    form_columns = ['age', 'fev1_lspline_male', 'fev1_mspline_male', 'fev1_sspline_male', 'fvc_lspline_male',
                    'fvc_mspline_male', 'fvc_sspline_male', 'fer_lspline_male', 'fer_mspline_male', 'fer_sspline_male',
                    'fef2575_lspline_male', 'fef2575_mspline_male', 'fef2575_sspline_male', 'fef75_lspline_male',
                    'fef75_mspline_male', 'fef75_sspline_male', 'fev1_lspline_female', 'fev1_mspline_female',
                    'fev1_sspline_female', 'fvc_lspline_female', 'fvc_mspline_female', 'fvc_sspline_female',
                    'fer_lspline_female', 'fer_mspline_female', 'fer_sspline_female', 'fef2575_lspline_female',
                    'fef2575_mspline_female', 'fef2575_sspline_female', 'fef75_lspline_female', 'fef75_mspline_female',
                    'fef75_sspline_female']
    column_default_sort = 'age'
    column_filters = ['age']


class PredGliLvLookupAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'parameter', 'sex', 'age', 'mspline', 'sspline', 'lspline']
    form_columns = ['parameter', 'sex', 'age', 'mspline', 'sspline', 'lspline']
    column_default_sort = 'id'
    column_filters = ['parameter', 'sex', 'age']
    column_searchable_list = ['parameter', 'sex', 'age']


class PredGliTlcoLookupAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'parameter', 'sex', 'age', 'mspline', 'sspline', 'lspline']
    form_columns = ['parameter', 'sex', 'age', 'mspline', 'sspline', 'lspline']
    column_default_sort = 'id'
    column_filters = ['parameter', 'sex', 'age']
    column_searchable_list = ['parameter', 'sex', 'age']


class PredLmsCoeffAdmin(GlobalSiteAdminMixin):
    column_list = ['coefficientid', 'sourceid', 'parameter', 'gender', 'intercept_l', 'age_l', 'intercept_m',
                   'height_m', 'age_m', 'afram_m', 'neastasia_m', 'seastasia_m', 'othermixed_m', 'intercept_s',
                   'age_s', 'afram_s', 'neastasia_s', 'seastasia_s', 'othermixed_s']
    form_columns = ['sourceid', 'parameter', 'gender', 'intercept_l', 'age_l', 'intercept_m', 'height_m', 'age_m',
                    'afram_m', 'neastasia_m', 'seastasia_m', 'othermixed_m', 'intercept_s', 'age_s', 'afram_s',
                    'neastasia_s', 'seastasia_s', 'othermixed_s']
    column_default_sort = 'coefficientid'
    column_filters = ['sourceid', 'parameter', 'gender']
    column_searchable_list = ['parameter', 'gender']


class PredLmsEquationsAdmin(GlobalSiteAdminMixin):
    column_list = ['gliid', 'equationid', 'sourceid', 'l_equation', 'm_equation', 's_equation', 'lln_equation',
                   'zscore_equation']
    form_columns = ['equationid', 'sourceid', 'l_equation', 'm_equation', 's_equation', 'lln_equation',
                    'zscore_equation']
    column_default_sort = 'gliid'
    column_filters = ['equationid', 'sourceid']
    column_searchable_list = ['l_equation', 'm_equation', 's_equation', 'lln_equation', 'zscore_equation']


class PredNoncaucasianCorrectionsAdmin(GlobalSiteAdminMixin):
    column_list = ['correctionid', 'parameterid', 'parameter', 'factor', 'reference']
    form_columns = ['parameterid', 'parameter', 'factor', 'reference']
    column_default_sort = 'correctionid'
    column_filters = ['parameterid', 'parameter']
    column_searchable_list = ['parameter', 'reference']


class PredSourcexEthnicityAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'sourceid', 'ethnicityid']
    form_columns = ['sourceid', 'ethnicityid']
    column_default_sort = 'id'
    column_filters = ['sourceid', 'ethnicityid']


class PredSourcexGenderAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'sourceid', 'genderid']
    form_columns = ['sourceid', 'genderid']
    column_default_sort = 'id'
    column_filters = ['sourceid', 'genderid']


class PredSourcexParameterAdmin(GlobalSiteAdminMixin):
    column_list = ['sxpid', 'paramid', 'sourceid']
    form_columns = ['paramid', 'sourceid']
    column_default_sort = 'sxpid'
    column_filters = ['paramid', 'sourceid']


class PredSourcexTestAdmin(GlobalSiteAdminMixin):
    column_list = ['id', 'sourceid', 'testid']
    form_columns = ['sourceid', 'testid']
    column_default_sort = 'id'
    column_filters = ['sourceid', 'testid']
