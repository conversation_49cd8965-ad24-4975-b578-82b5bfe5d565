#!/bin/bash
set -xeuo pipefail

#We run this as 'source' so rely on the callers trapping of errors.

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

export RUN_HASURA_BACKGROUND=true
source ${SCRIPT_DIR}/run_hasura.sh

${SCRIPT_DIR}/run_update_hasura.sh

${SCRIPT_DIR}/wait_for_hasura_to_die.sh ${GRAPHQL_PID}