-- prefs_fields
WITH new_fields AS (
    INSERT INTO prefs_fields (site_id, fieldname, default_fielditem_id)
        SELECT s.id, pf.fieldname, pf.default_fielditem_id
        FROM prefs_fields pf
                 CROSS JOIN site s
        WHERE pf.site_id = 1
          AND s.id != 1
        RETURNING field_id AS new_field_id, fieldname, site_id),
     old_fields AS (SELECT field_id AS old_field_id, fieldname
                    FROM prefs_fields
                    WHERE site_id = 1)
SELECT o.old_field_id, n.new_field_id, n.site_id, n.fieldname
FROM old_fields o
         JOIN new_fields n ON o.fieldname = n.fieldname;

-- sidebar_modules
INSERT INTO sidebar_modules (site_id, "order", created, updated, title, url, icon, enabled)
SELECT s.id,
       m.order,
       m.created,
       m.updated,
       m.title,
       m.url,
       m.icon,
       m.enabled
FROM sidebar_modules m
         CROSS JOIN site s
WHERE m.site_id = 1
  AND s.id != 1;

-- prefs_pred_new
INSERT INTO prefs_pred_new (site_id,
                            equationid,
                            age_clipmethod,
                            age_clipmethodid,
                            ht_clipmethod,
                            ht_clipmethodid,
                            wt_clipmethod,
                            wt_clipmethodid,
                            active,
                            startdate,
                            enddate,
                            markfordeletion,
                            lastedit,
                            lasteditby)
SELECT s.id,
       p.equationid,
       p.age_clipmethod,
       p.age_clipmethodid,
       p.ht_clipmethod,
       p.ht_clipmethodid,
       p.wt_clipmethod,
       p.wt_clipmethodid,
       p.active,
       p.startdate,
       p.enddate,
       p.markfordeletion,
       p.lastedit,
       p.lasteditby
FROM prefs_pred_new p
         CROSS JOIN site s
WHERE p.site_id = 1
  AND s.id != 1;