DO
$$
    DECLARE
        config_row   RECORD;
    BEGIN
        -- Get the single row from site_config
        SELECT * INTO config_row FROM site_config LIMIT 1;

        IF config_row IS NULL THEN
            RAISE NOTICE 'No data found in site_config table';
            RETURN;
        END IF;

        -- Simple value migrations
        INSERT INTO site_settings (created, updated, name, description, value, site_id)
        VALUES (NOW(), NOW(), 'site_institution', 'Client/Hospital name', config_row.site_institution, 1),
               (NOW(), NOW(), 'site_state', 'Site state code', config_row.site_state, 1),
               (NOW(), NOW(), 'site_logo', 'S3 file reference for site logo',
                config_row.site_logo_resourcename, 1),
               (NOW(), NOW(), 'site_logo_showonreports', 'Show site logo on reports (Y/N)',
                CASE WHEN config_row.site_logo_showonreports THEN 'Y' ELSE 'N' END, 1),
               (NOW(), NOW(), 'pas_mode_local', 'Local PAS mode enabled',
                CASE WHEN config_row.pas_mode_local THEN 'Y' ELSE 'N' END, 1),
               (NOW(), NOW(), 'emr_mode_active', 'EMR mode active',
                CASE WHEN config_row.emr_mode_active THEN 'Y' ELSE 'N' END, 1),
               (NOW(), NOW(), 'healthservice_code_primary', 'Primary health service code/ID',
                config_row.healthservice_code_primary, 1),
               (NOW(), NOW(), 'db_files_repository_path', 'Database files repository path',
                config_row.db_files_repository_path, 1),
               (NOW(), NOW(), 'accreditation_logo_showonreports',
                'Show accreditation logo on reports (Y/N)',
                CASE WHEN config_row.accreditation_logo_showonreports THEN 'Y' ELSE 'N' END, 1),
               (NOW(), NOW(), 'accreditation_logo_resourcename',
                'S3 file reference for accreditation logo', config_row.accreditation_logo_resourcename, 1),
               (NOW(), NOW(), 'site_country', 'Site country', config_row.site_country, 1),
               (NOW(), NOW(), 'site_parameter_units', 'Parameter units (TRADITIONAL/SI)',
                config_row.site_parameter_units, 1),
               (NOW(), NOW(), 'autoreport_algorithm', 'Active autoreport algorithm version',
                config_row.autoreport_algorithm, 1),
               (NOW(), NOW(), 'cpet_pred_selection', 'CPET prediction selection method',
                config_row.cpet_pred_selection, 1);

        -- JSON value migrations for site logo positioning
        INSERT INTO site_settings (created, updated, name, description, value_json, site_id)
        VALUES (NOW(), NOW(), 'site_logo_positioning', 'Site logo positioning settings',
                json_build_object(
                        'width', config_row.site_logo_width,
                        'height', config_row.site_logo_height,
                        'left', config_row.site_logo_left,
                        'top', config_row.site_logo_top,
                        'textflow', config_row.site_logo_textflow
                ), 1);

        -- JSON value migrations for accreditation logo positioning
        INSERT INTO site_settings (created, updated, name, description, value_json, site_id)
        VALUES (NOW(), NOW(), 'accreditation_logo_positioning', 'Accreditation logo positioning settings',
                json_build_object(
                        'width', config_row.accreditation_logo_width,
                        'height', config_row.accreditation_logo_height,
                        'left', config_row.accreditation_logo_left,
                        'top', config_row.accreditation_logo_top
                ), 1);

        -- JSON value migrations for report options and preferences
        INSERT INTO site_settings (created, updated, name, description, value_json, site_id)
        VALUES (NOW(), NOW(), 'report_options', 'Report generation options and preferences',
                json_build_object(
                        'suppress_fer_pcmpv_value_rft_report', config_row.option_suppress_fer_pcmpv_value_rft_report,
                        'print_authorised_on_reports', config_row.option_printauthorisedonreports,
                        'print_authorised_suppress_no_data', config_row.option_printauthorisedonreports_suppressnodata,
                        'print_verified_on_reports', config_row.option_printverifiedonreports,
                        'print_verified_suppress_no_data', config_row.option_printverifiedonreports_suppressnodata,
                        'fer_calc_preats_ers2021', config_row.option_fer_calc_preats_ers2021,
                        'fer_calc_ats_ers2021', config_row.option_fer_calc_ats_ers2021,
                        'fer_calc_changeover_date', config_row.fer_calc_changeover_date
                ), 1);

        -- Drop the site_config table after successful migration
        DROP TABLE IF EXISTS site_config;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Error during migration: %', SQLERRM;
            RAISE;
    END
$$;