-- Remove redundant tables

DROP TABLE IF EXISTS fit_masks;
DROP TABLE IF EXISTS fit_modelXsize;
DROP TABLE IF EXISTS fit_modelXstyle;
DROP TABLE IF EXISTS fit_styleXmanufacturer;
DROP TABLE IF EXISTS fit_masks_sizes;
DROP TABLE IF EXISTS fit_masks_styles;
DROP TABLE IF EXISTS fit_masks_models;
DROP TABLE IF EXISTS fit_masks_manufacturers;

DROP TABLE IF EXISTS ganshorn_processed_visit;
DROP TABLE IF EXISTS integration_device;
DROP TABLE IF EXISTS integration_devices_config;
DROP TABLE IF EXISTS list_nationality_OLD;
DROP TABLE IF EXISTS psg_diagnostics;

DROP TABLE IF EXISTS r_fit_masks;
DROP TABLE IF EXISTS r_fit_masks_data;
DROP TABLE IF EXISTS r_fit_protocolsXexercises;
DROP TABLE IF EXISTS r_fit;
DROP TABLE IF EXISTS r_fit_protocols;

DROP TABLE IF EXISTS unit_conversion_factors;

-- Site specific tables
ALTER TABLE list_language ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;
ALTER TABLE list_nationality ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;

ALTER TABLE prefs_fields ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;
ALTER TABLE prefs_pred_new ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;

ALTER TABLE sidebar_modules ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;
ALTER TABLE doctors ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;

ALTER TABLE list_language
    ADD CONSTRAINT fk_list_language_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE list_nationality
    ADD CONSTRAINT fk_list_nationality_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE prefs_fields
    ADD CONSTRAINT fk_prefs_fields_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE prefs_pred_new
    ADD CONSTRAINT fk_prefs_pred_new_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE sidebar_modules
    ADD CONSTRAINT fk_sidebar_modules_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE doctors
    ADD CONSTRAINT fk_doctors_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

ALTER TABLE pas_pt
    ADD CONSTRAINT fk_pas_pt_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

DO $$
    DECLARE
        constraint_name_var TEXT;
    BEGIN
        -- Find the foreign key constraint name
        SELECT tc.constraint_name INTO constraint_name_var
        FROM information_schema.table_constraints tc
                 JOIN information_schema.key_column_usage kcu
                      ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'site_settings'
          AND tc.constraint_type = 'FOREIGN KEY'
          AND kcu.column_name = 'site_id'
        LIMIT 1;

        -- Drop the constraint if found
        IF constraint_name_var IS NOT NULL THEN
            EXECUTE 'ALTER TABLE site_settings DROP CONSTRAINT ' || constraint_name_var;
        END IF;
    END $$;

ALTER TABLE site_settings
    ADD CONSTRAINT fk_site_settings_site_id
        FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE;

-- Fix prefs_fielditems_prefs_id_seq
SELECT setval('prefs_fielditems_prefs_id_seq', (SELECT MAX(prefs_id) FROM prefs_fielditems));
SELECT setval('prefs_fields_field_id_seq', (SELECT MAX(field_id) FROM prefs_fields));
