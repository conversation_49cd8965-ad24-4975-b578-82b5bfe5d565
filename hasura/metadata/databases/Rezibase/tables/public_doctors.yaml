table:
  name: doctors
  schema: public
insert_permissions:
  - role: user
    permission:
      check:
        site_id:
          _eq: X-Hasura-Site-Id
      set:
        site_id: x-hasura-Site-Id
      columns:
        - hidden
        - is_referring
        - prevent_appearing_in_new_records
        - id
        - building
        - clinic_name
        - consulting_location
        - created_by
        - doctor_id
        - edi_address
        - email
        - ext
        - external_reference
        - fax
        - forename
        - gp_code
        - hpi_number
        - hpio_number
        - initials
        - job_description
        - last_modified_by
        - mobile
        - phone
        - postal_barcode
        - postal_barcode_sample
        - post_code
        - practice_code
        - practice_number
        - provider_number
        - record_source
        - salutation
        - secretary_line_1
        - secretary_line_2
        - specialty
        - state
        - street
        - suburb
        - surname
        - title
        - created_at
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - hidden
        - is_referring
        - prevent_appearing_in_new_records
        - id
        - building
        - clinic_name
        - consulting_location
        - created_by
        - doctor_id
        - edi_address
        - email
        - ext
        - external_reference
        - fax
        - forename
        - gp_code
        - hpi_number
        - hpio_number
        - initials
        - job_description
        - last_modified_by
        - mobile
        - phone
        - postal_barcode
        - postal_barcode_sample
        - post_code
        - practice_code
        - practice_number
        - provider_number
        - record_source
        - salutation
        - secretary_line_1
        - secretary_line_2
        - specialty
        - state
        - street
        - suburb
        - surname
        - title
        - created_at
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      allow_aggregations: true
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - hidden
        - is_referring
        - prevent_appearing_in_new_records
        - id
        - building
        - clinic_name
        - consulting_location
        - created_by
        - doctor_id
        - edi_address
        - email
        - ext
        - external_reference
        - fax
        - forename
        - gp_code
        - hpi_number
        - hpio_number
        - initials
        - job_description
        - last_modified_by
        - mobile
        - phone
        - postal_barcode
        - postal_barcode_sample
        - post_code
        - practice_code
        - practice_number
        - provider_number
        - record_source
        - salutation
        - secretary_line_1
        - secretary_line_2
        - specialty
        - state
        - street
        - suburb
        - surname
        - title
        - created_at
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      check: null
    comment: ""
