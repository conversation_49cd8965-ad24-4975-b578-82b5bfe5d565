table:
  name: ab_user
  schema: public
object_relationships:
  - name: lab
    using:
      foreign_key_constraint_on: selected_lab_id
array_relationships:
  - name: alarms
    using:
      foreign_key_constraint_on:
        column: closed_by_id
        table:
          name: alarms
          schema: public
  - name: roles_users
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: roles_users
          schema: public
  - name: session_data
    using:
      foreign_key_constraint_on:
        column: closed_by_id
        table:
          name: session_data
          schema: public
  - name: sessions
    using:
      foreign_key_constraint_on:
        column: performed_by_id
        table:
          name: sessions
          schema: public
  - name: user_site_controls
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: user_site_control
          schema: public
  - name: user_sites
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: user_sites
          schema: public
