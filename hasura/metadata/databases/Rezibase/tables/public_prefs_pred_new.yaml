table:
  name: prefs_pred_new
  schema: public
object_relationships:
  - name: equation
    using:
      manual_configuration:
        column_mapping:
          equationid: id
        insertion_order: null
        remote_table:
          name: pred_equations
          schema: public
  - name: pred_ref_clipmethod_age
    using:
      foreign_key_constraint_on: age_clipmethodid
  - name: pred_ref_clipmethod_ht
    using:
      foreign_key_constraint_on: ht_clipmethodid
  - name: pred_ref_clipmethod_wt
    using:
      foreign_key_constraint_on: wt_clipmethodid
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - active
        - age_clipmethod
        - age_clipmethodid
        - enddate
        - equationid
        - ht_clipmethod
        - ht_clipmethodid
        - lastedit
        - lasteditby
        - markfordeletion
        - startdate
        - wt_clipmethod
        - wt_clipmethodid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - active
        - age_clipmethod
        - ht_clipmethod
        - lasteditby
        - wt_clipmethod
        - enddate
        - startdate
        - age_clipmethodid
        - equationid
        - ht_clipmethodid
        - markfordeletion
        - prefid
        - wt_clipmethodid
        - lastedit
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - active
        - age_clipmethod
        - age_clipmethodid
        - enddate
        - equationid
        - ht_clipmethod
        - ht_clipmethodid
        - lastedit
        - lasteditby
        - markfordeletion
        - startdate
        - wt_clipmethod
        - wt_clipmethodid
      filter: {}
      check: null
    comment: ""
delete_permissions:
  - role: user
    permission:
      filter: {}
    comment: ""
