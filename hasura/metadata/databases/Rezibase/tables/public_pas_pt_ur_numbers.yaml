table:
  name: pas_pt_ur_numbers
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
computed_fields:
  - name: health_service
    definition:
      function:
        name: get_ur_health_service
        schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - create_by
        - create_date
        - created_inreslab_xx
        - patientid
        - ur
        - ur_hsid
        - ur_status
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - created_inreslab_xx
        - create_by
        - ur
        - ur_hsid
        - ur_status
        - patientid
        - ur_id
        - create_date
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - create_by
        - create_date
        - created_inreslab_xx
        - patientid
        - ur
        - ur_hsid
        - ur_status
      filter: {}
      check: null
    comment: ""
