table:
  name: pas_pt_names
  schema: public
object_relationships:
  - name: patient
    using:
      manual_configuration:
        column_mapping:
          patientid: patientid
        insertion_order: null
        remote_table:
          name: pas_pt
          schema: public
insert_permissions:
  - role: user
    permission:
      check:
        patient:
          site_id:
            _eq: X-Hasura-Site-Id
      columns:
        - firstname
        - middlename
        - name_type
        - patientid
        - surname
        - title
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - firstname
        - middlename
        - name_type
        - surname
        - title
        - nameid
        - patientid
      filter:
        patient:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - firstname
        - middlename
        - name_type
        - patientid
        - surname
        - title
      filter:
        patient:
          site_id:
            _eq: X-Hasura-Site-Id
      check:
        patient:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
delete_permissions:
  - role: user
    permission:
      filter:
        patient:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
