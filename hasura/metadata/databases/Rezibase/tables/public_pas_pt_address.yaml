table:
  name: pas_pt_address
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
insert_permissions:
  - role: user
    permission:
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      columns:
        - address_1
        - address_2
        - address_type_code
        - patientid
        - postcode
        - statename
        - suburb
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - address_1
        - address_2
        - address_type_code
        - postcode
        - statename
        - suburb
        - addressid
        - patientid
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - address_1
        - address_2
        - address_type_code
        - patientid
        - postcode
        - statename
        - suburb
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
