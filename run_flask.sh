#!/bin/bash
set -euo pipefail

if [ "$APP_TYPE" == "test" ]; then
	export SERVER_NAME=${SERVER_NAME:-"${HEROKU_APP_NAME}.herokuapp.com"}
	export HASURA_GRAPHQL_ENDPOINT=https://${SERVER_NAME}
	export HASURA_ADMIN_URL=${HASURA_GRAPHQL_ENDPOINT}
	export VITE_APP_HASURA_URL="${HASURA_GRAPHQL_ENDPOINT}/v1/graphql"
	echo "Review App, changed Hasura url: ${VITE_APP_HASURA_URL}"
else
	export HASURA_GRAPHQL_ENDPOINT=${HASURA_GRAPHQL_ENDPOINT:-https://${SERVER_NAME}}
	export HASURA_ADMIN_URL=${HASURA_ADMIN_URL:-$HASURA_GRAPHQL_ENDPOINT}

	export HASURA_ADMIN_URL=${HASURA_GRAPHQL_ENDPOINT}
	export VITE_APP_HASURA_URL="${HASURA_GRAPHQL_ENDPOINT}/v1/graphql"
fi

export PORT=${FLASK_PORT:-3010}
gunicorn -c gunicorn.conf.py