import {gql} from '@apollo/client';

export const createPatient = gql`
  mutation CreatePatient(
    $ur: String!
    $ur_hsid: String!
    $ur_status: String!
    $title: String
    $firstname: String!
    $surname: String!
    $middlename: String
    $dob: date
    $gender_code: String
    $gender_forrfts_code: String
    $email: String
    $phone_home: String
    $phone_mobile: String
    $phone_work: String
    $address_1: String
    $address_2: String
    $suburb: String
    $postcode: String
    $countryofbirth_code: String
    $preferredlanguage_code: String
    $aboriginalstatus_code: String
    $medicare_no: String
    $medicare_expirydate: String
    $death_indicator: String
    $death_date: String
    $race_forrfts_code: String
  ) {
    # Insert the patient record with nested inserts for related tables
    insert_pas_pt_one(
      object: {
        ur: $ur
        ur_hsid: $ur_hsid
        dob: $dob
        gender_code: $gender_code
        gender_forrfts_code: $gender_forrfts_code
        email: $email
        phone_home: $phone_home
        phone_mobile: $phone_mobile
        phone_work: $phone_work
        countryofbirth_code: $countryofbirth_code
        preferredlanguage_code: $preferredlanguage_code
        aboriginalstatus_code: $aboriginalstatus_code
        medicare_no: $medicare_no
        medicare_expirydate: $medicare_expirydate
        death_indicator: $death_indicator
        death_date: $death_date
        race_forrfts_code: $race_forrfts_code
        # Nested insert for patient names
        pas_pt_names: {
          data: {
            name_type: "primary"
            title: $title
            firstname: $firstname
            surname: $surname
            middlename: $middlename
          }
        }
        # Nested insert for patient addresses
        pas_pt_addresses: {
          data: {
            address_type_code: "primary"
            address_1: $address_1
            address_2: $address_2
            suburb: $suburb
            postcode: $postcode
          }
        }
        # Nested insert for patient UR numbers
        pas_pt_ur_numbers: {
          data: {
            ur: $ur
            ur_hsid: $ur_hsid
            ur_status: $ur_status
          }
        }
      }
    ) {
      patientid
      ur_id
      ur
      ur_hsid
    }
  }
`;



export const createPatientUrNumbers = gql`
  mutation CreatePatientUrNumbers(
    $objects: [pas_pt_ur_numbers_insert_input!]!
  ) {
    insert_pas_pt_ur_numbers(
      objects: $objects
    ) {
      affected_rows
      returning {
        ur_id
        patientid
      }
    }
  }
`;