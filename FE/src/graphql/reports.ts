import {graphql} from '@/graphql';


export const respiratoryLabQuery = graphql(`
  query GetRespiratoryLabData(
    $where: rft_routine_bool_exp!,
    $provWhere: prov_test_bool_exp!,
    $walkWhere: r_walktests_v1heavy_bool_exp!,
    $cpetWhere: r_cpet_bool_exp!,
    $sptWhere: r_spt_bool_exp!,
    $hastWhere: r_hast_bool_exp!
  ) {
    rft_routine(where: $where) {
      id: rftid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
    prov_test(where: $provWhere) {
      id: provid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
    r_walktests_v1heavy(where: $walkWhere) {
      id: walkid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
    r_cpet(where: $cpetWhere) {
      id: cpetid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
    r_spt(where: $sptWhere) {
      id: sptid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
    r_hast(where: $hastWhere) {
      id: hastid
      patientid
      sessionid
      testtime
      testtype
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      r_session {
        sessionid
        testdate
        report_copyto
        req_name
      }
    }
  }
`);




export const cpapClinicQuery = graphql(`
  query GetCPAPClinicData($where: cpap_visit_clinic_bool_exp!) {
    cpap_visit_clinic(where: $where) {
      id: cpap_visit_clinic_id
      patientid
      testdatetime: visit_datetime
      testtype: visit_type
      req_name: clinician
      report_copyto: report_to
      report_status
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
    }
  }
`);

export const sleepStudyQuery = graphql(`
  query GetSleepStudies($where: p_sleep_study_bool_exp!) {
    p_sleep_study(where: $where) {
      id: test_id
      patientid
      report_copyto: copy_to
      testdate: study_date
      req_name: requested_by
      report_status
      testtype: test_type
      scoring_assigned_to
      reporting_assigned_to
      pas_pt {
        ur
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
    }
  }
`);



export const getPersons = graphql(`
  query GetPersons {
    persons {
      id:personid
      email
      enabled
      firstname
      profession_category
      surname
      title
      user_name
    }
    
  }`)