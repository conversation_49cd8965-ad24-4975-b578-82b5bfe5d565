import { graphql } from 'gql.tada';

export const AllParameters = graphql(`
  query AllParameters {
    pred_ref_parameters {
      parameterid
      id
      longname
      description
      decimalplaces
      decimalplaces_si
      reporttypeid_notused
      testid
      units
      units_si
      valid_high
      valid_low
      valid_low_si
      valid_high_si
      units_convert_trad_to_si
    }
  }
`);

export const getHealthServices = graphql(`
  query GetHealthServices {
    list_healthservices {
      code
      description
    }
  }
`);
