import {useState} from 'react';
import {Navigate} from 'react-router';

import {observer} from 'mobx-react-lite';

import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';
import authStore from '@/store/auth.store.ts';

export const MFAChallenge = observer(() => {
  const [tokenType, setTokenType] = useState('totp');

  if (!authStore.requireMfa) {
    return <Navigate to="/" />;
  }

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex w-full flex-col items-center justify-center px-10">
        <div className="min-w-88">
          <div className="mb-8">
            <img
              src="/dark-logo.png"
              alt="Rezibase"
              className="absolute top-5 left-5 h-6"
            />
          </div>

          <h1 className="mb-3 text-center text-[32px] leading-6 font-bold text-neutral-900">
            Verify its you!
          </h1>
          <p className="mb-10 text-center text-gray-700">Please enter OTP from your authenticator app.</p>

          <Form
            action={`/api/user/mfa/verify/${tokenType}/`}
            method="POST"
            onSubmitSuccess={async (res) => {
              console.log(res);
              authStore.setAccessToken(res.data.access_token);
              // return navigate('/');
            }}
          >
            <div className="space-y-6">
              <FormRootErrors />

              <div>
                <TextFormField
                  name="token"
                  pattern={tokenType === 'backupCode' ? /[0-9a-z]{8}/i : /[0-9]{6}/i}
                  required
                >
                  <Label>{tokenType === 'backupCode' ? 'Backup Code' : 'OTP Code'}</Label>
                  <div className="relative">
                    <Input
                      size="lg"
                      autoComplete={tokenType === 'backupCode' ? undefined : 'one-time-code'}
                      placeholder={tokenType === 'backupCode' ? 'XXXX XXXX' : 'XXX XXX'}
                      className="peer react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>

                <div className="mt-1 flex justify-end">
                  <button
                    type="button"
                    onClick={() => setTokenType((t) => (t === 'backupCode' ? 'totp' : 'backupCode'))}
                    className="focus:outline-noe block w-max cursor-pointer text-right text-sm font-medium text-neutral-800 hover:underline"
                  >
                    {tokenType === 'totp' ? 'Use Backup Code' : 'Use OTP token'}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="h-10 w-full rounded-sm"
              >
                Submit
              </Button>
            </div>
          </Form>
        </div>
      </div>

      <div className="hidden w-full max-w-180 shrink-0 bg-white p-2 md:block md:w-[60%]">
        <div
          className="relative h-full w-full rounded-xl bg-cover bg-center"
          style={{
            backgroundImage: "url('/login.png')",
            backgroundPosition: 'right',
          }}
        >
          <div
            className="absolute inset-0 rounded-xl"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.4)', // 40% black overlay
            }}
          ></div>
        </div>
      </div>
    </div>
  );
});
