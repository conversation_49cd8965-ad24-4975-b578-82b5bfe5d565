import {useNavigate} from 'react-router';

import {TriangleAlert} from 'lucide-react';

import {Button} from '@/components/ui/button';
import {Card} from '@/components/ui/card';
import {useApiQuery} from '@/hooks/use-api-query.ts';
import authStore from '@/store/auth.store.ts';

export default function MFACodesPage() {
  const navigate = useNavigate();
  const {data} = useApiQuery('/api/user/mfa/static/' as any);

  return (
    <div className="flex min-h-screen w-full items-center bg-neutral-50">
      <div className="container mx-auto max-w-xl">
        <Card className="rounded-lg p-6 shadow-md">
          <div className="space-y-4">
            <div>
              <h1 className="text-lg font-semibold">Recovery codes</h1>
              <p className="mt-1 text-sm text-neutral-800">
                Recovery codes can be used to access your account in the event you lose access to your device
                and cannot receive two-factor authentication codes.
              </p>
            </div>

            <p className="text-sm text-neutral-800">
              Treat your recovery codes with the same level of attention as you would your password! We
              recommend saving them with a password manager such as&nbsp;
              <a
                className="underline"
                href="https://1password.com/"
                target="_blank"
                rel="noopener noreferrer"
              >
                1Password
              </a>
              , or&nbsp;
              <a
                className="underline"
                href="https://bitwarden.com/"
                target="_blank"
                rel="noopener noreferrer"
              >
                BitWarden
              </a>
              .
            </p>

            <div className="rounded-md bg-yellow-50 p-4">
              <div className="flex">
                <div className="flex shrink-0 items-start">
                  <TriangleAlert
                    className="h-5 w-5 text-yellow-400"
                    aria-hidden="true"
                  />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-yellow-800">Put these in a safe spot.</p>
                  <p className="text-sm text-yellow-700">
                    If you lose your device and don’t have the recovery codes you will lose access to your
                    account.
                  </p>
                </div>
              </div>
            </div>

            <ul className="grid grid-cols-2 gap-4 font-mono text-lg uppercase">
              {data?.backup_codes?.map((code: string) => (
                <li
                  className="!my-0 space-x-1.5 text-center"
                  key={code}
                >
                  <span>{code.slice(0, 4)}</span>
                  <span>{code.slice(4)}</span>
                </li>
              ))}
            </ul>

            <div className="mt-6">
              <Button className="w-full" onPress={async () => {
                await authStore.validateTokenOnMount();
                return navigate('/');
              }}>I've saved the backup codes.</Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
