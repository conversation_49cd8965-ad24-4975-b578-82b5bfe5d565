import clsx from 'clsx';
import {ComponentProps} from 'react';
import {useQueryState} from 'nuqs';

import {Ta<PERSON>, Ta<PERSON>List, TabsTrigger} from '@/components/ui/tabs';
import PatientsTests from '@/views/patients/components/PatientsTests.tsx';
import ProfileTabs from "@/views/patients/components/ProfileTabs.tsx";

const LabsConfig = [
  {
    lab: 'All',
  },
  {
    lab: 'respiratory laboratory',
  },
  {lab: 'sleep laboratory'},
  {
    lab: 'CPAP Clinic',
  },
];
export type Lab =
  | 'All'
  | 'respiratory laboratory'
  | 'sleep laboratory'
  | 'CPAP Clinic';

function LabTabs(props: ComponentProps<typeof Tabs>) {
  const [selectedTab, setSelectedTab] = useQueryState('lab', {defaultValue: 'All'})

  return (
    <div className="w-0 flex-1">
      <div className="flex items-center sticky top-10 z-20 mb-6 bg-neutral-50 pt-1">
        <Tabs
          className="w-full bg-neutral-50 pt-3"
          value={selectedTab}
          onValueChange={setSelectedTab}
          {...props}
        >
          <TabsList className="flex w-fit justify-start rounded-none border-b bg-neutral-50 p-0">
            {LabsConfig?.map(({lab}) => (
              <TabsTrigger
                key={lab}
                className={clsx(
                  'data-[state=active]:after:bg-brand-500 after:absolute after:inset-x-0 after:-bottom-0.5 after:h-0.5',
                  'relative flex cursor-pointer items-center gap-x-2 p-3 pt-0 text-neutral-700 capitalize data-[state=active]:rounded-none data-[state=active]:bg-neutral-50 data-[state=active]:shadow-none',
                  'data-[state=active]:text-brand-600'
                )}
                value={lab}
              >
                {lab}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
        <ProfileTabs />
      </div>
      <PatientsTests lab={selectedTab as Lab} />
    </div>
  );
}

export default LabTabs;