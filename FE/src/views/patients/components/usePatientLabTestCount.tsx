import {useQuery} from '@apollo/client';

import {getPatientsAllLabCounts} from '@/graphql/patients.ts';

export const usePatientLabTestCount = (patientId: number) => {
  const {data, loading, error} = useQuery(getPatientsAllLabCounts, {
    variables: {patientId},
    skip: !patientId,
  });

  let totalCount = 0;

  if (!loading && !error && data) {
    const cpapClinicCount = data.cpap_visit_clinic_aggregate?.aggregate?.count || 0;
    const psgFilesCount = data.r_psg_imported_files_aggregate?.aggregate?.count || 0;
    const sleepStudyCount = data.p_sleep_study_aggregate?.aggregate?.count || 0;
    const rftRoutineCount = data.rft_routine_aggregate?.aggregate?.count || 0;
    const provTestCount = data.prov_test_aggregate?.aggregate?.count || 0;
    const walkTestsCount = data.r_walktests_v1heavy_aggregate?.aggregate?.count || 0;
    const cpetCount = data.r_cpet_aggregate?.aggregate?.count || 0;
    const sptCount = data.r_spt_aggregate?.aggregate?.count || 0;
    const hastCount = data.r_hast_aggregate?.aggregate?.count || 0;

    totalCount =
      cpapClinicCount +
      psgFilesCount +
      sleepStudyCount +
      rftRoutineCount +
      provTestCount +
      walkTestsCount +
      cpetCount +
      sptCount +
      hastCount;
  }

  return {
    loading,
    count: loading ? null : totalCount,
    error: error || null,
  };
};
