import {useState} from 'react';
import {
  Cell,
  Column,
  Input,
  Menu,
  MenuItem,
  MenuTrigger,
  Popover,
  Row,
  SubmenuTrigger,
  Table,
  TableHeader,
} from 'react-aria-components';
import {Link, useLocation, useParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {format, parse} from 'date-fns';
import {ChevronRight} from 'lucide-react';

import EditIcon from '@/assets/iconly/Edit.svg?react';
import EyeIcon from '@/assets/iconly/Eye.svg?react';
import {ReportStatusChip} from '@/components/ReportStatusChip.tsx';
import {LoadableTableBody} from '@/components/Table.tsx';
import PlusIcon from '@/components/icons/PlusIconly.tsx';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {But<PERSON>} from '@/components/ui/button';
import {
  getPatientsCPAPClinicData,
  getPatientsDetailData,
  getPatientsRSessionData,
  getPatientsRespiratoryLabData,
  getPatientsSleepLabData,
  getSleepStudyLabListTypes,
} from '@/graphql/patients';
import {getPreferenceField} from '@/graphql/preferences.ts';
import AddEditTestSession from '@/views/patients/components/add-edit-test-session.tsx';
import {Lab} from '@/views/patients/patient-detail/LabTabs/LabTabs.tsx';

function PatientsTests({lab}: {lab: Lab}) {
  const {patientId} = useParams();
  const [search, setSearch] = useState('');
  const [testType, setTestType] = useState('');
  const [debouncedSearch] = useDebouncedValue(search, 300);
  const [, setIsOpen] = useDialogState('add-test-session');
  const location = useLocation();

  const testTypeOptions = [
    {id: 'RFT', label: 'RFT'},
    {
      id: 'BHR',
      label: 'BHR',
      submenu: [
        {id: 'BHR-MCT', label: 'BHR-MCT'},
        {id: 'BHR-EVH', label: 'BHR-EVH'},
      ],
    },
    {id: 'CPET', label: 'CPET'},
    {id: '6MWT', label: '6MWT'},
    {id: 'HAST', label: 'HAST'},
    {
      id: 'SPT',
      label: 'SPT',
      submenu: [
        {id: 'SPT-STD', label: 'SPT-STD'},
        {id: 'SPT-CUST', label: 'SPT-CUST'},
      ],
    },
    {id: 'PSG', label: 'PSG'},
  ];

  const handleTestSelection = (test: string) => {
    setTestType(test);
    setIsOpen(true);
  };

  const {data: Rsessions, loading: rSessionsLoading} = useQuery(getPatientsRSessionData, {
    variables: {patientId: parseInt(patientId ?? '')},
  });

  const {data: RespiratoryLabData, loading: respiratoryLabTestsLoading} = useQuery(
    getPatientsRespiratoryLabData,
    {
      variables: {
        testType: `%${debouncedSearch}%`,
        patientId: parseInt(patientId ?? ''),
      },
    }
  );

  const {data: CPAPClinicData, loading: cpapClinicTestsLoading} = useQuery(getPatientsCPAPClinicData, {
    variables: {
      testType: `%${debouncedSearch}%`,
      patientId: parseInt(patientId ?? ''),
    },
  });

  const {data: SleepLabData, loading: sleepLabDataLoading} = useQuery(getPatientsSleepLabData, {
    variables: {
      testType: `%${debouncedSearch}%`,
      patientId: parseInt(patientId ?? ''),
    },
  });

  const {data: SleepStudyLabListTypes, loading: sleepStudyLabTestsLoading} = useQuery(
    getSleepStudyLabListTypes,
    {
      variables: {patientId: parseInt(patientId ?? '')},
    }
  );

  const rSessionsIdToDateMap = Rsessions?.r_sessions?.reduce(
    (acc, item) => {
      acc[item.sessionid] = item.testdate;
      return acc;
    },
    {} as Record<string, any>
  );

  const patientTests = Object.values(RespiratoryLabData ?? {}).flat();

  const respiratoryLabData = patientTests?.map((test) => ({
    ...test,
    testdate: (rSessionsIdToDateMap as Record<number, string>)?.[(test as any)?.sessionid],
  }));

  let sleepStudy = Object.values(SleepLabData?.p_sleep_study ?? {});
  const rPSGImportedFiles = Object.values(SleepLabData?.r_psg_imported_files ?? {});

  const sleepStudyListMap = SleepStudyLabListTypes?.list_psg_types?.reduce(
    (acc, item) => {
      if (item.code) {
        acc[item.code] = item.description;
      }
      return acc;
    },
    {} as Record<string, any>
  );

  sleepStudy = sleepStudy?.map((test) => ({
    ...test,
    testtype: test.test_type ? sleepStudyListMap?.[test.test_type] : null,
  }));

  const {data: RMOAddresses} = useQuery(getPreferenceField, {
    variables: {fieldName: 'ReferringMO addresses'},
  });
  const {data: smokingHistory} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Smoking_history'},
  });

  const {data: admisssionStatus} = useQuery(getPreferenceField, {
    variables: {fieldName: 'AdmissionStatus'},
  });

  const {data: reportCopyTo} = useQuery(getPreferenceField, {
    variables: {fieldName: 'ReportCopyTo'},
  });

  const {data: patientData, loading: isPatientsDataLoading} = useQuery(getPatientsDetailData, {
    variables: {patientId: parseInt(patientId ?? '')},
  });

  const sleepLabData = [...sleepStudy, ...rPSGImportedFiles];

  function normalizeData(cpapVisitData: any[]) {
    return cpapVisitData.map((item) => {
      if (item.__typename === 'cpap_visit_clinic') {
        const normalizedItem = {...item};
        if (normalizedItem.testdate && typeof normalizedItem.testdate === 'string') {
          normalizedItem.testdate = normalizedItem.testdate.split('T')[0];
        }
        if (normalizedItem.testtime && typeof normalizedItem.testtime === 'string') {
          const timePart = normalizedItem.testtime.split('T')[1];
          normalizedItem.testtime = timePart ? timePart.substring(0, 8) : null;
        }
        if (normalizedItem.report_status === '' || normalizedItem.report_status === null || normalizedItem.report_status === undefined) {
          normalizedItem.report_status = 'Unreported';
        }

        return normalizedItem;
      }

      return item;
    });
  }

  const labData = {
    All: normalizeData([
      ...respiratoryLabData,
      ...sleepLabData,
      ...(CPAPClinicData?.cpap_visit_clinic ?? []),
    ]),
    'respiratory laboratory': respiratoryLabData,
    'sleep laboratory': sleepLabData,
    'CPAP Clinic': CPAPClinicData?.cpap_visit_clinic ?? [],
  };

  const actionColumn = {
    id: 'action',
    name: '',
    cellProps: {className: 'react-aria-Cell w-24'},
    render: (item: any) => (
      <div className="flex items-center justify-end">
        {item.__typename === 'rft_routine' && (
          <>
            {!(item.report_status as string)?.includes('Completed') && (
              <Link
                to={`${location.pathname.replace(/\/$/, '')}/rft/${item.id}?edit=true`}
                className="p-2.5 text-neutral-600 focus:outline-none -my-2"
                onClick={(e) => e.stopPropagation()}
              >
                <EditIcon
                  strokeWidth={1.5}
                  className="size-5"
                />
              </Link>
            )}
            <Link
              to={`${location.pathname.replace(/\/$/, '')}/rft/${item.id}?edit=false`}
              className="p-2.5 text-neutral-600 focus:outline-none -my-2"
              onClick={(e) => e.stopPropagation()}
            >
              <EyeIcon
                strokeWidth={1.5}
                className="size-5"
              />
            </Link>
          </>
        )}
      </div>
    ),
  };

  const columns =
    lab === 'CPAP Clinic'
      ? [
          {
            name: 'Date',
            id: 'testdate',
            props: {allowSorting: true},
            render: (item: any) => {
              let formattedDate;
              if (item.testdate) {
                formattedDate = format(new Date(item.testdate), 'dd MMM yyyy, HH:mm');
              }
              return <div>{formattedDate}</div>;
            },
          },
          {name: 'Test Type', id: 'testtype', props: {isRowHeader: true}},
          {...actionColumn},
        ]
      : [
          {
            name: 'Date',
            id: 'testtime',
            props: {allowSorting: true},
            render: (item: any) => {
              let dateTime;
              let formatted;

              if (item.testdate && item.testtime) {
                dateTime = parse(`${item.testdate} ${item.testtime}`, 'yyyy-MM-dd HH:mm:ss', new Date());
                formatted = format(dateTime, 'dd MMM yyyy, HH:mm');
              } else if (item.testdate) {
                dateTime = parse(`${item.testdate}`, 'yyyy-MM-dd', new Date());
                formatted = format(dateTime, 'dd MMM yyyy');
              }
              return <div>{formatted}</div>;
            },
          },
          {name: 'Test type', id: 'testtype', props: {isRowHeader: true}},
          {
            name: 'Report Status',
            id: 'report_status',
            render: (item: any) =>
              item.report_status === '-' ? (
                <div className="pl-3">{item.report_status}</div>
              ) : (
                <ReportStatusChip
                  className="w-fit"
                  reportStatus={item.report_status || 'Unreported'}
                />
              ),
          },
          {...actionColumn},
        ];

  const isLoading =
    rSessionsLoading ||
    respiratoryLabTestsLoading ||
    sleepLabDataLoading ||
    sleepStudyLabTestsLoading ||
    cpapClinicTestsLoading;

  function sortLabData(data: any[]) {
    if (!data || data.length === 0) return [];
    return [...data].sort((a, b) => {
      const dateA = new Date(a.testdate + (a.testtime ? 'T' + a.testtime : 'T00:00:00'));
      const dateB = new Date(b.testdate + (b.testtime ? 'T' + b.testtime : 'T00:00:00'));
      return dateB.getTime() - dateA.getTime();
    });
  }

  return (
    <div className="px-px">
      <div className="mb-4 flex items-center gap-x-4">
        <div className="relative w-90">
          <Input
            placeholder={`Search ${lab === 'CPAP Clinic' ? 'visit' : 'test'} type`}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-gray-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <MenuTrigger>
          <Button className="react-aria-Button ml-auto h-8 shrink-0 rounded-sm font-semibold">
            <PlusIcon
              width={16}
              height={16}
            />
            Add Test
          </Button>
          <Popover
            className="react-aria-Popover min-w-34"
            placement="bottom end"
          >
            <Menu onAction={(key) => handleTestSelection(key as unknown as string)}>
              {testTypeOptions.map((test) =>
                test.submenu ? (
                  <SubmenuTrigger key={test.id}>
                    <MenuItem
                      id={test.id}
                      isDisabled={test.label !== 'RFT'}
                      className="react-aria-MenuItem flex h-8 w-full items-center justify-between rounded-sm pr-1 pl-2 text-neutral-800 hover:bg-neutral-100 focus:outline-none data-focused:bg-neutral-100 data-[disabled]:text-neutral-300"
                    >
                      <span className="text-sm">{test.label}</span>
                      <ChevronRight className="ml-2 h-4 w-4 text-gray-400 group-data-[focused]:text-blue-700" />
                    </MenuItem>
                    <Popover>
                      <Menu
                        className="react-aria-Menu max-h-60 overflow-y-auto p-1 focus:outline-none"
                        onAction={(key) => handleTestSelection(key as unknown as string)}
                      >
                        {test.submenu.map((subTest) => (
                          <MenuItem
                            key={subTest.id}
                            id={subTest.id}
                            isDisabled={test.label !== 'RFT'}
                            className="react-aria-MenuItem flex h-8 items-center gap-x-3 rounded-sm px-2 text-sm text-neutral-800 hover:bg-neutral-100 focus:outline-none data-focused:bg-neutral-100"
                          >
                            {subTest.label}
                          </MenuItem>
                        ))}
                      </Menu>
                    </Popover>
                  </SubmenuTrigger>
                ) : (
                  <MenuItem
                    key={test.id}
                    id={test.id}
                    className="react-aria-MenuItem flex h-8 items-center gap-x-3 rounded-sm px-2 text-sm text-neutral-800 hover:bg-neutral-100 focus:outline-none data-focused:bg-neutral-100 data-[disabled]:text-neutral-300"
                    isDisabled={test.label !== 'RFT'}
                  >
                    {test.label}
                  </MenuItem>
                )
              )}
            </Menu>
          </Popover>
        </MenuTrigger>
      </div>

      <Table aria-label="Patient List">
        <TableHeader columns={columns}>
          {(column) => <Column {...column.props}>{column.name}</Column>}
        </TableHeader>

        <LoadableTableBody
          items={sortLabData(labData[lab]) as any[]}
          emptyTitle="No test found"
          emptyDescription="Try refining your search or add a new test"
          isLoading={isLoading}
          columnCount={columns.length}
        >
          {(item) => (
            <Row
              id={item.__typename + item.id}
              columns={columns}
              href={
                item.__typename === 'rft_routine'
                  ? `${location.pathname.replace(/\/$/, '')}/rft/${item.id}`
                  : undefined
              }
            >
              {(column) => (
                <Cell {...((column as any).cellProps ?? {})}>
                  {column?.render ? column.render(item) : (item as any)[column.id]}
                </Cell>
              )}
            </Row>
          )}
        </LoadableTableBody>
      </Table>

      {!isPatientsDataLoading && patientData && (
        <AddEditTestSession
          type={testType}
          referralMOAddresses={RMOAddresses?.prefs_fields[0].prefs_fielditems ?? []}
          smokingHistory={smokingHistory?.prefs_fields[0].prefs_fielditems ?? []}
          admissionsStatus={admisssionStatus?.prefs_fields[0].prefs_fielditems ?? []}
          reportCopyTo={reportCopyTo?.prefs_fields[0].prefs_fielditems ?? []}
          patientDetail={patientData}
        />
      )}
    </div>
  );
}

export default PatientsTests;
