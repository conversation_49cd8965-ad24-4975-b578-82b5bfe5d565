import {useEffect} from 'react';
import {Cell, Column, Input, Row, Table, TableHeader} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {parseAsString, useQueryState} from 'nuqs';

import {LoadableTableBody} from '@/components/Table.tsx';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {Button} from '@/components/ui/button';
import {getHealthServices} from '@/graphql/common';
import {getDoctorsData} from '@/graphql/contacts';
import {getPreferenceField} from '@/graphql/preferences';
import AddContactDialog from '@/views/contacts/components/add-contact';
import EditContact from '@/views/contacts/components/edit-contact';

function Contacts() {
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  const [selectedDoctorId, setSelectedDoctorId] = useQueryState('doctorId');
  const {data, previousData, loading } = useQuery(getDoctorsData, {
    variables: {
      nameFilter: debouncedSearch ? `%${debouncedSearch}%` : '%',
    },
  });

  const [, setIsOpen] = useDialogState('add-contact');
  const [, setEditContact] = useDialogState('edit-contact');
  const {data: healthServices} = useQuery(getHealthServices);

  const {data: titles} = useQuery(getPreferenceField, {variables: {fieldName: 'Titles'}});

  useEffect(() => {
    setEditContact(true);
  }, [selectedDoctorId]);

  return (
    <div className="flex h-full flex-col gap-5">
      <div className="flex w-full items-center justify-between">
        <div className="relative w-90">
          <Input
            placeholder="Search Contacts"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <Button
          className="react-aria-Button ml-auto h-8 rounded-sm"
          onPress={() => setIsOpen(true)}
        >
          Add Contact
        </Button>
      </div>
      <Table
        aria-label="Contacts List"
        className="react-aria-Table has-[.table-empty]:grow"
      >
        <TableHeader>
          <Column isRowHeader>NAME</Column>
          <Column>Doctor ID</Column>
          <Column>MOBILE#</Column>
          <Column>Email</Column>
          <Column>Clinic</Column>
        </TableHeader>

        <LoadableTableBody
          emptyTitle="No contacts found"
          emptyDescription="Try refining your search or add a new contact"
          items={(loading ? previousData?.doctors : data?.doctors) ?? []}
          isLoading={loading}
          columnCount={5}
        >
          {(doctor) => {
            const fullName = [doctor.surname, doctor.forename].filter(Boolean).join(', ').toLowerCase();

            return (
              <Row
                key={doctor.id}
                onAction={() => {
                  setSelectedDoctorId(doctor.id as unknown as string);
                  setEditContact(true);
                }}
              >
                <Cell className="react-aria-Cell text-sm text-neutral-900 capitalize">
                  {doctor.title ? `${doctor.title.toLowerCase()}. ${fullName}` : fullName}
                </Cell>
                <Cell>{doctor.doctor_id}</Cell>
                <Cell>{doctor.mobile}</Cell>
                <Cell>{doctor.email}</Cell>
                <Cell>{doctor.clinic_name}</Cell>
              </Row>
            );
          }}
        </LoadableTableBody>
      </Table>
      <AddContactDialog
        healthServices={healthServices?.list_healthservices ?? []}
        titles={titles?.prefs_fields[0]?.prefs_fielditems ?? []}
      />


        <EditContact doctor={data?.doctors?.find((d) => d.id === Number(selectedDoctorId))} />
      
    </div>
  );
}

export default Contacts;
