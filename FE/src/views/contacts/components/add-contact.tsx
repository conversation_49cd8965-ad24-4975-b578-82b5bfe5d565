import {
  <PERSON><PERSON>,
  <PERSON>ing,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';

import {useMutation} from '@apollo/client';
import {ChevronDown, X} from 'lucide-react';
import {useQueryState} from 'nuqs';

import {QueryResultType} from '@/@types/graphql.tada';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Form, FormField, FormRootErrors, TextFormField} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/styled/FieldError';
import {getHealthServices} from '@/graphql/common';
import {addDoctorMutation, getDoctorsData} from '@/graphql/contacts';
import {getPreferenceField} from '@/graphql/preferences';

function AddContact({
  healthServices,
  titles,
}: {
  healthServices: QueryResultType<typeof getHealthServices>['list_healthservices'];
  titles: QueryResultType<typeof getPreferenceField>['prefs_fields'][0]['prefs_fielditems'];
}) {
  const [_, setSelectedDoctorId] = useQueryState('doctorId');
  const [isOpen, setIsOpen] = useDialogState('add-contact');

  const [addDoctor, {loading}] = useMutation(addDoctorMutation, {
    refetchQueries: [getDoctorsData],
  });

  const handleSubmit = async (data: any) => {
    if (Object.values(data).filter(Boolean).length === 0) return;
    try {
      const result = await addDoctor({
        variables: {
          doctor: {
            title: data.title || null,
            forename: data.firstname || null,
            surname: data.surname || null,
            provider_number: data.provider_number || null,
            building: data.address_1 || null,
            street: data.address_2 || null,
            suburb: data.suburb || null,
            post_code: data.postcode || null,
            phone: data.phone || null,
            fax: data.fax || null,
            mobile: data.phone || null,
            email: data.email || null,
          },
        },
      });
      setSelectedDoctorId(String(result.data?.insert_doctors_one?.id));
      setIsOpen(false);
    } catch (e) {
      console.log('error', e);
    }
  };

  return (
    <>
      <Modal
        isDismissable
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        className="react-aria-Modal max-w-xl overflow-y-auto rounded-md p-0"
      >
        <Dialog>
          <Form onSubmit={handleSubmit}>
            <FormRootErrors />

            <div className="flex items-center justify-between border-b border-neutral-200 px-6 py-4 text-neutral-800">
              <Heading
                className="text-sm font-semibold"
                slot="title"
              >
                Add Contact
              </Heading>
              <div
                onClick={() => setIsOpen(false)}
                className="cursor-pointer"
              >
                <X className="h-4 w-4" />
              </div>
            </div>

            <div className="space-y-5 px-6 pt-8">
              <div className="flex gap-3">
                <FormField name="title">
                  <FieldError />
                  <Select
                    className="react-aria-Select w-1/5"
                    placeholder="Select Title"
                  >
                    <Label className="text-xs font-semibold text-neutral-700">Title</Label>
                    <RACButton className="react-aria-Button w-full rounded-sm">
                      <SelectValue className="react-aria-SelectValue w-full text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox items={titles}>
                        {(item) => (
                          <ListBoxItem
                            id={item.fielditem?.toString()}
                            textValue={item.fielditem?.toString()}
                          >
                            <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                              {item.fielditem}
                            </div>
                          </ListBoxItem>
                        )}
                      </ListBox>
                    </Popover>
                  </Select>
                </FormField>

                <TextFormField
                  name="firstname"
                  className="w-2/5"
                >
                  <Label className="text-xs font-semibold text-neutral-700">First Name</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="First name"
                  />
                  <FieldError />
                </TextFormField>

                <TextFormField
                  name="surname"
                  className="w-2/5"
                >
                  <Label className="text-xs font-semibold text-neutral-700">Surname</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="Surname"
                  />
                  <FieldError />
                </TextFormField>
              </div>

              {/* Provider Number */}
              <TextFormField name="provider_number">
                <Label className="text-xs font-semibold text-neutral-700">Provider Number</Label>
                <Input
                  className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                  placeholder="Enter provider number"
                />
                <FieldError />
              </TextFormField>

              {/* Address Fields */}
              <TextFormField name="address_1">
                <Label className="text-xs font-semibold text-neutral-700">Building</Label>
                <Input
                  className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                  placeholder="Enter building"
                />
                <FieldError />
              </TextFormField>

              <TextFormField name="address_2">
                <Label className="text-xs font-semibold text-neutral-700">Street</Label>
                <Input
                  className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                  placeholder="Enter street"
                />
                <FieldError />
              </TextFormField>

              <div className="flex gap-3">
                <TextFormField
                  name="suburb"
                  className="w-full"
                >
                  <Label className="text-xs font-semibold text-neutral-700">Suburb</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="Enter suburb"
                  />
                  <FieldError />
                </TextFormField>

                <TextFormField
                  name="postcode"
                  className="w-full"
                >
                  <Label className="text-xs font-semibold text-neutral-700">Postcode</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="Enter postcode"
                  />
                  <FieldError />
                </TextFormField>
              </div>

              <div className="flex gap-3">
                <TextFormField
                  name="phone"
                  className="w-full"
                >
                  <Label className="text-xs font-semibold text-neutral-700">Phone</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="Enter phone number"
                  />
                  <FieldError />
                </TextFormField>

                <TextFormField
                  name="fax"
                  className="w-full"
                >
                  <Label className="text-xs font-semibold text-neutral-700">Fax</Label>
                  <Input
                    className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                    placeholder="Enter fax number"
                  />
                  <FieldError />
                </TextFormField>
              </div>

              <TextFormField name="email">
                <Label className="text-xs font-semibold text-neutral-700">Email</Label>
                <Input
                  className="ring-brand2-400/30 focus-visible:!border-brand2-500 w-full rounded-sm !border !border-neutral-300 bg-white px-4 py-2 text-neutral-900 outline-none placeholder:text-sm placeholder:text-neutral-600 focus-visible:ring-3"
                  placeholder="Enter email address"
                />
                <FieldError />
              </TextFormField>

              <FormField name="health_service">
                <Select
                  className="react-aria-Select"
                  placeholder="Select Health service "
                >
                  <Label className="text-xs font-semibold text-neutral-700">Health service</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      {healthServices.map((item) => (
                        <ListBoxItem
                          id={Number(item.code)}
                          key={item.code}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.description}
                          </div>
                        </ListBoxItem>
                      ))}
                    </ListBox>
                  </Popover>
                  <FieldError />
                </Select>
              </FormField>
            </div>

            <div className="flex items-center gap-x-3 px-6 pt-4 pb-6">
              <Button
                className="text-brand-500 hover:!text-brand-500 w-full rounded-sm font-semibold"
                variant="outlined"
                type="button"
                onPress={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="h-10 w-full rounded-sm font-semibold"
                isDisabled={loading}
              >
                {loading ? 'Adding...' : 'Add'}
              </Button>
            </div>
          </Form>
        </Dialog>
      </Modal>
    </>
  );
}

export default AddContact;
