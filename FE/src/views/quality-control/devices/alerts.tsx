import clsx from 'clsx';
import {useMemo, useRef} from 'react';
import {
  Label,
  ListBox,
  ListBoxItem,
  Popover,
  Select,
  Button as SelectButton,
  SelectValue,
} from 'react-aria-components';
import {useParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {Table, createColumnHelper} from '@tanstack/react-table';
import {intlFormat} from 'date-fns';
import {Check, ChevronDown, ScanSearch} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import DataTable from '@/components/datatable/DataTable.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip';
import {allAlarmsQuery} from '@/graphql/qc.ts';
import {useApiQuery} from '@/hooks/use-api-query.ts';
import ExpandedRow from '@/views/quality-control/components/device-summary/ExpandedRow.tsx';
import authStore from "@/store/auth.store.ts";

type AlarmsQueryResult = ReturnType<
  Exclude<(typeof allAlarmsQuery)['__apiType'], undefined>
>['alarms'][number];

const columnHelper = createColumnHelper<AlarmsQueryResult>();

export default function Alerts() {
  const {controlEquipmentId} = useParams();

  const tableRef = useRef<Table<any> | null>(null);

  const {data, loading: fetching} = useQuery(allAlarmsQuery, {
    variables: {controlMethodEquipmentId: +(controlEquipmentId ?? 0)},
  });

  const {data: parameters, isLoading: isParametersLoading} = useApiQuery(
    Paths.QC_PARAMETERS,
    {},
    {control_methods_equipments_id: controlEquipmentId}
  );

  const {data: sessions, isLoading: isSessionsLoading} = useApiQuery(
    Paths.QC_SESSIONS,
    {},
    {control_method_equipment_id: controlEquipmentId}
  );

  const {data: alarms, isLoading: isAlarmsLoading} = useApiQuery(
    Paths.QC_ALARMS,
    {},
    {
      control_method_equipment_id: controlEquipmentId?.toString(),
      site_id: authStore.tokenSiteId,
    }
  );

  const {data: controlRules, isLoading: isControlRulesLoading} = useApiQuery(
    Paths.QC_CONTROL_RULES,
    {},
    {
      control_method_equipment_id: controlEquipmentId,
    }
  );

  const {data: users} = useApiQuery(Paths.QC_USERS);

  const isLoading = isAlarmsLoading || isSessionsLoading || isControlRulesLoading || isParametersLoading;

  const columns = useMemo(
    () => [
      columnHelper.accessor((row) => new Date(row.session_datum.session.session_time as string), {
        id: 'created',
        header: 'Session Time',
        sortingFn: 'datetime',
        meta: {
          type: 'date',
        },
        cell: ({getValue}) => (
          <span className="text-neutral-800">
            {intlFormat(
              getValue(),
              {
                dateStyle: 'medium',
                timeStyle: 'short',
                hour12: false,
              },
              {locale: 'en-GB'}
            )}
          </span>
        ),
      }),
      columnHelper.accessor(
        (row) => row.session_datum.control_methods_equipments_parameter.parameter?.abbreviation,
        {
          header: 'Parameter',
          sortingFn: 'text',
          meta: {
            type: 'text',
          },
          cell: ({getValue}) => <span>{getValue() || 'N/A'}</span>,
        }
      ),
      columnHelper.accessor(() => undefined, {
        header: 'Rules Applied',
        enableSorting: false,
        meta: {
          type: 'text',
        },
        cell: (data) => {
          const filteredRules = controlRules?.filter((rule) =>
            rule?.control_method_equipment_parameters.some(
              (id) => id === data.row.original.session_datum.control_methods_equipments_parameter.id
            )
          );

          return (
            <span>
              {filteredRules
                ?.map((rule) => {
                  const value =
                    data?.row?.original?.session_datum?.control_methods_equipments_parameter?.control_methods_equipments_parameters_control_rules?.map(
                      (rule) => rule.value
                    );

                  const index = filteredRules?.findIndex((r) => r.id === rule.id);
                  return value[index] ? `${rule.name} (${value[index]})` : rule.name;
                })
                .join(', ')}
            </span>
          );
        },
      }),
      columnHelper.accessor(() => undefined, {
        header: 'Rules Violation',
        enableSorting: false,
        meta: {
          type: 'text',
        },
        cell: (data) => {
          const value =
            data?.row?.original?.session_datum?.control_methods_equipments_parameter?.control_methods_equipments_parameters_control_rules?.map(
              (rule) => rule.value
            );

          const rule = data?.row?.original?.alarm_control_rules?.map((rule) => rule.control_rule.name).join(', ');

          return (
          <div>
            {rule}
            {value?.filter(Boolean).length ? ` (${value.join('')})` : []}
          </div>
        )},
      }),
      columnHelper.accessor((row) => row.open, {
        id: 'status',
        header: 'Status',
        cell: (cellContext) => {
          const isOpen = cellContext?.row?.original?.open;
          return (
            <div className="flex items-center gap-x-4">
              <span
                className={clsx(
                  'rounded-md px-2 capitalize',
                  isOpen ? 'bg-red-50 text-red-700' : 'bg-blue-100 text-blue-800'
                )}
              >
                {isOpen ? 'open' : 'closed'}
              </span>
              {isOpen && (
                <Button
                  onPress={() => {
                    cellContext.table.resetExpanded();
                    cellContext.row.toggleExpanded();
                  }}
                  variant="plain"
                  className={'flex cursor-pointer items-center gap-x-2'}
                >
                  <ScanSearch className="h-4 w-4" />
                  <p className="text-xs">Review</p>
                </Button>
              )}
            </div>
          );
        },
        meta: {
          cellLoading: () => null,
          thProps: {
            className: 'max-w-12',
          },
          tdProps: {
            className: 'text-right',
          },
        },
      }),
      columnHelper.accessor(() => undefined, {
        header: 'Notes',
        enableSorting: false,
        cell: (data) => {
          const sessionDataId = data?.row?.original?.session_datum.id;
          const session = sessions?.find((session) =>
            session.session_datas.some((sd) => sd.id === sessionDataId)
          );
          const sessionData = session?.session_datas?.find(
            (sd) => sd.id === data?.row?.original?.session_datum.id
          );
          return (
            <Tooltip>
              <TooltipTrigger className="w-120 truncate text-left">{sessionData?.notes}</TooltipTrigger>
              <TooltipContent
                className={
                  sessionData?.notes?.length && sessionData.notes.length > 75
                    ? 'max-w-md whitespace-pre-wrap'
                    : 'hidden'
                }
              >
                {sessionData?.notes}
              </TooltipContent>
            </Tooltip>
          );
        },
      }),
    ],
    [authStore.tokenSiteId, alarms, sessions, parameters, controlRules]
  );

  return (
    <div className="flex flex-col items-end">
      <div className="mb-4 flex items-center gap-x-2 text-right">
        <Label className="text-sm font-semibold text-neutral-600">Status:</Label>
        <Select
          placeholder="Select Status"
          className="react-aria-Select w-44"
          isDisabled={isLoading}
          defaultSelectedKey="open"
          onSelectionChange={(val) => {
            if (val === 'all') {
              tableRef.current?.setColumnFilters([]);
            } else {
              tableRef.current?.setColumnFilters([
                {
                  id: 'status',
                  value: val === 'open',
                },
              ]);
            }
          }}
          aria-label="Select Graph Type"
        >
          <SelectButton className="flex h-8 w-full cursor-pointer items-center justify-between rounded-md border border-gray-300 bg-white px-4 py-2">
            <SelectValue className="react-aria-SelectValue text-sm text-gray-600" />
            <ChevronDown className="h-5 w-5 text-gray-400 in-data-[open=true]:rotate-180" />
          </SelectButton>
          <Popover className="entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out max-h-60 w-(--trigger-width) overflow-auto rounded-md bg-white text-base ring-1 shadow-lg ring-black/5">
            <ListBox className="space-y-2 p-2">
              {[
                {id: 'all', name: 'All'},
                {id: 'open', name: 'Open'},
                {id: 'closed', name: 'Closed'},
              ].map((graphType) => (
                <ListBoxItem
                  key={graphType.id}
                  id={graphType.id}
                  textValue={graphType.name}
                >
                  <div className="flex w-full cursor-pointer items-center justify-between">
                    <span>{graphType.name}</span>
                    <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                  </div>
                </ListBoxItem>
              ))}
            </ListBox>
          </Popover>
        </Select>
      </div>
      <div className="flex w-full">
        <DataTable
          ref={tableRef}
          data={data?.alarms ?? []}
          expandedRow={(row) => (
            <ExpandedRow
              fromAlert={true}
              sessions={sessions}
              users={users}
              row={row}
            />
          )}
          columns={columns as any}
          isLoading={isLoading || fetching}
          initialState={{
            columnFilters: [
              {
                id: 'status',
                value: true,
              },
            ],
            sorting: [
              {
                id: 'created',
                desc: true,
              },
            ],
          }}
        />
      </div>
    </div>
  );
}
