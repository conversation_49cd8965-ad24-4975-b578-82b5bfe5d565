import {Link} from 'react-router';
import {useQuery} from '@apollo/client';
import {getTestsList} from '@/graphql/normal-values.ts';

export default function NormalValuesTestLists() {
  const {data} = useQuery(getTestsList);
  const MAX_PARAMETERS = 9;

  return (
    <div className="columns-3 gap-4 space-y-4">
      {data?.pred_ref_tests.map(
        (item) =>
          !!item.pred_sourcextests?.length && (
            <Link
              to={`/normal-values/library/${item.testid}`}
              className="mb-4 block break-inside-avoid rounded-md border border-neutral-200 bg-white p-4"
              key={item.testid}
            >
              <div className="border-b border-neutral-200 pb-2 text-sm font-semibold text-neutral-800 capitalize">
                {item.test}
              </div>
              <div className="flex flex-col gap-y-3 pt-6 text-sm text-neutral-700">
                {item.pred_sourcextests?.map(
                  (sourceTest) =>
                    sourceTest?.sources?.description && (
                      <div
                        key={sourceTest.id}
                        className="flex items-start gap-x-1.5"
                      >
                        <div className="flex items-center gap-x-1 text-xs">
                          {(() => {
                            const match = sourceTest.sources.description.match(/^(.+?)\s*\((\d{4})\)$/);
                            if (match) {
                              const [_, text, year] = match;
                              return (
                                <>
                                  <span className="font-semibold text-neutral-800">{text.trim()}</span>
                                  <span className="font-normal text-neutral-700">({year}):</span>
                                </>
                              );
                            }
                            return (
                              <span className="font-semibold text-neutral-800">
                                {sourceTest.sources.description}:
                              </span>
                            );
                          })()}
                        </div>
                        <div className="flex items-center gap-x-1">
                          {sourceTest.sources?.parameters?.slice(0, MAX_PARAMETERS).map((e) => (
                            <span
                              key={e?.parameter?.id}
                              className="mb-1 bg-[#F4FAEB] px-2 py-1 text-[10px] whitespace-nowrap text-neutral-800"
                            >
                              {e?.parameter?.description}
                            </span>
                          ))}
                          {sourceTest.sources?.parameters?.length > MAX_PARAMETERS && (
                            <span className="mb-1 px-2 py-1 text-[10px] whitespace-nowrap text-neutral-800">
                              +{sourceTest.sources?.parameters.length - MAX_PARAMETERS}
                            </span>
                          )}
                        </div>
                      </div>
                    )
                )}
              </div>
            </Link>
          )
      )}
    </div>
  );
}
