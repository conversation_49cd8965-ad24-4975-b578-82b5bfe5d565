import clsx from 'clsx';
import {ComponentProps, useMemo, useState} from 'react';
import {Button as RA<PERSON><PERSON><PERSON>on, Tooltip, TooltipTrigger} from 'react-aria-components';
import {useNavigate, useParams} from 'react-router';
import {Fragment} from 'react/jsx-runtime';

import {useQuery} from '@apollo/client';
import {
  type CellContext,
  RowData,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getGroupedRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {CalculatorIcon, ChevronDown, PencilIcon, PlusIcon} from 'lucide-react';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {Paths} from '@/api-types/routePaths.ts';
import {Button} from '@/components/ui/button';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table.tsx';
import {getTestDetail, getTestEquations, getTestSources} from '@/graphql/normal-values.ts';
import {useApiQuery} from '@/hooks/use-api-query.ts';
import {isSuperUser} from '@/lib/permissions.ts';
import EquationAddEditDrawer from '@/views/normal-values/library/EquationAddEditDrawer.tsx';
import {EquationCalculator} from '@/views/normal-values/library/EquationCalculator.tsx';

import AddEditSourceDrawer from './AddEditSourceDrawer';

declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends RowData, TValue> {
    noHeader?: boolean;
    tdProps?: ComponentProps<'td'> | ((cell: CellContext<TData, TValue>) => ComponentProps<'td'>);
    thProps?: ComponentProps<'th'> | ((cell: CellContext<TData, TValue>) => ComponentProps<'td'>);

    [key: string]: any;
  }
}

const columnHelper = createColumnHelper<QueryResultType<typeof getTestEquations>['pred_equations'][number]>();

const grouping = ['source', 'parameter'];

export default function NormalValuesTestEquations() {
  const {testId} = useParams<{testId: string}>();
  const [expanded, setExpanded] = useState<any | Record<string, boolean>>(false);
  const [calculatorOpen, setCalculatorOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState<[number, string]>();
  const navigate = useNavigate();

  const {data: currentUser} = useApiQuery(Paths.PROFILE);
  const {data: testData} = useQuery(getTestDetail, {
    variables: {testid: +testId!},
  });

  const {data: equationData} = useQuery(getTestEquations, {
    variables: {testid: +testId!},
  });

  const {data: sourcesData} = useQuery(getTestSources, {
    variables: {testid: +testId!},
  });

  const sourcesWithoutEquations = useMemo(() => {
    if (!sourcesData?.pred_sourcextest || !equationData?.pred_equations) return [];

    const sourceIdsWithEquations = new Set(equationData.pred_equations.map((eq) => eq.sourceid));
    const uniqueSourceIds = new Set();

    return sourcesData.pred_sourcextest
      .filter((source) => !sourceIdsWithEquations.has(source.sourceid))
      .filter((source) => {
        if (uniqueSourceIds.has(source?.sources?.id)) {
          return false;
        }
        uniqueSourceIds.add(source?.sources?.id);
        return true;
      })
      .map((source) => ({
        id: source?.sources?.id!,
        sourceid: source.sourceid,
        source: source.sources?.source ?? 'Unknown Source',
        equationid: null,
        agegroup: null,
        equationtype: null,
        stattype_range: null,
        testid: Number(testId),
      }));
  }, [sourcesData, equationData, testId]);

  const openCalculator = (sourceName?: [number, string]) => {
    setSelectedSource(sourceName);
    setCalculatorOpen(true);
  };

  const columns = useMemo(
    () => [
      columnHelper.accessor('source', {
        header: 'Source',
        meta: {
          noHeader: true,
          tdProps: {
            className: 'text-neutral-800 text-xs !font-semibold bg-neutral-100 sticky top-28 z-10',
          },
        },
      }),
      columnHelper.accessor('parameter', {
        header: 'Parameter',
        meta: {
          thProps: {
            className: '!pl-15',
          },
          tdProps: {
            className: '[&>div]:ml-6 text-neutral-800 text-sm',
          },
        },
      }),
      columnHelper.accessor('gender', {
        header: 'Gender',
      }),
      columnHelper.accessor('ethnicity', {
        header: 'Ethnicity',
      }),
      columnHelper.accessor('agegroup', {
        header: 'Age Group',
      }),
      columnHelper.accessor('equationtype', {
        header: 'Equation type',
      }),
      columnHelper.accessor('stattype_range', {
        header: 'Range type',
      }),
    ],
    []
  );

  const tableData = useMemo(() => {
    const equationsData = equationData?.pred_equations ?? [];
    return [...equationsData, ...sourcesWithoutEquations];
  }, [equationData, sourcesWithoutEquations]);

  const table = useReactTable({
    data: tableData as any,
    columns,
    state: {
      grouping,
      expanded,
    },
    autoResetExpanded: false,
    onExpandedChange: (val) => {
      setExpanded((old:any) => {
        const newVal = typeof val === 'function' ? val(old) : val;
        if (typeof newVal === 'object' && !Object.keys(newVal).length) return old;
        return newVal;
      });
    },
    manualPagination: true,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  return (
    <div>
      <div className="mb-6 flex items-center gap-x-2">
        <h1 className="text-base/7 font-semibold text-neutral-900">
          {testData?.pred_ref_tests[0].description}
        </h1>

        <div className="flex-1" />
        {isSuperUser(currentUser?.email) && (
          <Button
            onPress={() => navigate('./source/add')}
            size="small"
          >
            <PlusIcon className="size-4" />
            Add Source
          </Button>
        )}
      </div>

      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                if (header.column.columnDef.meta?.noHeader) return null;

                const thProps = header.column.columnDef.meta?.thProps as ComponentProps<'th'>;

                return (
                  <TableHead
                    key={header.id}
                    {...thProps}
                    colSpan={header.colSpan ?? thProps?.colSpan}
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow
              data-selectable={!row.getIsGrouped() && row.original.equationid ? true : undefined}
              className={clsx(
                'data-[selectable]:cursor-pointer [&:not([data-selectable])]:!bg-white',
                !(row.original.parameter || row.getIsGrouped()) && 'hidden'
              )}
              onClick={() =>
                !row.getIsGrouped() && row.original.equationid && navigate('./' + row.original.equationid)
              }
              key={row.id}
            >
              {row.getVisibleCells().map((cell, _, cells) => (
                <Fragment key={cell.id}>
                  {(() => {
                    if (cell.getIsGrouped()) {
                      const prefix = cell.column.columnDef.meta?.noHeader
                        ? flexRender(cell.column.columnDef.header, cell.getContext() as any)
                        : null;
                      const cellProps = cell.column.columnDef.meta?.tdProps as ComponentProps<'td'>;

                      const hasEquations = row.subRows.some((r) => r.original.equationid);
                      const count = hasEquations
                        ? row.subRows.map((r) => r.subRows?.length || 1).reduce((a, b) => a + b, 0)
                        : 0;
                      let actions = null;

                      if (cell.column.id === 'source') {
                        actions = (
                          <div className="flex gap-x-2">
                            {isSuperUser(currentUser?.email) && (
                              <>
                                <TooltipTrigger>
                                  <RACButton
                                    onPress={() =>
                                      navigate(
                                        `./source/${cell.row.original.equationid ? cell.row.original.sourceid : (cell.row.original as any).id}`
                                      )
                                    }
                                    className="react-aria-Button -my-1 block cursor-pointer rounded p-1.5 hover:bg-neutral-300"
                                  >
                                    <PencilIcon className="size-3.5" />
                                  </RACButton>
                                  <Tooltip>Edit Source</Tooltip>
                                </TooltipTrigger>

                                <TooltipTrigger>
                                  <RACButton
                                    onPress={() =>
                                      navigate(
                                        `./add?sourceId=${cell.row.original.sourceid}&sourceName=${cell.row.original.source}`
                                      )
                                    }
                                    className="react-aria-Button -my-1 block cursor-pointer rounded p-1 hover:bg-neutral-300"
                                  >
                                    <PlusIcon className="size-4" />
                                  </RACButton>
                                  <Tooltip>Add Equation</Tooltip>
                                </TooltipTrigger>
                              </>
                            )}

                            <TooltipTrigger>
                              <RACButton
                                onPress={() =>
                                  openCalculator([cell.row.original.sourceid!, cell.row.original.source!])
                                }
                                className="react-aria-Button -my-1 block cursor-pointer rounded p-1 hover:bg-neutral-300"
                              >
                                <CalculatorIcon className="size-4" />
                              </RACButton>
                              <Tooltip>Calculation Checker</Tooltip>
                            </TooltipTrigger>
                          </div>
                        );
                      }

                      if (cell.column.id === 'parameter') {
                        if (!cell.row.original.equationid) {
                          return (
                            <TableCell
                              {...cellProps}
                              colSpan={row.getVisibleCells().length}
                              className="text-center"
                            >
                              <div className="flex flex-col items-center justify-center gap-x-1 py-6 space-y-1.5">
                                <span className="text-md font-semibold text-neutral-600 capitalize">
                                  No equation
                                </span>
                                {isSuperUser(currentUser?.email) && (
                                  <div>
                                    <span className="text-xs text-neutral-600 italic">
                                      You haven’t added any equations to this source yet. Add new equation
                                    </span>

                                    <Button
                                      variant="plain"
                                      onPress={() =>
                                        navigate(
                                          `./add?sourceId=${cell.row.original.sourceid}&sourceName=${cell.row.original.source}¶meterId=${cell.row.original.parameterid}¶meterName=${cell.row.original.parameter}`
                                        )
                                      }
                                      className="p-0 text-xs pl-1 italic"
                                      size="small"
                                    >
                                       here
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </TableCell>
                          );
                        }

                        if (isSuperUser(currentUser?.email)) {
                          actions = (
                            <div className="hidden gap-x-2 [tr:hover_&]:flex">
                              <RACButton
                                onPress={() =>
                                  navigate(
                                    `./add?sourceId=${cell.row.original.sourceid}&sourceName=${cell.row.original.source}¶meterId=${cell.row.original.parameterid}¶meterName=${cell.row.original.parameter}`
                                  )
                                }
                                className="react-aria-Button -my-1 block cursor-pointer rounded p-1 hover:bg-neutral-200"
                              >
                                <PlusIcon className="size-4" />
                              </RACButton>
                            </div>
                          );
                        }
                      }

                      return (
                        <TableCell
                          {...cellProps}
                          colSpan={cellProps?.colSpan ?? row.getVisibleCells().length}
                        >
                          <div className="flex items-center gap-x-6">
                            <button
                              onClick={row.getToggleExpandedHandler()}
                              className="flex cursor-pointer items-center gap-x-2"
                            >
                              <ChevronDown
                                strokeWidth={2.5}
                                className={clsx(
                                  'size-3 transition duration-150',
                                  row.getIsExpanded() ? '' : '-rotate-90'
                                )}
                              />
                              <div className="flex items-center gap-x-1.5">
                                {prefix && <span>{prefix}:</span>}
                                <span>{flexRender(cell.column.columnDef.cell, cell.getContext())}</span>
                                {!!count && <span className="ml-3 text-neutral-700">{count}</span>}
                              </div>
                            </button>
                            {actions}
                          </div>
                        </TableCell>
                      );
                    }

                    if (cells.find((c) => c.getIsGrouped())) {
                      return null;
                    }

                    if (cell.getIsAggregated()) {
                      return (
                        <TableCell>
                          {flexRender(
                            cell.column.columnDef.aggregatedCell ?? cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    }

                    if (cell.column.columnDef.meta?.noHeader) {
                      return null;
                    }

                    if (cell.getIsPlaceholder()) {
                      return <TableCell data-placeholder />;
                    }
                    if (cell.getIsAggregated()) {
                      return <TableCell data-aggregated />;
                    }
                    if (cell.getIsGrouped()) {
                      return <TableCell data-grouped />;
                    }

                    return <TableCell>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>;
                  })()}
                </Fragment>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {isSuperUser(currentUser?.email) && <AddEditSourceDrawer />}
      {isSuperUser(currentUser?.email) && <EquationAddEditDrawer />}

      <EquationCalculator
        testId={Number(testId)}
        testName={testData?.pred_ref_tests[0].description ?? ''}
        sourceName={selectedSource?.[1]}
        sourceId={selectedSource?.[0]!}
        isOpen={calculatorOpen}
        onClose={() => setCalculatorOpen(false)}
      />
    </div>
  );
}
