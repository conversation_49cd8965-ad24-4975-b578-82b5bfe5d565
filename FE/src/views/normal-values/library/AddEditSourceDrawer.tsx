import {ComponentProps} from 'react';
import {Checkbox, Dialog, GridListItem, Heading, Modal, Button as RACButton} from 'react-aria-components';
import {useNavigate, useParams} from 'react-router';

import {useMutation, useQuery} from '@apollo/client';
import {X} from 'lucide-react';

import {CheckboxIcon} from '@/components/ui/CheckboxIcon.tsx';
import {Input, Label, TextArea} from '@/components/ui/Field';
import {Button} from '@/components/ui/button';
import {Form, FormRootErrors, GridListFormField, NumberFormField, TextFormField} from '@/components/ui/form';
import {getPredGenders} from '@/graphql/equations';
import {
  createSource,
  createSourceTestRelation,
  getSourceDetail,
  getTestDetail,
  updateSource,
} from '@/graphql/normal-values';
import {getEthnicities} from '@/graphql/patients';

export default function AddEditSourceDrawer(props: ComponentProps<typeof Modal>) {
  const {sourceId, testId} = useParams<{sourceId: string; testId: string}>();
  const navigate = useNavigate();

  const isAddMode = sourceId === 'add';
  const {data: testData} = useQuery(getTestDetail, {
    variables: {testid: testId ? +testId : 0},
    skip: !testId,
  });

  const {data: sourceData} = useQuery(getSourceDetail, {
    variables: {sourceId: sourceId ? +sourceId : 0},
    skip: !sourceId || sourceId === 'add',
  });

  const {data: gendersData} = useQuery(getPredGenders);
  const {data: ethnicitiesData} = useQuery(getEthnicities);
  const [createSourceMutation] = useMutation(createSource, {
    refetchQueries: [getTestDetail],
  });

  const [createSourceTestRelationMutation] = useMutation(createSourceTestRelation, {
    refetchQueries: [getTestDetail],
  });

  const [updateSourceMutation] = useMutation(updateSource, {
    refetchQueries: [getSourceDetail, getTestDetail],
  });


  const handleFormSubmit = async (formData: any) => {
    const sourcePayload = {
      sourceid: formData.sourceid,
      source: formData.source,
      pub_reference: formData.pub_reference,
      description: formData.source,
    };

    try {
      if (isAddMode) {
        const sourceWithRelations = {
          ...sourcePayload,
          genders: {
            data: formData.genders.map((e: string) => ({genderid: +e})),
          },
          ethnicities: {
            data: formData.ethnicities.map((e: string) => ({ethnicityid: +e})),
          },
          parameters: {
            data: formData.parameters.map((e: string) => ({paramid: +e})),
          },
        };

        const result = await createSourceMutation({
          variables: {
            source: sourceWithRelations,
          },
        });

        if (testId && result.data) {
          await createSourceTestRelationMutation({
            variables: {
              sourceid: formData.sourceid,
              testid: +testId,
            },
          });
        }
      } else if (sourceId && sourceId !== 'add') {

        const sourceDbId = sourceData?.pred_ref_sources[0]?.id;

        const genderRelations = formData.genders.map((genderId: string) => ({
          sourceid: +sourceDbId!,
          genderid: +genderId,
        }));

        const ethnicityRelations = formData.ethnicities.map((ethnicityId: string) => ({
          sourceid: +sourceDbId!,
          ethnicityid: +ethnicityId,
        }));

        const parameterRelations = formData.parameters.map((paramId: string) => ({
          sourceid: +sourceDbId!,
          paramid: +paramId,
        }));

        delete sourcePayload.sourceid;
        await updateSourceMutation({
          variables: {
            sourceId: +sourceDbId!,
            source: sourcePayload,
            genders: genderRelations,
            ethnicities: ethnicityRelations,
            parameters: parameterRelations,
          },
        });
      }
    } catch (error) {
      console.error('Error updating source:', error);
      throw error;
    }

    navigate(window.location.pathname.split('/').slice(0, -1).join('/'), {
      replace: true,
    });
  };

  const initialValues = isAddMode
    ? {
        test: testData?.pred_ref_tests[0]?.test || '',
        sourceid: null,
        source: '',
        pub_reference: '',
        parameters: [],
        genders: [],
        ethnicities: [],
      }
    : {
        test: testData?.pred_ref_tests[0]?.test || '',
        sourceid: sourceData?.pred_ref_sources[0]?.sourceid || null,
        source: sourceData?.pred_ref_sources[0]?.source || '',
        pub_reference: sourceData?.pred_ref_sources[0]?.pub_reference || '',
        parameters:
          (sourceData?.pred_ref_sources[0] as any)?.parameters?.map((p: any) =>
            p.parameter.parameterid.toString()
          ) ?? [],
        genders:
          (sourceData?.pred_ref_sources[0] as any)?.genders?.map((p: any) =>
            p.pred_ref_gender.id.toString()
          ) ?? [],
        ethnicities:
          (sourceData?.pred_ref_sources[0] as any)?.ethnicities?.map((p: any) => p.ethnicity.id.toString()) ??
          [],
      };

  return (
    <Modal
      isDismissable
      className="react-aria-Drawer"
      isOpen={!!sourceId}
      onOpenChange={() => {
        navigate(window.location.pathname.split('/').slice(0, -2).join('/'), {
          replace: true,
        });
      }}
      {...props}
    >
      <Dialog className="react-aria-Dialog h-full">
        <Heading slot="title">{isAddMode ? 'Add Source' : 'Edit Source'}</Heading>
        <RACButton slot="close">
          <X />
        </RACButton>

        <Form
          key={isAddMode ? 'add-source' : sourceId}
          values={initialValues}
          className="mt-6 flex-grow"
          onSubmit={handleFormSubmit}
          id="source-form"
        >
          <div className="mt-6 space-y-3 pb-6">
            <FormRootErrors />

            <NumberFormField
              name="sourceid"
              aria-label="id"
              required
              formatOptions={{
                useGrouping: false,
              }}
              defaultValue={null}
            >
              <Label>ID</Label>
              <Input
                size="md"
                placeholder="Enter numeric ID for example: 1232"
              />
            </NumberFormField>

            <TextFormField
              isReadOnly
              required
              name="test"
            >
              <Label>Test</Label>
              <Input size="md" />
            </TextFormField>

            <TextFormField
              required
              name="source"
            >
              <Label>Source Name</Label>
              <Input size="md" />
            </TextFormField>

            <TextFormField name="pub_reference">
              <Label>Reference</Label>
              <TextArea
                className="react-aria-TextArea field-sizing-content"
                size="md"
              />
            </TextFormField>

            <div>
              <Label>Parameters</Label>
              <GridListFormField
                aria-label="Parameters"
                name="parameters"
                selectionMode="multiple"
                className="react-aria-GridList max-h-30 overflow-auto rounded-md border border-neutral-400 p-1"
                items={testData?.pred_ref_tests[0]?.pred_ref_parameters ?? []}
              >
                {(item) => (
                  <GridListItem
                    textValue={item.description!}
                    id={item.parameterid?.toString()}
                  >
                    <Checkbox slot="selection">
                      <CheckboxIcon className="checkbox size-4" />
                    </Checkbox>
                    <span>{item.description}</span>
                  </GridListItem>
                )}
              </GridListFormField>
            </div>

            <div>
              <Label>Genders</Label>
              <GridListFormField
                aria-label="Genders"
                name="genders"
                selectionMode="multiple"
                className="react-aria-GridList max-h-30 overflow-auto rounded-md border border-neutral-400 p-1"
                items={gendersData?.pred_ref_genders ?? []}
              >
                {(item) => (
                  <GridListItem
                    textValue={item.gender!}
                    id={item.id?.toString()}
                  >
                    <Checkbox slot="selection">
                      <CheckboxIcon className="checkbox size-4" />
                    </Checkbox>
                    <span>{item.gender}</span>
                  </GridListItem>
                )}
              </GridListFormField>
            </div>

            <div>
              <Label>Ethnicities</Label>
              <GridListFormField
                aria-label="Ethnicities"
                name="ethnicities"
                selectionMode="multiple"
                className="react-aria-GridList max-h-30 overflow-auto rounded-md border border-neutral-400 p-1"
                items={ethnicitiesData?.pred_ref_ethnicities ?? []}
              >
                {(item) => (
                  <GridListItem
                    textValue={item.description!}
                    id={item.id?.toString()}
                  >
                    <Checkbox slot="selection">
                      <CheckboxIcon className="checkbox size-4" />
                    </Checkbox>
                    <span>{item.description}</span>
                  </GridListItem>
                )}
              </GridListFormField>
            </div>
          </div>

          <div className="flex justify-end space-x-3 border-t border-gray-200 bg-white py-4 sm:px-6">
            <Button
              variant="outlined"
              onPress={() => {
                navigate(window.location.pathname.split('/').slice(0, -2).join('/'), {
                  replace: true,
                });
              }}
            >
              Cancel
            </Button>
            <Button type="submit">{isAddMode ? 'Add Source' : 'Save Changes'}</Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}
