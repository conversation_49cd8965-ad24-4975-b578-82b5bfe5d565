import {clsx} from 'clsx';
import {useState} from 'react';
import {
  DateInput,
  DatePicker,
  DateSegment,
  Dialog,
  Group,
  Heading,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
  Tree,
  TreeItem,
  TreeItemContent,
} from 'react-aria-components';
import {useForm} from 'react-hook-form';

import {useMutation} from '@apollo/client';
import {getLocalTimeZone, now, toCalendarDateTime} from '@internationalized/date';
import {areIntervalsOverlapping} from 'date-fns';
import {ChevronDown, ChevronRight, Weight, X} from 'lucide-react';

import {QueryResultType} from '@/@types/graphql.tada';
import BookIcon from '@/assets/iconly/Book.svg?react';
import ManWomanIcon from '@/assets/iconly/ManWoman.svg?react';
import UsersIcon from '@/assets/iconly/Users.svg?react';
import LockIcon from '@/assets/iconly/lock.svg?react';
import {CalendarIconly} from '@/components/icons/CalenderIconly';
import {useDialogState} from '@/components/modal-state-provider';
import {Label} from '@/components/ui/Field';
import {Button} from '@/components/ui/button';
import {Calendar} from '@/components/ui/calendar';
import {Form, FormField, FormRootErrors, SelectFormField} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/FieldError';
import {addPrefPredNew, getClipMethods, getPrefrencesList} from '@/graphql/normal-values';

interface EquationGrouperProps {
  equations: any;
  prefsNew: any;
  onAddNewPref: (pref: any) => void;
  clipMethods: QueryResultType<typeof getClipMethods>['pred_ref_clipmethods'];
}

const EquationGrouper = ({equations, prefsNew, onAddNewPref, clipMethods}: EquationGrouperProps) => {
  const [isOpen, setIsOpen] = useDialogState('add-preferences');
  const [selectedItem, setSelectedItem] = useState<any>();
  const [addNewPrefPred, {loading}] = useMutation(addPrefPredNew, {
    refetchQueries: [getPrefrencesList],
  });

  const form = useForm();

  const groupedEquations = equations?.reduce((acc: any, equation: any) => {
    if (!acc[equation.test]) {
      acc[equation.test] = {};
    }

    if (!acc[equation.test][equation.source]) {
      acc[equation.test][equation.source] = {};
    }

    if (!acc[equation.test][equation.source][equation.parameter]) {
      acc[equation.test][equation.source][equation.parameter] = {};
    }

    if (!acc[equation.test][equation.source][equation.parameter][equation.agegroup]) {
      acc[equation.test][equation.source][equation.parameter][equation.agegroup] = {};
    }

    if (!acc[equation.test][equation.source][equation.parameter][equation.agegroup][equation.ethnicity]) {
      acc[equation.test][equation.source][equation.parameter][equation.agegroup][equation.ethnicity] = {};
    }

    if (
      !acc[equation.test][equation.source][equation.parameter][equation.agegroup][equation.ethnicity][
        equation.gender
      ]
    ) {
      acc[equation.test][equation.source][equation.parameter][equation.agegroup][equation.ethnicity][
        equation.gender
      ] = [];
    }

    acc[equation.test][equation.source][equation.parameter][equation.agegroup][equation.ethnicity][
      equation.gender
    ].push(equation);

    return acc;
  }, {});

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={(e) => {
        setSelectedItem(null);
        setIsOpen(e);
      }}
      className="react-aria-Modal w-150 rounded-md p-0"
    >
      <Dialog className="mx-auto w-full">
        <Form
          control={form}
          onSubmit={async (data) => {
            const hasOverlap = prefsNew
              .filter(
                (pref: any) =>
                  pref.equation.test === selectedItem.test &&
                  pref.equation.parameter === selectedItem.parameter &&
                  pref.equation.agegroup === selectedItem.agegroup &&
                  pref.equation.ethnicity === selectedItem.ethnicity &&
                  pref.equation.gender === selectedItem.gender
              )
              .some((pref: any) => {
                const newStartDate = new Date(data.start_date);
                const newEndDate = new Date(data.end_date);
                const existingStartDate = new Date(pref.startdate);
                const existingEndDate = pref.enddate ? new Date(pref.enddate) : null;

                if (pref.equation.source !== selectedItem.source) {
                  if (existingEndDate) {
                    return areIntervalsOverlapping(
                      { start: newStartDate, end: newEndDate },
                      { start: existingStartDate, end: existingEndDate }
                    );
                  } else {
                    return areIntervalsOverlapping(
                      { start: newStartDate, end: newEndDate },
                      { start: existingStartDate, end: new Date('9999-12-31') }
                    );
                  }
                }
                return false;
              });

            if (hasOverlap) {
              form.setError('root', {
                type: 'server_validate',
                message: 'Unable to create preferences. Please try fixing the dates.',
              });
              return;
            }

            const result = await addNewPrefPred({
              variables: {
                prefPred: {
                  equationid: selectedItem.equationid,
                  active: null,
                  startdate: data.start_date.toString(),
                  enddate: data.end_date.toString(),
                  markfordeletion: null,
                  lastedit: new Date(),
                  lasteditby: null,
                  age_clipmethod: null,
                  age_clipmethodid: data.age_clip_method,
                  ht_clipmethod: null,
                  ht_clipmethodid: data.height_clip_method,
                  wt_clipmethod: null,
                  wt_clipmethodid: data.weight_clip_method,
                },
              },
            });

            const newPrefId = result.data?.insert_prefs_pred_new_one?.prefid;
            if (newPrefId) {
              const newPreference = {
                prefid: newPrefId,
                equationid: selectedItem.equationid,
                equation: selectedItem,
                active: null,
                startdate: data.start_date,
                enddate: data.end_date,
                markfordeletion: null,
                lastedit: new Date().toISOString(),
                lasteditby: null,
                age_clipmethod: null,
                age_clipmethodid: data.age_clip_method,
                ht_clipmethod: null,
                ht_clipmethodid: data.height_clip_method,
                wt_clipmethod: null,
                wt_clipmethodid: data.weight_clip_method,
              };

              onAddNewPref(newPreference);
            }
            setIsOpen(false);
            setSelectedItem(null);
            form.reset();
          }}
        >
          <div className="flex items-center justify-between border-b border-neutral-200 px-6 py-4 text-neutral-800">
            <Heading
              className="text-sm font-semibold"
              slot="title"
            >
              Add Normal Value Preferences
            </Heading>
            <div
              onClick={() => {
                setSelectedItem(null);
                setIsOpen(false);
              }}
              className="cursor-pointer"
            >
              <X className="h-4 w-4" />
            </div>
          </div>

          <div className="h-full max-h-150 overflow-y-auto px-6 py-4">
            <div className="pt-2 pb-4 text-sm">
              <p>
                {selectedItem
                  ? 'Select date range and clip method for this preference.'
                  : 'Navigate through Lab tests and select an option (locked items are already in your preferences).'}
              </p>
              {selectedItem && (
                <div className="mt-2 flex flex-wrap gap-x-1 text-xs text-neutral-600">
                  <span className="font-medium">Preference: </span>
                  <div className="flex flex-wrap items-center gap-x-px">
                    <span className="max-w-[100px] truncate">{selectedItem.test}</span>
                    <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                    <span className="max-w-[100px] truncate">{selectedItem.source}</span>
                    <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                    <span className="max-w-[100px] truncate">{selectedItem.parameter}</span>
                    <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                    <span className="max-w-[100px] truncate">{selectedItem.agegroup}</span>
                    <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                    <span className="max-w-[100px] truncate">{selectedItem.ethnicity}</span>
                    <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                    <span className="max-w-[100px] truncate">{selectedItem.gender}</span>
                  </div>
                </div>
              )}
            </div>
            <Tree className={selectedItem && 'hidden'}>
              {Object.keys(groupedEquations || {}).map((testName) => (
                <TreeItem
                  key={testName}
                  className={clsx(
                    'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
                    'data-focus-visible:ring-3 data-focused:bg-gray-100 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90'
                  )}
                  textValue={testName}
                  onAction={async () => {
                    setSelectedItem(null);
                  }}
                >
                  <TreeItemContent>
                    <div className="flex w-full items-center gap-x-2">
                      <ChevronRight className="size-4 transition-transform" />
                      {testName}
                    </div>
                  </TreeItemContent>

                  {Object.keys(groupedEquations[testName] || {}).map((sourceName) => {
                    return (
                      <TreeItem
                        key={sourceName}
                        className={clsx(
                          'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
                          'data-focus-visible:ring-3 data-focused:bg-gray-100 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90'
                        )}
                        textValue={sourceName}
                        onAction={async () => {
                          setSelectedItem(null);
                        }}
                      >
                        <TreeItemContent>
                          <div className="flex w-full items-center gap-x-2">
                            <ChevronRight className="size-4 transition-transform" />
                            <BookIcon className="size-4" />
                            {sourceName}
                          </div>
                        </TreeItemContent>

                        {Object.keys(groupedEquations[testName][sourceName] || {}).map((parameterName) => {
                          return (
                            <TreeItem
                              key={parameterName}
                              className={clsx(
                                'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
                                'data-focus-visible:ring-3 data-focused:bg-gray-100 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90'
                              )}
                              textValue={parameterName}
                              onAction={async () => {
                                setSelectedItem(null);
                              }}
                            >
                              <TreeItemContent>
                                <div className="flex w-full items-center gap-x-2">
                                  <ChevronRight className="size-4 transition-transform" />
                                  <Weight className="size-4" />
                                  {parameterName}
                                </div>
                              </TreeItemContent>

                              {Object.keys(groupedEquations[testName][sourceName][parameterName] || {}).map(
                                (ageGroupName) => {
                                  return (
                                    <TreeItem
                                      key={ageGroupName}
                                      className={clsx(
                                        'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
                                        'data-focus-visible:ring-3 data-focused:bg-gray-100 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90'
                                      )}
                                      textValue={ageGroupName}
                                      onAction={async () => {
                                        setSelectedItem(null);
                                      }}
                                    >
                                      <TreeItemContent>
                                        <div className="flex w-full items-center gap-x-2">
                                          <ChevronRight className="size-4 transition-transform" />
                                          {ageGroupName}
                                        </div>
                                      </TreeItemContent>

                                      {Object.keys(
                                        groupedEquations[testName][sourceName][parameterName][ageGroupName] ||
                                          {}
                                      ).map((ethnicityName) => {
                                        return (
                                          <TreeItem
                                            key={ethnicityName}
                                            className={clsx(
                                              'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
                                              'data-focus-visible:ring-3 data-focused:bg-gray-100 data-hovered:bg-gray-100 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90'
                                            )}
                                            textValue={ethnicityName}
                                            onAction={async () => {
                                              setSelectedItem(null);
                                            }}
                                          >
                                            <TreeItemContent>
                                              <div className="flex w-full items-center gap-x-2 truncate">
                                                <ChevronRight className="size-4 transition-transform" />
                                                <UsersIcon className="size-4" />
                                                {ethnicityName}
                                              </div>
                                            </TreeItemContent>

                                            {Object.keys(
                                              groupedEquations[testName][sourceName][parameterName][
                                                ageGroupName
                                              ][ethnicityName] || {}
                                            ).map((genderName) => {
                                              const isItemDisabled = !!prefsNew.find(
                                                (item: any) =>
                                                  item.equationid ===
                                                  groupedEquations[testName][sourceName][parameterName][
                                                    ageGroupName
                                                  ][ethnicityName][genderName][0].equationid
                                              );
                                              return (
                                                <TreeItem
                                                  key={genderName}
                                                  className={clsx(
                                                    'react-aria-TreeItem data-focus-visible:ring-brand-500/40 data-[disabled]:bg-muted/50 data-disabled:text-muted-foreground relative mb-2 flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*19px)] text-sm outline-hidden',
                                                    'data-focus-visible:ring-3 [&[data-expanded=true]>div>div>svg:first-child]:rotate-90',
                                                    selectedItem?.equationid ===
                                                      groupedEquations[testName][sourceName][parameterName][
                                                        ageGroupName
                                                      ][ethnicityName][genderName][0].equationid
                                                      ? 'bg-brand-500 hover:bg-brand-500 text-white'
                                                      : 'hover:bg-gray-100'
                                                  )}
                                                  textValue={genderName}
                                                  isDisabled={isItemDisabled}
                                                  onAction={async () => {
                                                    const item =
                                                      groupedEquations[testName][sourceName][parameterName][
                                                        ageGroupName
                                                      ][ethnicityName][genderName][0];
                                                    setSelectedItem(item);
                                                  }}
                                                >
                                                  <TreeItemContent>
                                                    <div className="flex w-full items-center gap-x-2">
                                                      <ManWomanIcon className="size-4 shrink-0 transition-transform" />
                                                      {genderName}
                                                      {isItemDisabled && (
                                                        <div className="flex items-center justify-center gap-x-2">
                                                          <LockIcon className="size-4 transition-transform" />
                                                          <span className="text-muted-foreground text-xs capitalize">
                                                            already exists
                                                          </span>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </TreeItemContent>
                                                </TreeItem>
                                              );
                                            })}
                                          </TreeItem>
                                        );
                                      })}
                                    </TreeItem>
                                  );
                                }
                              )}
                            </TreeItem>
                          );
                        })}
                      </TreeItem>
                    );
                  })}
                </TreeItem>
              ))}
            </Tree>
          </div>

          <div className={clsx('-mt-7 flex h-full flex-col gap-4 px-6 pt-4 pb-8', !selectedItem && 'hidden')}>
            <FormRootErrors />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                name="start_date"
                defaultValue={toCalendarDateTime(now(getLocalTimeZone()))}
                required
              >
                <DatePicker
                  granularity="day"
                  hourCycle={24}
                  shouldForceLeadingZeros
                  data-required
                >
                  <Label>Start date</Label>

                  <Group className="react-aria-Group h-8 w-full rounded-sm">
                    <DateInput className="react-aria-DateInput text-[13px]">
                      {(segment) => <DateSegment segment={segment} />}
                    </DateInput>
                    <RACButton className="react-aria-Button group">
                      <CalendarIconly
                        className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                        strokeWidth={1.5}
                      />
                    </RACButton>
                  </Group>
                  <Popover className="react-aria-Popover w-max">
                    <Dialog className="p-4">
                      <Calendar />
                    </Dialog>
                  </Popover>
                  <FieldError />
                </DatePicker>
              </FormField>

              <FormField
                name="end_date"
                defaultValue={null}

              >
                <DatePicker
                  granularity="day"
                  hourCycle={24}
                  shouldForceLeadingZeros
                  data-required
                >
                  <Label>End date</Label>
                  <Group className="react-aria-Group h-8 w-full rounded-sm">
                    <DateInput className="react-aria-DateInput text-[13px]">
                      {(segment) => <DateSegment segment={segment} />}
                    </DateInput>
                    <RACButton className="react-aria-Button group">
                      <CalendarIconly
                        className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                        strokeWidth={1.5}
                      />
                    </RACButton>
                  </Group>
                  <Popover className="react-aria-Popover w-max">
                    <Dialog className="p-4">
                      <Calendar />
                    </Dialog>
                  </Popover>
                  <FieldError />
                </DatePicker>
              </FormField>

              <div className="col-span-2">
                <SelectFormField
                  name="weight_clip_method"
                  placeholder="Select Weight Clip Method"
                  required
                >
                  <Label>Weight clip method</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={clipMethods}>
                      {(item) => (
                        <ListBoxItem
                          id={item.clipmethodid}
                          textValue={item.description ?? ''}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.description}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>
              </div>

              <div className="col-span-2">
                <SelectFormField
                  name="height_clip_method"
                  placeholder="Select Height Clip Method"
                  required
                >
                  <Label>Height clip method</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={clipMethods}>
                      {(item) => (
                        <ListBoxItem
                          id={item.clipmethodid}
                          textValue={item.description ?? ''}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.description}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>
              </div>

              <div className="col-span-2">
                <SelectFormField
                  name="age_clip_method"
                  placeholder="Select Age Clip Method"
                  required
                >
                  <Label>Age clip method</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={clipMethods}>
                      {(item) => (
                        <ListBoxItem
                          id={item.clipmethodid}
                          textValue={item.description ?? ''}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.description}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-x-3 border-t border-neutral-200 px-6 pt-4 pb-6">
            <Button
              className="h-10 w-full rounded-sm font-semibold"
              type="submit"
              isDisabled={!selectedItem || loading}
            >
              {loading ? 'Adding...' : 'Add Preference'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
};

export default EquationGrouper;
