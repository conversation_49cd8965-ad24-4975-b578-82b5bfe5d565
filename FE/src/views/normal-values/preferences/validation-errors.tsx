import {Dialog, Modal} from 'react-aria-components';
import {X} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {ChevronRight} from 'lucide-react';
import {QueryResultType} from '@/@types/graphql.tada';
import {getPrefrencesList} from '@/graphql/normal-values';


interface ValidationErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  errors: {[key: string]: string[]};
  data: QueryResultType<typeof getPrefrencesList>;
}

export function ValidationErrorModal({isOpen, onClose, errors, data}: ValidationErrorModalProps) {
  const getErrorDetails = (prefId: string) => {
    const pref = data?.prefs_pred_new?.find((p) => String(p.prefid) === prefId);
    if (!pref || !pref.equation) return null;

    return {
      test: pref?.equation.test,
      parameter: pref?.equation.parameter,
      source: pref?.equation.source,
      startDate: pref.startdate,
      endDate: pref.enddate,
      ethnicity: pref?.equation.ethnicity,
      ageGroup: pref?.equation.agegroup,
      gender: pref?.equation.gender
    };
  };

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={onClose}
      className="react-aria-Modal w-[500px] rounded-md bg-white p-0 shadow-lg"
    >
      <Dialog className="mx-auto w-full">
        <div className="flex items-center justify-between border-b border-neutral-200 px-4 py-3">
          <h2 className="text-sm font-medium text-neutral-800">Validation Errors</h2>
          <button
            onClick={onClose}
            className="rounded-full p-1 hover:bg-neutral-100"
          >
            <X className="h-4 w-4 text-neutral-500" />
          </button>
        </div>

        <div className="max-h-[60vh] overflow-y-auto px-4 py-3">
          <div className="space-y-3 divide-y divide-neutral-200 px-3">
            {Object.entries(errors).map(([prefId, errorMessages]) => {
              const details = getErrorDetails(prefId);
              if (!details) return null;

              return (
                <div
                  key={prefId}
                  className="bg-white py-3 first:pt-0 last:pb-0 w-full"
                >
                  <div className="flex flex-wrap items-center gap-x-1 text-sm text-neutral-900">
                    <div className="flex flex-wrap items-center gap-x-px">
                      <span className="max-w-[100px] truncate">{details.test}</span>
                      <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                      <span className="max-w-[100px] truncate">{details.source}</span>
                      <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                      <span className="max-w-[100px] truncate">{details.parameter}</span>
                      <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                      <span className="max-w-[100px] truncate">{details.ageGroup}</span>
                      <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                      <span className="max-w-[100px] truncate">{details.ethnicity}</span>
                      <ChevronRight className="mx-1 inline-block h-3 w-3 shrink-0" />
                      <span className="max-w-[100px] truncate">{details.gender}</span>
                    </div>
                  </div>
                  <div className="mt-2 space-y-1.5">
                    {errorMessages.map((message, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 text-sm text-neutral-800"
                      >
                        <span className="h-1 w-1 rounded-full bg-neutral-600" />
                        <span className="text-xs">{message}</span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="border-t border-neutral-200 px-4 py-3">
          <Button
            className="w-full"
            onPress={onClose}
          >
            Close
          </Button>
        </div>
      </Dialog>
    </Modal>
  );
}
