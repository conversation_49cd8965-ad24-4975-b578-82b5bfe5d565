import {
  Checkbox,
  DateInput,
  DatePicker,
  DateSegment,
  Dialog,
  Group,
  Heading,
  Key,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
  type Selection,
} from 'react-aria-components';

import {useApolloClient} from '@apollo/client';
import {Check, ChevronDown, X} from 'lucide-react';

import {QueryResultType} from '@/@types/graphql.tada';
import InfoIcon from '@/assets/iconly/Info.svg?react';
import {CalendarIconly} from '@/components/icons/CalenderIconly';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {CheckboxIcon} from '@/components/ui/CheckboxIcon';
import {Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Calendar} from '@/components/ui/calendar';
import {Form, FormField, FormRootErrors} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {getPersons, respiratoryLabQuery} from '@/graphql/reports';
import {updateRftRoutineFields} from '@/graphql/rft-mutations.ts';

function AddDefaults({
  onPrintReports,
  persons,
  selectedKeys,
}: {
  onPrintReports?: (formData: any) => void;
  persons: QueryResultType<typeof getPersons>['persons'];
  selectedKeys: Selection;
}) {
  const [isOpen, setIsOpen] = useDialogState('add-defaults');
  const client = useApolloClient();

  const handleSubmit = async (data: {
    reported_by: string;
    reported_by_date: string;
    authorised_by: string;
    authorised_by_date: string;
    verified_by: string;
    verified_by_date: string;
    completedby: boolean;
  }) => {
    if (onPrintReports) {
      await onPrintReports(data);
    }

    const rftKeys = Array.from(selectedKeys)
      .filter((key) => String(key).includes('rft_routine'))
      .map((routine) => Number(String(routine).replace('rft_routine', '')));

    if (rftKeys.length === 0) {
      return;
    }

    const getPersonName = (id: string) => {
      const person = persons.find((p) => String(p.id) === id);
      if (!person) return null;

      return person.title
        ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
        : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase();
    };

    const _set: Record<string, any> = {};

    if (data.reported_by) {
      _set.report_reportedby = getPersonName(data.reported_by);
    }

    if (data.reported_by_date) {
      _set.report_reporteddate = data.reported_by_date.toString();
    }

    if (data.authorised_by) {
      _set.report_authorisedby = getPersonName(data.authorised_by);
    }

    if (data.authorised_by_date) {
      _set.report_authoriseddate = data.authorised_by_date.toString();
    }

    if (data.verified_by) {
      _set.report_verifiedby = getPersonName(data.verified_by);
    }

    if (data.verified_by_date) {
      _set.report_verifieddate = data.verified_by_date.toString();
    }

    if (data.completedby === true) {
      _set.report_status = 'Completed';
    }

    const variables = {
      rftIds: rftKeys,
      _set,
    };

    console.log('Mutation variables:', variables);

    await client.mutate({
      mutation: updateRftRoutineFields,
      variables,
    });

    await client.refetchQueries({
      include: [respiratoryLabQuery],
    });

    setIsOpen(false);
  };

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="react-aria-Modal w-120 overflow-y-auto rounded-md p-0"
    >
      <Dialog>
        <Form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between border-b border-neutral-200 px-6 py-4 text-neutral-800">
            <Heading
              className="text-sm font-semibold"
              slot="title"
            >
              Add Defaults
            </Heading>
            <div onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </div>
          </div>
          <FormRootErrors />

          <div className="px-6 py-4">
            <div className="flex gap-3">
              <FormField name="reported_by">
                <Select
                  className="react-aria-Select mb-2 w-full"
                  placeholder="Select Staff Member"
                >
                  <Label className="mb-2 text-xs text-neutral-700">Reported by</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={persons}>
                      {(person) => (
                        <ListBoxItem
                          id={String(person.id) as Key}
                          textValue={
                            person.title
                              ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                              : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()
                          }
                        >
                          <div className="flex w-full cursor-pointer items-center justify-between">
                            <span className="capitalize">
                              {person.title
                                ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                                : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}
                            </span>
                            <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </Select>
                <FieldError />
              </FormField>

              <FormField name="reported_by_date">
                <DatePicker
                  granularity="day"
                  hourCycle={24}
                  shouldForceLeadingZeros
                  className="react-aria-DatePicker"
                >
                  <Label className="mb-2 text-xs font-semibold text-neutral-700">Date</Label>
                  <Group className="react-aria-Group w-40 rounded-sm text-neutral-700">
                    <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                    <RACButton className="react-aria-Button group">
                      <CalendarIconly
                        className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-500"
                        strokeWidth={1.5}
                      />
                    </RACButton>
                  </Group>
                  <Popover className="react-aria-Popover w-max">
                    <Dialog className="p-4">
                      <Calendar />
                    </Dialog>
                  </Popover>
                  <FieldError />
                </DatePicker>
              </FormField>
            </div>

            <div className="flex gap-3">
              <FormField name="authorised_by">
                <Select
                  className="react-aria-Select mb-2 w-full"
                  placeholder="Select Staff Member"
                >
                  <Label className="mb-2 text-xs text-neutral-700">Authorised by</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={persons}>
                      {(person) => (
                        <ListBoxItem
                          id={String(person.id) as Key}
                          textValue={
                            person.title
                              ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                              : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()
                          }
                        >
                          <div className="flex w-full cursor-pointer items-center justify-between">
                            <span className="capitalize">
                              {person.title
                                ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                                : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}
                            </span>
                            <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </Select>
                <FieldError />
              </FormField>

              <FormField name="authorised_by_date" >
                <DatePicker
                  granularity="day"
                  hourCycle={24}
                  shouldForceLeadingZeros
                  className="react-aria-DatePicker"
                >
                  <Label className="mb-2 text-xs font-semibold text-neutral-700">Date</Label>
                  <Group className="react-aria-Group w-40 rounded-sm text-neutral-700">
                    <DateInput >
                      {(segment) => {
                        return <DateSegment segment={segment} />;
                      }}
                    </DateInput>
                    <RACButton className="react-aria-Button group">
                      <CalendarIconly
                        className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-500"
                        strokeWidth={1.5}
                      />
                    </RACButton>
                  </Group>
                  <Popover className="react-aria-Popover w-max">
                    <Dialog className="p-4">
                      <Calendar />
                    </Dialog>
                  </Popover>
                  <FieldError />
                </DatePicker>
              </FormField>
            </div>

            <div className="flex gap-3">
              <FormField name="verified_by">
                <Select
                  className="react-aria-Select w-full"
                  placeholder="Select Staff Member"
                >
                  <Label className="mb-2 text-xs text-neutral-700">Verified by</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={persons}>
                      {(person) => (
                        <ListBoxItem
                          id={String(person.id) as Key}
                          textValue={
                            person.title
                              ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                              : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()
                          }
                        >
                          <div className="flex w-full cursor-pointer items-center justify-between">
                            <span className="capitalize">
                              {person.title
                                ? `${person.title}. ${[person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}`
                                : [person.surname, person.firstname].filter(Boolean).join(', ').toLowerCase()}
                            </span>
                            <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </Select>
                <FieldError />
              </FormField>

              <FormField name="verified_by_date">
                <DatePicker
                  granularity="day"
                  hourCycle={24}
                  shouldForceLeadingZeros
                  className="react-aria-DatePicker"
                >
                  <Label className="mb-2 text-xs font-semibold text-neutral-700">Date</Label>
                  <Group className="react-aria-Group w-40 rounded-sm text-neutral-700">
                    <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                    <RACButton className="react-aria-Button group">
                      <CalendarIconly
                        className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-500"
                        strokeWidth={1.5}
                      />
                    </RACButton>
                  </Group>
                  <Popover className="react-aria-Popover w-max">
                    <Dialog className="p-4">
                      <Calendar />
                    </Dialog>
                  </Popover>
                  <FieldError />
                </DatePicker>
              </FormField>
            </div>
            <div className="mt-4 mb-6 flex gap-1 text-xs text-neutral-700">
              <InfoIcon className="mt-px size-3.5 shrink-0 text-neutral-600" />
              These values are pre-filled based on your default settings. You can review and update them
              before printing.
            </div>
            <FormField name="completedby">
              <div className="mt-4 flex items-center gap-2">
                <Checkbox
                  slot="selection"
                  className="react-aria-Checkbox"
                >
                  <CheckboxIcon className="checkbox h-4 w-4" />
                  <div className="text-xs font-medium text-neutral-700">Move report status to completed</div>
                </Checkbox>
              </div>
            </FormField>
          </div>

          <div className="flex items-center gap-x-3 px-6 py-4">
            <Button
              onPress={() => setIsOpen(false)}
              className="text-brand-500 hover:!text-brand-500 w-full rounded-sm font-bold"
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full rounded-sm font-bold"
            >
              {onPrintReports ? 'Print & Update' : 'Update'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}

export default AddDefaults;
