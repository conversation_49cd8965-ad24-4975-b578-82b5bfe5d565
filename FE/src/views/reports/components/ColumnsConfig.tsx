import {ReactNode} from 'react';
import {Cell, Column} from 'react-aria-components';

import {format, isValid, parse, parseISO} from 'date-fns';

import {ReportStatusChip} from '@/components/ReportStatusChip.tsx';
import {ArrowUp} from 'lucide-react';

export interface ColumnConfig {
  id: string;
  header: string;
  accessor: (item: any) => any;
  renderCell: (value: any, item: any) => ReactNode;
  tabs?: string[];
  allowsSorting?: boolean;
}

export const columns: ColumnConfig[] = [
  {
    id: 'id',
    header: '#',
    accessor: (item) => item.id,
    renderCell: (value) => <Cell className="react-aria-Cell w-24">{value}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'mrn',
    header: 'Mrn',
    accessor: (item) => item.pas_pt.ur,
    renderCell: (value) => <Cell>{value}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'testDateTime',
    header: 'Test Date & Time',
    accessor: (item) => ({date: item.testdate, time: item.testtime}),
    renderCell: (value) => <Cell>{formatDateTime(value.date, value.time)}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'name',
    header: 'Name',
    accessor: (item) => {
      const fullName = [item.pas_pt.pas_pt_names[0].surname, item.pas_pt.pas_pt_names[0].firstname]
        .filter(Boolean)
        .join(', ')
        .toLowerCase();

      return {
        title: item.pas_pt.pas_pt_names[0].title,
        fullName,
      };
    },
    renderCell: (value) => {
      const displayName = value.title ? `${value.title.toLowerCase()}. ${value.fullName}` : value.fullName;

      return <Cell className="react-aria-Cell capitalize">{displayName}</Cell>;
    },
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'requestingMo',
    header: 'Requesting Mo',
    accessor: (item) => item.req_name,
    renderCell: (value) => <Cell>{value}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'testType',
    header: 'Test Type',
    accessor: (item) => item.testtype,
    renderCell: (value) => <Cell className="react-aria-Cell max-w-32 min-w-32 truncate">{value}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'reportStatus',
    header: 'Report status',
    accessor: (item) => item.report_status,
    renderCell: (value) => (
      <Cell className="react-aria-Cell w-10">{!!value && <ReportStatusChip reportStatus={value} />}</Cell>
    ),
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'copyTo',
    header: 'Copy To',
    accessor: (item) => item.report_copyto,
    renderCell: (value) => <Cell className="react-aria-Cell max-w-32 min-w-32 truncate">{value}</Cell>,
    tabs: ['RL', 'SL', 'CC'],
  },
  {
    id: 'assignedReporter',
    header: 'Assigned Reporter',
    accessor: (item) => item.reporting_assigned_to,
    renderCell: (value) => <Cell className="react-aria-Cell max-w-18 min-w-18 truncate">{value}</Cell>,
    tabs: ['SL'],
  },
  {
    id: 'assignedScorer',
    header: 'Assigned Scorer',
    accessor: (item) => item.scoring_assigned_to,
    renderCell: (value) => <Cell className="react-aria-Cell max-w-18 min-w-18 truncate">{value}</Cell>,
    tabs: ['SL'],
  },
];

export const getColumnsForTab = (tabId: string) => {
  return columns.filter((column) => column.tabs?.includes(tabId));
};

export const renderTableColumns = (currentTab: string, sortDescriptor?: {column?: string | number | null, direction?: 'ascending' | 'descending'}) => {
  const activeColumns = getColumnsForTab(currentTab);

  return activeColumns.map((column) => (
    <Column
      key={column.id}
      id={column.id}
      isRowHeader={column.id === 'id'}
      allowsSorting={column?.allowsSorting !== false}
    >
      <div className='flex items-center gap-x-2 justify-between'>
        {column.header}
        {column?.allowsSorting !== false && (
          <ArrowUp
            className={`h-3 w-3 transition-transform duration-200 ${
              sortDescriptor?.column === column.id
                ? sortDescriptor.direction === 'descending' ? 'rotate-180' : ''
                : 'opacity-0'
            }`}
          />
        )}
      </div>
    </Column>
  ));
};

export const renderTableRows = (item: any, currentTab: string) => {
  const activeColumns = getColumnsForTab(currentTab);

  return activeColumns.map((column) => {
    const value = column.accessor(item);
    return column.renderCell(value, item);
  });
};

const formatDateTime = (dateStr?: string, timeStr?: string) => {
  if (!dateStr) return '';

  try {
    let dateObj;

    if (dateStr.includes('-')) {
      dateObj = parseISO(dateStr);
    } else {
      dateObj = parse(dateStr, 'dd/MM/yyyy', new Date());

      if (!isValid(dateObj)) {
        dateObj = parse(dateStr, 'MM/dd/yyyy', new Date());
      }
    }

    if (!isValid(dateObj)) {
      return dateStr;
    }

    const formattedDate = format(dateObj, 'dd MMM yyyy');

    if (timeStr) {
      let timeFormat = 'HH:mm';

      if (timeStr.split(':').length > 2) {
        timeFormat = 'HH:mm:ss';
      }

      try {
        const timeObj = parse(timeStr, timeFormat, new Date());

        if (isValid(timeObj)) {
          return `${formattedDate}, ${format(timeObj, 'HH:mm')}`;
        }
      } catch {
        return `${formattedDate}, ${timeStr}`;
      }
    }

    return formattedDate;
  } catch {
    return timeStr ? `${dateStr}, ${timeStr}` : dateStr;
  }
};