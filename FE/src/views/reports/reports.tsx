import {useMemo, useState} from 'react';
import type {Selection, SortDescriptor} from 'react-aria-components';
import {
  Cell,
  Checkbox,
  Column,
  Input,
  Row,
  Tab,
  TabList,
  Table,
  TableHeader,
  Tabs,
} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {when} from 'mobx';

import {ReportStatusChip} from '@/components/ReportStatusChip.tsx';
import {LoadableTableBody} from '@/components/Table';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {CheckboxIcon} from '@/components/ui/CheckboxIcon';
import {Button} from '@/components/ui/button';
import {useRftReport} from '@/features/studies-rft/report';
import {RftStore} from '@/features/studies-rft/store/rtf.store';
import {getReportStatuses} from '@/graphql/lists';
import {getRequestedMO} from '@/graphql/patients';
import {cpapClinic<PERSON>uery, get<PERSON>ersons, respiratoryLabQuery, sleepStudyQuery} from '@/graphql/reports';
import FilterButton, {FilterItem} from '@/views/patients/components/filter-button';
import AddDefaults from '@/views/reports/components/add-defaults';

import {getColumnsForTab, renderTableColumns, renderTableRows} from './components/ColumnsConfig';

export default function Reports() {
  const [currentTab, setCurrentTab] = useState<string>('RL');
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<FilterItem[]>([
    {id: 'Unreported', textValue: 'Unreported'},
  ]);
  const [requestedMo, setRequestedMo] = useState<FilterItem[]>([]);
  const [assignedReporter, setAssignedReporter] = useState<FilterItem[]>([]);
  const [assignedScorer, setAssignedScorer] = useState<FilterItem[]>([]);
  const {data: ReportStatusList} = useQuery(getReportStatuses);
  const {data: ReportRequestedMO} = useQuery(getRequestedMO);
  const {createMultiReport, isLoading: reportLoading} = useRftReport();
  const [, setIsOpen] = useDialogState('add-defaults');

  const [selectedKeys, setSelectedKeys] = useState<Selection>(new Set());
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: 'testDateTime',
    direction: 'descending',
  });

  const buildBaseWhereClause = () => {
    return {
      pas_pt: {ur: {_ilike: search?.trim() ? `%${search.trim()}%` : '%'}},
    };
  };

  const buildWhereClause = (type: 'RL' | 'SL' | 'CC') => {
    const whereClause: any = buildBaseWhereClause();

    if (statusFilter.length > 0) {
      whereClause.report_status = {_in: statusFilter.map((g) => g.textValue).filter(Boolean)};
    }

    switch (type) {
      case 'RL':
        if (requestedMo.length > 0) {
          whereClause.r_session = {
            req_name: {_in: requestedMo.map((g) => g.textValue).filter(Boolean)},
          };
        }
        break;
      case 'SL':
        if (requestedMo.length > 0) {
          whereClause.requested_by = {_in: requestedMo.map((g) => g.textValue).filter(Boolean)};
        }
        if (assignedReporter.length > 0) {
          whereClause.reporting_assigned_to = {_in: assignedReporter.map((g) => g.textValue).filter(Boolean)};
        }
        if (assignedScorer.length > 0) {
          whereClause.scoring_assigned_to = {_in: assignedScorer.map((g) => g.textValue).filter(Boolean)};
        }
        break;
      case 'CC':
        if (requestedMo.length > 0) {
          whereClause.clinician = {_in: requestedMo.map((g) => g.textValue).filter(Boolean)};
        }
        break;
    }

    return whereClause;
  };

  const {data: respiratoryLabData, loading: respiratoryLoading} = useQuery(respiratoryLabQuery, {
    variables: {
      where: buildWhereClause('RL'),
      provWhere: buildWhereClause('RL'),
      walkWhere: buildWhereClause('RL'),
      cpetWhere: buildWhereClause('RL'),
      sptWhere: buildWhereClause('RL'),
      hastWhere: buildWhereClause('RL'),
    },
    skip: currentTab !== 'RL',
  });
  const {data: cpapClinicData, loading: cpapLoading} = useQuery(cpapClinicQuery, {
    variables: {
      where: buildWhereClause('CC'),
    },
    skip: currentTab !== 'CC',
  });
  const {data: sleepLabData, loading: sleepLabLoading} = useQuery(sleepStudyQuery, {
    variables: {
      where: buildWhereClause('SL'),
    },
    skip: currentTab !== 'SL',
  });

  const {data: personsData, loading: personsLoading} = useQuery(getPersons, {});

  const respiratoryLabTransformed = useMemo(() => {
    if (!respiratoryLabData) return [];

    const transformData = (data: any[], sessionKey: string) => {
      return data.map(({r_session, ...rest}) => ({
        ...rest,
        sessionid: r_session?.sessionid,
        testdate: r_session?.testdate,
        report_copyto: r_session?.report_copyto,
        req_name: r_session?.req_name,
        sessionKey,
      }));
    };

    return [
      ...transformData(respiratoryLabData.rft_routine || [], 'rft_routine'),
      ...transformData(respiratoryLabData.prov_test || [], 'prov_test'),
      ...transformData(respiratoryLabData.r_cpet || [], 'r_cpet'),
      ...transformData(respiratoryLabData.r_hast || [], 'r_hast'),
      ...transformData(respiratoryLabData.r_spt || [], 'r_spt'),
      ...transformData(respiratoryLabData.r_walktests_v1heavy || [], 'r_walktests_v1heavy'),
    ].sort((a, b) => {
      if (a.sessionKey === 'rft_routine' && b.sessionKey !== 'rft_routine') return -1;
      if (a.sessionKey !== 'rft_routine' && b.sessionKey === 'rft_routine') return 1;

      if (a.sessionKey === 'rft_routine' && b.sessionKey === 'rft_routine') {
        if (a.testdate && b.testdate) {
          return new Date(b.testdate).getTime() - new Date(a.testdate).getTime();
        }
      }

      return 0;
    });
  }, [respiratoryLabData]);

  const cpapClinicTransformed = useMemo(() => {
    if (!cpapClinicData) return [];

    return cpapClinicData.cpap_visit_clinic.map(({testdatetime, ...rest}) => ({
      ...rest,
      testdate: String(testdatetime)?.split('T')[0] ?? null,
      testtime: String(testdatetime)?.split('T')[1] ?? null,
    }));
  }, [cpapClinicData]);

  const sleepLabTransformed = useMemo(() => {
    if (!sleepLabData) return [];

    return sleepLabData.p_sleep_study.map(({...rest}) => ({
      ...rest,
      testtime: '',
    }));
  }, [sleepLabData]);

  const tabs = [
    {
      id: 'RL',
      label: 'Respiratory Laboratory',
      data: respiratoryLabTransformed,
      loading: respiratoryLoading,
      requestedMo: ReportRequestedMO
        ? [
            ...ReportRequestedMO.prov_test.map(({r_session, id}) => ({
              id: id,
              textValue: r_session?.req_name,
            })),
            ...ReportRequestedMO.r_cpet.map(({r_session, id}) => ({id: id, textValue: r_session?.req_name})),
            ...ReportRequestedMO.rft_routine.map(({r_session, id}) => ({
              id: id,
              textValue: r_session?.req_name,
            })),
            ...ReportRequestedMO.r_hast.map(({r_session, id}) => ({id: id, textValue: r_session?.req_name})),
            ...ReportRequestedMO.r_spt.map(({r_session, id}) => ({id: id, textValue: r_session?.req_name})),
            ...ReportRequestedMO.r_walktests_v1heavy.map(({r_session, id}) => ({
              id: id,
              textValue: r_session?.req_name,
            })),
          ]
        : [],
    },
    {
      id: 'SL',
      label: 'Sleep Laboratory',
      data: sleepLabTransformed,
      loading: sleepLabLoading,
      requestedMo: ReportRequestedMO
        ? [...ReportRequestedMO.p_sleep_study.map(({id, req_name}) => ({id: id, textValue: req_name}))]
        : [],
    },
    {
      id: 'CC',
      label: 'CPAP Clinic',
      data: cpapClinicTransformed,
      loading: cpapLoading,
      requestedMo: ReportRequestedMO
        ? [...ReportRequestedMO.cpap_visit_clinic.map(({id, req_name}) => ({id: id, textValue: req_name}))]
        : [],
    },
  ];

  const currentTabData = tabs.find((item) => item.id === currentTab) as unknown as any;
  const columnCount = getColumnsForTab(currentTab).length + 1;

  const sortedData = useMemo(() => {
    if (!currentTabData?.data || !sortDescriptor.column) return currentTabData?.data || [];

    const columns = getColumnsForTab(currentTab);
    const sortColumn = columns.find((col) => col.id === sortDescriptor.column);

    if (!sortColumn) return currentTabData.data;

    return [...currentTabData.data].sort((a, b) => {
      const aValue = sortColumn.accessor(a);
      const bValue = sortColumn.accessor(b);

      let comparison = 0;

      if (sortDescriptor.column === 'testDateTime') {
        const aDate = new Date(aValue.date + ' ' + (aValue.time || '00:00:00'));
        const bDate = new Date(bValue.date + ' ' + (bValue.time || '00:00:00'));
        comparison = aDate.getTime() - bDate.getTime();
      } else if (sortDescriptor.column === 'name') {
        comparison = (aValue.fullName || '').localeCompare(bValue.fullName || '');
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue || '').localeCompare(String(bValue || ''));
      }

      return sortDescriptor.direction === 'descending' ? -comparison : comparison;
    });
  }, [currentTabData?.data, sortDescriptor, currentTab]);

  const printRftReports = async (formData: any) => {
    const rftKeys = Array.from(selectedKeys).filter((key: any) => key.includes('rft_routine'));

    if (rftKeys.length === 0) {
      alert('Please select at least one RFT report to print');
      return;
    }

    try {
      const selectedItems = rftKeys
        .map((key) => currentTabData.data.find((item: any) => item.__typename + item.id === key))
        .filter(Boolean);

      const rftStores = await Promise.all(
        selectedItems.map(async (item) => {
          const rftStore = new RftStore({rftid: item.id});
          await when(() => {
            if (!rftStore.predMetadata) return false;

            const allParameterValues = [
              ...rftStore.spirometryStore.sp1.values,
              ...rftStore.spirometryStore.sp2.values,
              ...rftStore.lungVolumesStore.baseline.values,
              ...rftStore.coTransferStore.baseline.values,
              ...rftStore.bloodGasesStore.result1.values,
              ...rftStore.bloodGasesStore.result2.values,
              ...rftStore.exhaledNitricOxideStore.baseline.values,
              ...rftStore.mrpsStore.baseline.values,
            ];

            const allLoaded = allParameterValues.every((paramValue) => !paramValue.predLoading);

            return allLoaded;
          });

          if (!rftStore.report_reportedby) {
            rftStore.report_reportedby = formData.reported_by;
          }

          if (!rftStore.report_authorisedby) {
            rftStore.report_authorisedby = formData.authorised_by;
          }

          if (!rftStore.report_verifiedby) {
            rftStore.report_verifiedby = formData.verified_by;
          }

          if (!rftStore.report_reporteddate && formData.reported_by_date) {
            rftStore.report_reporteddate = formData.reported_by_date;
          }

          if (!rftStore.report_verifieddate && formData.verified_by_date) {
            rftStore.report_verifieddate = formData.verified_by_date;
          }

          if (!rftStore.report_authoriseddate && formData.authorised_by_date) {
            rftStore.report_authoriseddate = formData.authorised_by_date;
          }

          return rftStore;
        })
      );

      const reportProps = rftStores.map((store) => ({rftStore: store}));
      const url = await createMultiReport(reportProps);
      setSelectedKeys(new Set());
      rftStores.forEach((store) => store.dispose());

      if (!url) {
        throw new Error('Failed to generate report URL');
      }

      const reportWindow = window.open(url, '_blank');

      if (!reportWindow) {
        window.location.href = url;
        return;
      }

      reportWindow.addEventListener('load', () => {
        reportWindow.print();
      });
    } catch (error) {
      alert('Failed to generate reports. Please try again.');
    }
  };

  const isLoading = useMemo(() => {
    switch (currentTab) {
      case 'RL':
        return respiratoryLoading;
      case 'SL':
        return sleepLabLoading;
      case 'CC':
        return cpapLoading;
      default:
        return false;
    }
  }, [currentTab, respiratoryLoading, sleepLabLoading, cpapLoading]);

  return (
    <div className="mx-auto w-full py-6">
      <Tabs
        selectedKey={currentTab}
        onSelectionChange={(key) => {
          setSearch('');
          setCurrentTab(key as string);
          setStatusFilter([{id: 'Unreported', textValue: 'Unreported'}]);
          setAssignedReporter([]);
          setRequestedMo([]);
          setAssignedScorer([]);
          setSelectedKeys(new Set());
          setSortDescriptor({column: 'testDateTime', direction: 'descending'});
        }}
      >
        <div className="mb-6 flex w-max space-x-1 border-b">
          <TabList className="flex">
            {tabs.map((tab) => (
              <Tab
                key={tab.id}
                id={tab.id}
                className="data-[selected]:border-brand-500 data-[selected]:text-brand-500 relative z-1 -my-px cursor-pointer px-4 py-2 text-sm font-medium text-neutral-800 hover:text-neutral-900 focus:outline-none data-[selected]:border-b-2"
              >
                {tab.label}
              </Tab>
            ))}
          </TabList>
        </div>
      </Tabs>

      <div className="px-px">
        <div className="mb-4 flex w-full items-center gap-3">
          <div className="relative w-90">
            <Input
              placeholder="Search Reports"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-gray-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
            />
            <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
              <SearchIconly className="h-4.5 w-4.5" />
            </div>
          </div>

          <div className="flex flex-1 items-center gap-3">
            <FilterButton
              selectedItems={statusFilter}
              onSelectionChange={(selection) => {
                setStatusFilter(selection);
                setSelectedKeys(new Set());
              }}
              label="Report Status"
              reset={false}
              items={
                ReportStatusList?.list_reportstatuses.map((item) => ({
                  id: item.description,
                  textValue: item.description,
                  children: <ReportStatusChip reportStatus={item.description!} />,
                })) as any
              }
              disabled={isLoading}
            />

            <FilterButton
              selectedItems={requestedMo}
              onSelectionChange={(selection) => {
                setRequestedMo(selection);
                setSelectedKeys(new Set());
              }}
              label="Requested MO"
              reset={false}
              items={
                [
                  ...new Set(
                    currentTabData.requestedMo
                      ?.filter((item: any) => Boolean(item.textValue?.trim()))
                      .map((item: any) => item.textValue)
                  ),
                ].map((uniqueReqName) => ({
                  id: uniqueReqName,
                  textValue: uniqueReqName,
                })) as FilterItem[]
              }
              disabled={isLoading}
            />

            {['SL'].includes(currentTab) && (
              <>
                <FilterButton
                  selectedItems={assignedReporter}
                  onSelectionChange={(selection) => {
                    setAssignedReporter(selection);
                    setSelectedKeys(new Set());
                  }}
                  label="Assigned Reporter"
                  reset={false}
                  items={
                    [
                      ...new Set(
                        currentTabData.data
                          ?.filter((item: any) => Boolean(item.reporting_assigned_to?.trim()))
                          .map((item: any) => item.reporting_assigned_to)
                      ),
                    ].map((uniqueReqName) => ({
                      id: uniqueReqName,
                      textValue: uniqueReqName,
                    })) as FilterItem[]
                  }
                  disabled={isLoading}
                />

                <FilterButton
                  selectedItems={assignedScorer}
                  onSelectionChange={(selection) => {
                    setAssignedScorer(selection);
                    setSelectedKeys(new Set());
                  }}
                  label="Assigned Scorer"
                  reset={false}
                  items={
                    [
                      ...new Set(
                        currentTabData.data
                          ?.filter((item: any) => Boolean(item.scoring_assigned_to?.trim()))
                          .map((item: any) => item.scoring_assigned_to)
                      ),
                    ].map((uniqueReqName) => ({
                      id: uniqueReqName,
                      textValue: uniqueReqName,
                    })) as FilterItem[]
                  }
                  disabled={isLoading}
                />
              </>
            )}
          </div>

          <Button
            variant="solid"
            className="react-aria-Button h-8 flex-shrink-0 rounded-sm px-4 py-1 text-xs font-semibold whitespace-nowrap"
            onPress={() => setIsOpen(true)}
            isDisabled={isLoading}
          >
            {reportLoading ? 'compiling...' : 'Print Reports'}
          </Button>
        </div>

        <Table
          aria-label="Reports List"
          selectionMode="multiple"
          selectedKeys={selectedKeys}
          onSelectionChange={setSelectedKeys}
          sortDescriptor={sortDescriptor}
          onSortChange={setSortDescriptor}
        >
          <TableHeader>
            <Column>
              <Checkbox slot="selection">
                {({isIndeterminate}) => (
                  <svg
                    className="h-4 w-4"
                    viewBox="0 0 18 18"
                  >
                    {isIndeterminate ? (
                      <rect
                        x={3}
                        y={7.5}
                        width={12}
                        height={3}
                      />
                    ) : (
                      <CheckboxIcon />
                    )}
                  </svg>
                )}
              </Checkbox>
            </Column>

            {renderTableColumns(currentTab, sortDescriptor)}
          </TableHeader>

          <LoadableTableBody
            items={sortedData}
            emptyTitle="No test found"
            emptyDescription="Try refining your search or add a new test"
            isLoading={isLoading}
            columnCount={columnCount}
          >
            {(item: any) => (
              <Row
                id={item.__typename + item.id}
                isDisabled={!item.__typename.includes('rft_routine')}
                href={item.__typename === 'rft_routine' ? `reports/rft/${item.id}` : undefined}
              >
                <Cell className="react-aria-Cell w-12">
                  <Checkbox
                    slot="selection"
                    className="react-aria-Checkbox"
                  >
                    <CheckboxIcon className="checkbox h-4 w-4" />
                  </Checkbox>
                </Cell>

                {renderTableRows(item, currentTab)}
              </Row>
            )}
          </LoadableTableBody>
        </Table>

        {!personsLoading && (
          <AddDefaults
            onPrintReports={printRftReports}
            persons={personsData?.persons ?? []}
            selectedKeys={selectedKeys}
          />
        )}
      </div>
    </div>
  );
}
