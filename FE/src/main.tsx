import {StrictMode} from 'react';
import {I18nProvider} from 'react-aria-components';
import {ModalContext} from 'react-aria-components';
import {createRoot} from 'react-dom/client';

import {ApolloProvider} from '@apollo/client';
import '@smastrom/react-rating/style.css';
import {QueryClientProvider} from '@tanstack/react-query';
import {AllEnterpriseModule, LicenseManager, ModuleRegistry} from 'ag-grid-enterprise';
import {NuqsAdapter} from 'nuqs/adapters/react-router/v7';

import {apolloClient} from '@/apollo-client.ts';
import {ConfirmationProvider} from '@/hooks/use-confirmation.tsx';

import App from './App.tsx';
import {queryClient} from './query-client';
import './styles/index.css';

ModuleRegistry.registerModules([AllEnterpriseModule]);
LicenseManager.setLicenseKey(window.globalEnv.VITE_APP_AG_GRID_LICENSE_KEY as string);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <I18nProvider locale="en-AU">
      <NuqsAdapter>
        <QueryClientProvider client={queryClient}>
          <ApolloProvider client={apolloClient}>
            <ConfirmationProvider>
              <ModalContext.Provider value={{isDismissable: true}}>
                <App />
              </ModalContext.Provider>
            </ConfirmationProvider>
          </ApolloProvider>
        </QueryClientProvider>
      </NuqsAdapter>
    </I18nProvider>
  </StrictMode>
);
