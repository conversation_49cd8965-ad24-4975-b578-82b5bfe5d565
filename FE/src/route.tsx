import {Suspense, lazy} from 'react';
import {Navigate} from 'react-router';

import {StudiesRouter} from '@/features/studies';
import RftBreadcrumb from '@/features/studies-rft/components/RftBreadcrumb';
import AuthenticatedViewLayout from '@/layouts/AuthenticatedViewLayout.tsx';
import TestDetailBreadcrumb from '@/views/normal-values/components/TestDetailBreadcrumb';
import PatientLayout from '@/views/patients';
import PatientsView from '@/views/patients/patients-view';

import RootLayout from './layouts/RootLayout.tsx';
import PatientDetailBreadcrumb from './views/patients/components/PatientDetailBreadcrumb.tsx';
import ReportsLayout from './views/reports/ReportsLayout.tsx';
import MFASetupPage from "@/views/auth/MFASetup.tsx";
import MFACodesPage from "@/views/auth/MFACodes.tsx";
import {MFAChallenge} from "@/views/auth/MFAChallenge.tsx";
import {ResetPassword} from "@/views/auth/ResetPassword.tsx";

const NormalValuesTestDetailView = lazy(() => import('@/views/normal-values/library/test-equations.tsx'));
const NormalValuesTestListView = lazy(() => import('@/views/normal-values/library/test-lists.tsx'));

const LabTabs = lazy(() => import('./views/patients/patient-detail/LabTabs/LabTabs.tsx'));
const PatientTrendView = lazy(() => import('@/views/patients/patient-detail/patient-trend-view'));

const Dashboard = lazy(() => import('./views/dashboard'));

const PatientList = lazy(() => import('./views/patients/patients-list'));
const PatientDetail = lazy(() => import('./views/patients/patient-detail'));

const QualityControl = lazy(() => import('./views/quality-control/quality-control.tsx'));
const DevicesView = lazy(() => import('./views/quality-control/devices/devices-view'));
const ParametersView = lazy(() => import('./views/quality-control/parameters/parameters-view'));

const LoginPage = lazy(() => import('./views/auth/Login'));
const ForgetPassPage = lazy(() => import('./views/auth/ForgetPass'));

const Contacts = lazy(() => import('./views/contacts/contacts'));

const PdfImport = lazy(() => import('./views/pdf-import/pdf-import'));
const Reports = lazy(() => import('./views/reports'));
const Preferences = lazy(() => import('./views/normal-values/preferences/preferences.tsx'));
const NormalValues = lazy(() => import('./views/normal-values/normal-values.tsx'));

export const routes = [
  // Auth routes
  {
    path: '/auth/login',
    element: <LoginPage />,
  },
  {
    path: '/auth/forget-pass',
    element: <ForgetPassPage />,
  },
  {
    path: '/auth/mfa/setup',
    element: <MFASetupPage />,
  },
  {
    path: '/auth/mfa/codes',
    element: <MFACodesPage />,
  },
  {
    path: '/auth/mfa',
    element: <MFAChallenge />,
  },
  {
    path: '/auth/reset-pass',
    element: <ResetPassword />,
  },

  // Protected routes under RootLayout
  {
    path: '/',
    element: (
      <AuthenticatedViewLayout>
        <RootLayout />
      </AuthenticatedViewLayout>
    ),
    children: [
      {
        index: true,
        element: (
          <Navigate
            to="/patients"
            replace
          />
        ),
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
        handle: {
          crumbName: 'Dashboard',
        },
      },
      {
        path: 'contacts',
        element: <Contacts />,
        handle: {
          crumbName: 'Contacts',
        },
      },
      {
        path: 'pdf-import',
        element: <PdfImport />,
        handle: {
          crumbName: 'MAGIC Import',
        },
      },
      {
        path: 'reports',
        element: <ReportsLayout />,
        handle: {
          crumbName: 'Reporting',
        },
        children: [
          {
            index: true,
            element: <Reports />,
          },
          {
            path: '*',
            handle: {
              dynamicBreadcrumb: <RftBreadcrumb />,
              crumb: (data: any) => data,
              crumbName: 'Reporting Details',
            },
            element: <StudiesRouter />,
          },
        ],
      },
      {
        path: 'patients',
        handle: {
          crumbName: 'Patients',
        },
        children: [
          {
            index: true,
            element: <PatientsView />,
          },
          {
            path: 'all',
            element: <PatientList />,
          },
          {
            path: ':patientId',
            element: <PatientLayout />,
            handle: {
              dynamicBreadcrumb: <PatientDetailBreadcrumb />,
              crumbName: 'Patient Details',
            },
            children: [
              {
                element: <PatientDetail />,
                children: [
                  {
                    index: true,
                    element: <LabTabs />,
                  },
                  {
                    path: 'trend',
                    element: (
                      <Suspense fallback={<span />}>
                        <PatientTrendView />
                      </Suspense>
                    ),
                  },
                ],
              },
              {
                path: '*',
                handle: {
                  dynamicBreadcrumb: <RftBreadcrumb />,
                  crumbPath: '/patients/:patientId',
                  crumb: (data: any) => data,
                },
                element: <StudiesRouter />,
              },
            ],
          },
        ],
      },
      {
        path: 'quality-control',
        handle: {
          crumbName: 'Quality Control',
          crumbPath: '/quality-control',
        },
        element: <QualityControl />,
        children: [
          {
            index: true,
            element: (
              <Navigate
                to="devices"
                replace
              />
            ),
          },
          {
            path: 'devices/:labId/:deviceId/:controlEquipmentId',
            handle: {
              crumbName: 'Device',
              crumbPath: '/quality-control/devices/:labId/:deviceId/:controlEquipmentId',
            },
            element: <DevicesView />,
          },
          {
            path: 'parameters/:labId/:parameterId/:controlId',
            handle: {
              crumbName: 'Parameter',
              crumbPath: '/quality-control/parameters/:labId/:parameterId/:controlId',
            },
            element: <ParametersView />,
          },
          {
            path: 'devices',
            handle: {
              crumbName: 'Devices',
              crumbPath: '/quality-control/devices',
            },
            element: <DevicesView />,
          },
          {
            path: 'parameters',
            handle: {
              crumbName: 'Parameters',
              crumbPath: '/quality-control/parameters',
            },
            element: <ParametersView />,
          },
        ],
      },
      {
        path: 'normal-values',
        handle: {
          crumbName: 'Normal Values',
          crumbPath: '/normal-values',
        },
        element: <NormalValues />,
        children: [
          {
            index: true,
            element: (
              <Navigate
                to="library"
                replace
              />
            ),
          },
          {
            path: 'preferences',
            handle: {
              crumbName: 'Preferences',
              crumbPath: '/normal-values/preferences',
            },
            element: <Preferences />,
          },
          {
            path: 'library',
            handle: {
              crumbName: 'Library',
              crumbPath: '/normal-values/library',
            },
            children: [
              {
                index: true,
                element: <NormalValuesTestListView />,
              },
              {
                path: ':testId/:equationId?',
                handle: {
                  dynamicBreadcrumb: <TestDetailBreadcrumb />,
                  crumbName: 'Equations',
                  crumbPath: '/normal-values/library/:testId',
                },
                element: <NormalValuesTestDetailView />,
              },
              {
                path: ':testId/source/:sourceId',
                handle: {
                  dynamicBreadcrumb: <TestDetailBreadcrumb />,
                  crumbName: 'Source',
                  crumbPath: '/normal-values/library/:testId/source/:sourceId',
                },
                element: <NormalValuesTestDetailView />,
              },
            ],
          },
        ],
      },
    ],
  },
];
