import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';

/**
 * Main auto-interpretation function following ATS/ERS 2005 guidelines
 */
export default function AutoInterpret_rft_ATSERS2005(rftStore: RftStore): string {
  const report: string[] = [];

  const txt1 = ATSERS2005_Spirometry(rftStore);
  const txt2 = ATSERS2005_COTransfer(rftStore);

  // Special case for when both spirometry and CO transfer are normal
  if (txt1.toLowerCase() === "ventilatory function is within normal limits. " &&
    txt2.toLowerCase() === "carbon monoxide transfer factor is within normal limits. ") {
    report.push("Ventilatory function and carbon monoxide transfer factor are within normal limits. ");
  } else {
    report.push(txt1);
    report.push(txt2);
  }

  // Add remaining test interpretations
  report.push(ATSERS2005_LungVols(rftStore));
  report.push(ATSERS2005_MRPs(rftStore));
  report.push(ATSERS2005_FeNO(rftStore));
  report.push(ATSERS2005_ABGs(rftStore));
  report.push(ATSERS2005_SpO2(rftStore));

  // Return the complete report
  return report.join('');
}

/**
 * ATS ERS 2005 Spirometry interpretation
 */
export function ATSERS2005_Spirometry(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get pre-bronchodilator values
  const preFev1Value = rftStore.spirometryStore.getSp1Value('FEV1');
  const preVcValue = rftStore.spirometryStore.getSp1Value('VC');
  const preFvcValue = rftStore.spirometryStore.getSp1Value('FVC');
  const preLabel = rftStore.spirometryStore.sp1Condition ?? 'Pre-bronchodilator';

  // Get post-bronchodilator values
  const postFev1Value = rftStore.spirometryStore.getSp2Value('FEV1');
  const postVcValue = rftStore.spirometryStore.getSp2Value('VC');
  const postFvcValue = rftStore.spirometryStore.getSp2Value('FVC');
  const postLabel = rftStore.spirometryStore.sp2Condition ?? 'Post-bronchodilator';

  // Get actual values
  const preFev1 = preFev1Value?.result ?? 0;
  const preVc = preVcValue?.result ?? 0;
  const preFvc = preFvcValue?.result ?? 0;
  const postFev1 = postFev1Value?.result ?? 0;
  const postVc = postVcValue?.result ?? 0;
  const postFvc = postFvcValue?.result ?? 0;

  // Calculate VC/FVC maximums
  const preVcFvc = Math.max(preVc, preFvc);
  const postVcFvc = Math.max(postVc, postFvc);

  // Get reference values
  const vcLLN = preVcValue?.predResult?.lln ?? 0;
  const fvcLLN = preFvcValue?.predResult?.lln ?? 0;
  const VcFvc_LLN = Math.max(vcLLN, fvcLLN);
  const ferLLN = rftStore.spirometryStore.getSp1Value('FEV1/FVC')?.predResult?.lln ?? 0;
  const pFEV1 = preFev1Value?.predResult?.mpv ?? 1;
  const pFVC = preFvcValue?.predResult?.mpv ?? 1;

  const preDone = preFev1 > 0 && preVcFvc > 0;
  const postDone = postFev1 > 0 && postVcFvc > 0;

  let fev1: number, vcfvc: number, fer: number, prepostLabel: string;

  if (preDone) {
    fev1 = preFev1;
    vcfvc = preVcFvc;
    fer = preFev1 / preVcFvc * 100;
    prepostLabel = preLabel;
  } else if (postDone) {
    fev1 = postFev1;
    vcfvc = postVcFvc;
    fer = postFev1 / postVcFvc * 100;
    prepostLabel = postLabel;
  } else {
    return '';
  }

  txt.push(`${prepostLabel} `);

  if (fer > ferLLN) {
    if (vcfvc >= VcFvc_LLN) {
      txt.push("ventilatory function is within normal limits");
    } else {
      const severity = GetSeverity_ATSERS2005("Spiro", 100 * vcfvc / pFVC);
      txt.push(`spirometry suggests a ${severity} restrictive ventilatory defect`);
    }
  } else {
    if (vcfvc >= VcFvc_LLN) {
      const severity = GetSeverity_ATSERS2005("Spiro", 100 * fev1 / pFEV1);
      txt.push(`spirometry shows a ${severity} obstructive ventilatory defect`);
    } else {
      const severity = GetSeverity_ATSERS2005("Spiro", 100 * fev1 / pFEV1);
      txt.push(`spirometry shows a ${severity} mixed obstructive/restrictive ventilatory defect`);
    }
  }

  if (preDone && postDone) {
    // Calculate deltas
    const fev1Delta = postFev1 - preFev1;
    const fev1DeltaPercent = (fev1Delta / preFev1) * 100;
    const fvcDelta = postFvc - preFvc;
    const fvcDeltaPercent = (fvcDelta / preFvc) * 100;
    const vcDelta = postVc - preVc;
    const vcDeltaPercent = (vcDelta / preVc) * 100;

    // Define thresholds
    const significantFev1Delta = 0.2;
    const significantVcFvcDelta = 0.1;
    const significantPercentDelta = 12;

    // Determine responses
    const fev1ResponseSignificant = fev1Delta >= significantFev1Delta && fev1DeltaPercent >= significantPercentDelta;
    const fev1ResponseSmall = fev1Delta >= significantFev1Delta || fev1DeltaPercent >= significantPercentDelta;
    const fvcResponseSignificant = fvcDelta >= significantVcFvcDelta && fvcDeltaPercent >= significantPercentDelta;
    const vcResponseSignificant = vcDelta >= significantVcFvcDelta && vcDeltaPercent >= significantPercentDelta;

    if (fev1ResponseSignificant) {
      txt.push(" together with a highly significant bronchodilator response that is suggestive of asthma");
    } else if (fvcResponseSignificant || vcResponseSignificant) {
      txt.push(" with a significant bronchodilator response");
    } else if (fev1ResponseSmall) {
      txt.push(" together with a small but possibly clinically useful bronchodilator response");
    } else {
      txt.push(" with no significant bronchodilator response on this occasion");
    }

    // Check for normal limits with significant response
    if (txt.join('').includes("normal limits") && (fev1ResponseSignificant || fvcResponseSignificant || vcResponseSignificant)) {
      txt.length = 0; // Clear the array
      txt.push("Although baseline spirometric values are within normal limits there is a significant bronchodilator response indicating some airflow obstruction");
    }
  }

  // Ensure proper punctuation
  const result = txt.join('');
  if (!result.endsWith(".")) {
    return result + ". ";
  }
  return result + " ";
}

/**
 * Helper function to determine severity based on ATS/ERS 2005 criteria
 */
function GetSeverity_ATSERS2005(
  type: "Spiro" | "TLCO" | "Hyperinflation" | "Restriction",
  percentPredicted: number
): string {
  switch (type) {
    case "Spiro":
      if (percentPredicted >= 70) return "mild";
      if (percentPredicted >= 60) return "moderate";
      if (percentPredicted >= 50) return "moderately severe";
      if (percentPredicted >= 35) return "severe";
      if (percentPredicted > 0) return "very severe";
      return "";

    case "TLCO":
      if (percentPredicted > 60) return "mildly";
      if (percentPredicted > 40) return "moderately";
      if (percentPredicted > 0) return "severely";
      return "";

    case "Hyperinflation":
      if (percentPredicted > 150) return "marked";
      if (percentPredicted > 135) return "moderate";
      if (percentPredicted > 120) return "mild";
      return "";

    case "Restriction":
      if (percentPredicted > 75) return "mildly";
      if (percentPredicted > 60) return "moderately";
      if (percentPredicted > 0) return "severely";
      return "";

    default:
      return "";
  }
}

/**
 * ATS ERS 2005 Carbon monoxide transfer factor interpretation
 */
export function ATSERS2005_COTransfer(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get values from the store
  const tlcoValue = rftStore.coTransferStore.getParamValue('TLCO');
  const kcoValue = rftStore.coTransferStore.getParamValue('KCO');
  const vaValue = rftStore.coTransferStore.getParamValue('VA');
  const hbValue = rftStore.coTransferStore.getParamValue('Hb');

  // Get the actual values
  const tlco = tlcoValue?.result ?? 0;
  const kco = kcoValue?.result ?? 0;
  const va = vaValue?.result ?? 0;
  const hb = hbValue?.result ?? 0;

  // Get reference values
  const pTLCO = tlcoValue?.predResult?.mpv ?? 1; // Avoid division by zero
  const TLCOlln = tlcoValue?.predResult?.lln ?? 0;
  const vaLLN = vaValue?.predResult?.lln ?? 0;
  const kcoRangeHi = kcoValue?.predResult?.uln ?? 0;

  const testDone = tlco > 0;

  if (testDone) {
    let addedBit: string;
    let adjustedTlco = tlco;
    let adjustedKco = kco;

    if (hb === 0) {
      addedBit = ", uncorrected for haemoglobin,";
    } else {
      const hbFactor = rftStore.coTransferStore.hbFactor;
      if (hbFactor) {
        adjustedTlco = tlco * hbFactor;
        adjustedKco = kco * hbFactor;
      }
      addedBit = ", corrected for haemoglobin,";
    }

    if (adjustedTlco <= TLCOlln) {
      txt.push(`Carbon monoxide transfer factor${addedBit}`);
      const severity = GetSeverity_ATSERS2005("TLCO", 100 * adjustedTlco / pTLCO);
      txt.push(`is ${severity} reduced `);

      if (va > vaLLN) {
        txt.push("indicating lung parenchymal and/or pulmonary vascular dysfunction. ");
      } else if (adjustedKco > kcoRangeHi) {
        txt.push("due to loss of alveolar volume rather than lung parenchymal or pulmonary vascular dysfunction. ");
      } else {
        txt.push("at least in part due to loss of alveolar volume, but also indicating lung parenchymal and/or pulmonary vascular dysfunction. ");
      }
    } else {
      txt.push(`Carbon monoxide transfer factor${addedBit}is within normal limits. `);
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 Lung Volumes interpretation
 */
export function ATSERS2005_LungVols(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get values from the store
  const tlcValue = rftStore.lungVolumesStore.getParamValue('TLC');
  const frcValue = rftStore.lungVolumesStore.getParamValue('FRC');
  const rvValue = rftStore.lungVolumesStore.getParamValue('RV');
  const rvTlcValue = rftStore.lungVolumesStore.getParamValue('RV/TLC');

  const tlc = tlcValue?.result ?? 0;
  const frc = frcValue?.result ?? 0;
  const rv = rvValue?.result ?? 0;

  // Get reference values
  const pTLC = tlcValue?.predResult?.mpv ?? 1; // Avoid division by zero
  const tlcRangeLo = tlcValue?.predResult?.lln ?? 0;
  const tlcRangeHi = tlcValue?.predResult?.uln ?? 0;
  const rvLLN = rvValue?.predResult?.lln ?? 0;
  const rvtlcULN = rvTlcValue?.predResult?.uln ?? 0;

  // Calculate RV/TLC as percentage
  const rv_tlc = tlc > 0 ? (100 * rv / tlc) : 0;
  const testDone = tlc > 0 && frc > 0 && rv > 0;

  if (testDone) {
    if (tlc < tlcRangeLo) {
      if (rv < rvLLN) {
        const severity = GetSeverity_ATSERS2005("Restriction", 100 * tlc / pTLC);
        txt.push(`Plethysmographic lung volumes show a ${severity} reduction confirming a restrictive pulmonary defect. `);
      } else {
        const severity = GetSeverity_ATSERS2005("Restriction", 100 * tlc / pTLC);
        txt.push(`Total lung capacity shows a ${severity} reduction confirming a restrictive pulmonary defect. `);
      }
    } else if (tlc > tlcRangeHi) {
      if (rv_tlc > rvtlcULN) {
        const severity = GetSeverity_ATSERS2005("Hyperinflation", 100 * tlc / pTLC);
        txt.push(`Plethysmographic lung volumes indicate ${severity} hyperinflation with gas trapping. `);
      } else {
        const severity = GetSeverity_ATSERS2005("Hyperinflation", 100 * tlc / pTLC);
        txt.push(`Plethysmographic lung volumes indicate ${severity} hyperinflation but without an increased RV/TLC ratio. `);
      }
    } else {
      if (tlc < (tlcRangeLo + 0.25)) {
        txt.push('Total lung capacity is towards the lower limit of the normal range raising the possibility of a restrictive pulmonary defect. ');
      } else if (rv_tlc > rvtlcULN) {
        txt.push('Whilst TLC is within the normal range, the raised RV/TLC ratio indicates some gas trapping. ');
      } else {
        txt.push('Plethysmographic lung volumes are within normal limits. ');
      }
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 MRPs interpretation
 */
export function ATSERS2005_MRPs(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get MIP and MEP values from the store
  const mipValue = rftStore.mrpsStore.getParamValue('MIP');
  const mepValue = rftStore.mrpsStore.getParamValue('MEP');

  const mip = mipValue?.result ?? 0;
  const mep = mepValue?.result ?? 0;
  const miplln = mipValue?.predResult?.lln ?? 0;
  const meplln = mepValue?.predResult?.lln ?? 0;

  const testDone = mip > 0 && mep > 0;

  if (testDone) {
    if (mip > miplln && mep > meplln) {
      txt.push('Maximal respiratory pressures are within the normal range. ');
    } else if (mip <= miplln && mep <= meplln) {
      txt.push('Maximal respiratory pressures are reduced indicating respiratory muscle and diaphragmatic weakness. ');
    } else if (mip <= miplln) {
      txt.push('Maximal inspiratory pressure is reduced indicating diaphragmatic weakness. Maximal expiratory pressure is within the normal range. ');
    } else if (mep <= meplln) {
      txt.push('Maximal expiratory pressure is reduced indicating respiratory muscle weakness. Maximal inspiratory pressure is within the normal range. ');
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 FeNO interpretation
 */
export function ATSERS2005_FeNO(rftStore: RftStore) {
  const txt: string[] = [];

  // Get FeNO value from the store
  const value = rftStore.exhaledNitricOxideStore.getParamValue('FeNO');
  const feno = value?.result ?? 0;
  const fenouln = value?.predResult?.uln ?? 0;

  const testDone = feno > 0;

  if (testDone) {
    // Calculate grey_uln based on fenouln
    let grey_uln: number;
    if (fenouln === 20) {
      grey_uln = 35;
    } else if (fenouln === 25) {
      grey_uln = 50;
    } else {
      grey_uln = fenouln * 1.4; // Default fallback
    }

    if (feno < fenouln) {
      txt.push('FeNO result is low and does not suggest the presence of active eosinophilic inflammation. ');
    } else if (feno <= grey_uln) {
      txt.push('FeNO result is intermediate and may suggest the presence of active eosinophilic inflammation. ');
    } else {
      txt.push('FeNO result is high and suggests the presence of active eosinophilic inflammation. ');
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 ABGs interpretation
 */
export function ATSERS2005_ABGs(rftStore: RftStore) {
  const txt: string[] = [];

  // Get values from the blood gases store
  const value1 = rftStore.bloodGasesStore.getResult1Value('PaO2');
  const value2 = rftStore.bloodGasesStore.getResult1Value('PaCO2');
  const value3 = rftStore.bloodGasesStore.getResult1Value('pH');
  const value4 = rftStore.bloodGasesStore.getResult1Value('A-aPO2');

  const pao2 = value1?.result ?? 0;
  const paco2 = value2?.result ?? 0;
  const ph = value3?.result ?? 0;
  const aapo2 = value4?.result ?? 0;
  const fio2 = rftStore.bloodGasesStore.result1.fio2.toLowerCase();

  const fio2_is_air = fio2.includes('air');
  const testDone = paco2 > 0 && pao2 > 0 && ph > 0;

  if (testDone) {
    // PO2 interpretation
    if (pao2 < (value1?.predResult?.lln ?? 0)) {
      if (fio2_is_air) {
        if (aapo2 > (value4?.predResult?.uln ?? 0)) {
          txt.push('Arterial blood gases reveal hypoxaemia with a widened (A-a)PO2 gradient');
        } else {
          txt.push('Arterial blood gases reveal hypoxaemia but with a normal (A-a)PO2 gradient');
        }
      } else {
        txt.push('Arterial blood gases reveal hypoxaemia');
      }
    } else {
      if (fio2_is_air) {
        if (aapo2 > (value4?.predResult?.uln ?? 0)) {
          txt.push('Arterial blood gases reveal normoxaemia but with a widened (A-a)PO2 gradient');
        } else {
          txt.push('Arterial blood gases reveal normoxaemia with a normal (A-a)PO2 gradient');
        }
      } else {
        txt.push('Arterial blood gases reveal normoxaemia');
      }
    }

    // PCO2 interpretation
    if (paco2 < (value2?.predResult?.lln ?? 0)) {
      txt.push(', alveolar hyperventilation');
    } else if (paco2 > (value2?.predResult?.uln ?? 0)) {
      txt.push(', alveolar hypoventilation');
    }

    // pH interpretation
    if (ph > (value3?.predResult?.uln ?? 0)) {
      txt.push(' and alkalosis. ');
    } else if (ph < (value3?.predResult?.lln ?? 0)) {
      txt.push(' and acidosis. ');
    } else {
      txt.push(' and a normal pH. ');
    }

    const result = txt.join('');
    if (!result.endsWith('. ')) {
      return result + '. ';
    }
    return result;
  }

  return '';
}

/**
 * ATS ERS 2005 SpO2 interpretation
 */
export function ATSERS2005_SpO2(rftStore: RftStore) {
  const value = rftStore.bloodGasesStore.getResult1Value('SpO2');
  const lln = value?.predResult?.lln;
  const spo2 = value?.result ?? 0;

  if (spo2 <= 0 || !lln) return '';

  if (spo2 < lln) {
    return `Oxygen saturation by pulse oximetry is reduced. `;
  } else {
    return `Oxygen saturation by pulse oximetry is within the normal range. `;
  }
}
