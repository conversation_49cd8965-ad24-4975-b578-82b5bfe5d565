import authStore from '@/store/auth.store.ts';

import {RftStore} from '../studies-rft/store/rtf.store.ts';
import AutoInterpret_rft_ATSERS2005 from './ats-ers-2005.ts';
import {AutoInterpret_rft_ATSERS2021} from './ats-ers-2021.ts';

export default function autoInterpretRft(rftStore: RftStore) {
  const autoReportAlgorithm = authStore.site?.getConfig('autoreport_algorithm') ?? 'ats_ers_2021';

  if (autoReportAlgorithm === 'ats_ers_2021') {
    return AutoInterpret_rft_ATSERS2021(rftStore).replaceAll(/\s+/g, ' ');
  } else if (autoReportAlgorithm === 'ats_ers_2005') {
    return AutoInterpret_rft_ATSERS2005(rftStore).replaceAll(/\s+/g, ' ');
  }
}
