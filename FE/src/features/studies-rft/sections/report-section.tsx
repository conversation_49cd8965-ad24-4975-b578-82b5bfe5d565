import {clsx} from 'clsx';
import {useEffect} from 'react';
import {
  DateInput,
  DatePicker,
  DateSegment,
  Dialog,
  DialogTrigger,
  Group,
  Heading,
  ListBox,
  ListBoxItem,
  Menu,
  MenuItem,
  MenuTrigger,
  Modal,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {fromDate, getLocalTimeZone, toCalendarDateTime} from '@internationalized/date';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Placeholder from '@tiptap/extension-placeholder';
import Text from '@tiptap/extension-text';
import {EditorContent, useEditor} from '@tiptap/react';
import {Calendar as CalenderIcon, ChevronDown, X} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import SparksAiIcon from '@/assets/iconly/SparksAi.svg?react';
import NIL from '@/components/NIL.tsx';
import {ReportStatusChip} from '@/components/ReportStatusChip.tsx';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {StreamStringExtension} from '@/components/tiptap/stream-string.tsx';
import {Suggestions} from '@/components/tiptap/suggestions.tsx';
import {Label, TextArea} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Calendar} from '@/components/ui/calendar.tsx';
import {FormField, FormRootErrors, SelectFormField, TextFormField} from '@/components/ui/form';
import {Form} from '@/components/ui/form/Form.tsx';
import autoInterpretRft from '@/features/auto-interpret-rft';
import ReportPhrasesDialog from '@/features/studies-rft/components/ReportPhrasesDialog.tsx';
import {getPreferenceField} from '@/graphql/preferences.ts';
import {getPersonsWithPerm} from '@/graphql/users.ts';
import {formatPersonName} from '@/lib/utils.ts';
import {globalStore} from '@/store/global.store.ts';

import {RftStore} from '../store/rtf.store.ts';

export default observer(({rftStore}: {rftStore: RftStore}) => {

  const [, setAmendDialogState] = useDialogState('amend-report-modal');

  return (
    <div className="space-y-4 px-4 pb-4 text-xs/[1.3]">
      <MenuTrigger>
        <RACButton
          className={clsx(
            '-mx-1 flex w-full items-center justify-between rounded px-1 py-1 focus:outline-none',
            rftStore.isEditing ? 'cursor-pointer hover:bg-neutral-50' : ''
          )}
          aria-label="Menu"
          isDisabled={!rftStore.isEditing}
        >
          <div className="text-[11px]/[1.4] font-semibold text-neutral-600">Report Status</div>
          <ReportStatusChip
            reportStatus={
              rftStore.report_status ?? (globalStore.reportStatuses ?? [])[0]?.description ?? 'Unreported'
            }
          />
        </RACButton>
        <Popover>
          <Menu items={globalStore.reportStatuses ?? []}>
            {(item) => (
              <MenuItem
                id={item.description}
                textValue={item.description}
                className="react-aria-MenuItem px-1"
                onAction={() => rftStore.setProperty('report_status', item.description)}
              >
                <ReportStatusChip reportStatus={item.description} />
              </MenuItem>
            )}
          </Menu>
        </Popover>
      </MenuTrigger>

      <ReportTextArea rftStore={rftStore} />

      <ReportStageLog
        label="Reported by"
        selectedPerson={rftStore.report_reportedby ?? null}
        selectedDate={rftStore.report_reporteddate ?? null}
        fieldName="report_reportedby"
        rftStore={rftStore}
        perm="rft_can_report"
      />

      <ReportStageLog
        label="Authorized by"
        selectedPerson={rftStore.report_authorisedby ?? null}
        selectedDate={rftStore.report_authoriseddate ?? null}
        rftStore={rftStore}
        fieldName="report_authorisedby"
        perm="rft_can_authorise_reports"
      />

      <ReportStageLog
        label="Verified by"
        selectedPerson={rftStore.report_verifiedby ?? null}
        selectedDate={rftStore.report_verifieddate ?? null}
        rftStore={rftStore}
        fieldName="report_verifiedby"
        perm="rft_can_verifyreports"
      />

      {(rftStore.report_amendedby || rftStore.report_amendeddate) && (
        <ReportStageLog
          label="Amended by"
          selectedPerson={rftStore.report_amendedby?.trim() ?? null}
          selectedDate={rftStore.report_amendeddate ?? null}
          rftStore={rftStore}
          fieldName="report_amendedby"
          perm="rft_can_ammendverifiedreports"
        />
      )}

      {rftStore.report_amendednotes && (
        <div>
          <Label className="mb-1.5 block text-[11px]/[1.4] text-neutral-600">Amended notes</Label>
          <p className="text-xs text-neutral-800">{rftStore.report_amendednotes}</p>
        </div>
      )}

      {rftStore.isAmendable && (
        <>
          <Button
            variant="outlined"
            size="small"
            className="w-full"
            onPress={() => setAmendDialogState(true)}
          >
            Amend Report
          </Button>
          <AmendReportModal rftStore={rftStore} />
        </>
      )}
    </div>
  );
});

interface ReportStageLogProps {
  rftStore: RftStore;
  perm: string;
  label: string;
  selectedPerson: string | null;
  selectedDate: string | null;
  fieldName: string;
}

function ReportStageLog({
  rftStore,
  perm,
  label,
  selectedPerson,
  selectedDate,
  fieldName,
}: ReportStageLogProps) {
  const {data: personsData} = useQuery(getPersonsWithPerm, {
    variables: {
      perm,
    },
  });

  const personsList =
    personsData?.persons.map((person) => ({
      id: formatPersonName(person).toLowerCase(),
      label:
        formatPersonName(person) + (person.profession_category ? ` (${person.profession_category})` : ''),
    })) ?? [];

  if (selectedPerson && !personsList.find((p) => p.id === selectedPerson?.trim()?.toLowerCase())) {
    personsList.push({
      id: selectedPerson?.trim()?.toLowerCase(),
      label: selectedPerson,
    });
  }

  return (
    <div className="grid grid-cols-2 items-start gap-x-2">
      {rftStore.isEditing ? (
        <Select
          selectedKey={selectedPerson?.toLowerCase() ?? null}
          placeholder={undefined}
          onSelectionChange={(key) => {
            const personKey = (key as string | null) ?? undefined;
            rftStore.setProperty(fieldName as any, personKey);
          }}
        >
          <Label className="mb-1.5 block text-[11px]/[1.4] text-neutral-600">{label}</Label>
          <RACButton className="react-aria-Button h-8 w-full">
            <SelectValue className="react-aria-SelectValue text-[13px]/[1.3]" />
            <ChevronDown
              className="size-4 text-gray-400"
              aria-hidden="true"
            />
          </RACButton>
          <Popover className="react-aria-Popover w-auto min-w-[--trigger-width]">
            <ListBox items={personsList}>
              {(item) => (
                <ListBoxItem
                  id={item.id}
                  textValue={item.label}
                >
                  {item.label}
                </ListBoxItem>
              )}
            </ListBox>
          </Popover>
        </Select>
      ) : (
        <div>
          <Label className="mb-1.5 block text-[11px]/[1.4] text-neutral-600">{label}</Label>
          <div className="flex h-8 text-start text-[13px]/[1.3] text-neutral-800">
            {selectedPerson ? selectedPerson : <NIL className="ml-0" />}
          </div>
        </div>
      )}
      <div>
        <DatePicker
          value={
            selectedDate ? toCalendarDateTime(fromDate(new Date(selectedDate), getLocalTimeZone())) : null
          }
          granularity="day"
          shouldForceLeadingZeros
          onChange={(dateValue) => {
            const dateString = dateValue ? dateValue.toDate(getLocalTimeZone()).toISOString() : undefined;
            if (label === 'Reported by') rftStore.setProperty('report_reporteddate', dateString);
            if (label === 'Authorized by') rftStore.setProperty('report_authoriseddate', dateString);
            if (label === 'Verified by') rftStore.setProperty('report_verifieddate', dateString);
            if (label === 'Amended by') rftStore.setProperty('report_amendeddate', dateString);
          }}
        >
          <Label className="mb-1.5 block text-[11px]/[1.4] text-neutral-600">Date</Label>
          <div
            className={clsx(
              rftStore.isEditing ? 'hidden' : 'flex',
              'h-8 items-center text-[13px]/[1.3] text-neutral-800'
            )}
          >
            {!selectedDate ? (
              <NIL className="ml-0 text-neutral-800" />
            ) : (
              <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
            )}
          </div>
          <Group
            className={clsx(
              'react-aria-Group data-[focus-within]:border-brand2-400 relative h-8 w-full rounded-md border-gray-300',
              !rftStore.isEditing && 'hidden'
            )}
          >
            <DateInput className="flex-1 text-[13px]">
              {(segment) => (
                <DateSegment
                  className="react-aria-DateSegment focus:outline-none"
                  segment={segment}
                />
              )}
            </DateInput>
            <RACButton className="react-aria-Button mr-1 border-0 p-1 outline-none">
              <CalenderIcon className="h-4 w-4 text-neutral-500" />
            </RACButton>
          </Group>
          <Popover className="react-aria-Popover w-max">
            <Dialog className="p-0 outline-none">
              <Calendar />
            </Dialog>
          </Popover>
        </DatePicker>
      </div>
    </div>
  );
}

const extensions = [
  Document,
  Text,
  Paragraph,
  StreamStringExtension,
  Placeholder.configure({
    placeholder: 'Add your report here...',
  }),
];

const ReportTextArea = observer(({rftStore}: {rftStore: RftStore}) => {
  const [isDialogOpen, setIsDialogOpen] = useDialogState('report-phrases-dialogue');
  const editor = useEditor({
    extensions,
    content: rftStore.report_text,
    editable: rftStore.isEditing,
    onUpdate: ({editor}) => {
      if (rftStore.isEditing) {
        rftStore.setProperty('report_text', editor.getText());
      }
    },
  });

  const {data: SpirometryPhrases} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Reportphrases_Spirometry'},
  });
  const {data: TLCOPhrases} = useQuery(getPreferenceField, {variables: {fieldName: 'Reportphrases_TLCO'}});
  const {data: LVPhrases} = useQuery(getPreferenceField, {variables: {fieldName: 'Reportphrases_LV'}});
  const {data: MRPPhrases} = useQuery(getPreferenceField, {variables: {fieldName: 'Reportphrases_MRP'}});
  const {data: FeNOPhrases} = useQuery(getPreferenceField, {variables: {fieldName: 'Reportphrases_FeNO'}});
  const {data: SpO2Phrases} = useQuery(getPreferenceField, {variables: {fieldName: 'Reportphrases_SpO2'}});
  const {data: GeneralPhrases} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Reportphrases_General'},
  });

  const data = [
    {
      id: SpirometryPhrases?.prefs_fields?.[0]?.field_id,
      name: 'Spirometry',
      fieldItems: SpirometryPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: TLCOPhrases?.prefs_fields?.[0]?.field_id,
      name: 'TLCO',
      fieldItems: TLCOPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: LVPhrases?.prefs_fields?.[0]?.field_id,
      name: 'LV',
      fieldItems: LVPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: MRPPhrases?.prefs_fields?.[0]?.field_id,
      name: 'MRP',
      fieldItems: MRPPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: FeNOPhrases?.prefs_fields?.[0]?.field_id,
      name: 'FeNO',
      fieldItems: FeNOPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: SpO2Phrases?.prefs_fields?.[0]?.field_id,
      name: 'SpO2',
      fieldItems: SpO2Phrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
    {
      id: GeneralPhrases?.prefs_fields?.[0]?.field_id,
      name: 'General',
      fieldItems: GeneralPhrases?.prefs_fields?.[0]?.prefs_fielditems,
    },
  ];

  const suggestions = data
    .flatMap((items) => items?.fieldItems?.map((item) => item.fielditem) || [])
    .filter(Boolean);

  useEffect(() => {
    if (!editor?.getText() && rftStore.report_text) {
      editor?.commands.setContent(rftStore.report_text);
    }
  }, [editor, rftStore.report_text]);

  if (!rftStore.isEditing) {
    return (
      <div className="react-aria-TextField">
        <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Report</Label>
        <p className="py-1 text-xs text-neutral-800">{rftStore.report_text || <NIL className="ml-0" />}</p>
      </div>
    );
  }

  return (
    <div className="react-aria-TextField">
      <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Report</Label>
      <div className="relative">
        <EditorContent
          editor={editor}
          className="text-xs"
        />
        {editor && !isDialogOpen && (
          <Suggestions
            className="text-xs"
            suggestions={suggestions ?? []}
            editor={editor}
          />
        )}
        <div className="user-select-none absolute right-1.75 bottom-1 z-20 flex items-center">
          <Button
            size="small"
            variant="plain"
            className="rounded-sm px-1.25 py-1.25 data-hovered:bg-neutral-100"
            isPending={editor?.storage.streamString.isStreaming}
            isDisabled={editor?.storage.streamString.isStreaming}
            onPress={() => {
              const autoReport = autoInterpretRft(rftStore);
              if (autoReport) editor?.commands.streamString(autoReport);
            }}
          >
            <SparksAiIcon />
          </Button>

          <DialogTrigger>
            <Button
              size="small"
              variant="plain"
              className="rounded-sm px-2 py-1.25 data-hovered:bg-neutral-100"
              onPress={() => setIsDialogOpen(true)}
            >
              Phrases
            </Button>

            <ReportPhrasesDialog
              data={data}
              onPhraseSelection={(selectedText) => {
                editor?.commands.insertContent(selectedText + ' ');
              }}
            />
          </DialogTrigger>
        </div>
      </div>
    </div>
  );
});

const AmendReportModal = observer(({rftStore}: {rftStore: RftStore}) => {
  const [isOpen, setOpenState] = useDialogState('amend-report-modal');

  const {data: personsData} = useQuery(getPersonsWithPerm, {
    variables: {
      perm: 'rft_can_ammendverifiedreports',
    },
  });

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={setOpenState}
      className="react-aria-Modal max-w-100 overflow-y-auto"
    >
      <Dialog>
        <Heading slot="title">Amend Report</Heading>
        <RACButton slot="close">
          <X />
        </RACButton>

        <Form
          onSubmit={async (values) => {
            const completedNAmended = globalStore.reportStatuses.find(
              (status) =>
                status.description?.toLowerCase()?.includes('completed') &&
                status.description?.toLowerCase()?.includes('amend')
            );

            rftStore.setProperty('report_amendedby', values.report_amendedby);
            if (values.report_amendeddate) {
              rftStore.setProperty('report_amendeddate', values.report_amendeddate.toDate().toISOString());
            }
            rftStore.setProperty('report_amendednotes', values.report_amendednotes);
            rftStore.setProperty('isEditing', true);
            if (completedNAmended) {
              rftStore.setProperty('report_status', completedNAmended.description);
            }

            setOpenState(false);
          }}
        >
          <div className="space-y-4 py-6">
            <FormRootErrors />

            <SelectFormField
              name="report_amendedby"
              placeholder="Select Staff Member"
              defaultValue={rftStore.report_amendedby}
            >
              <Label>Amended by</Label>
              <RACButton className="react-aria-Button w-full rounded-sm">
                <SelectValue className="react-aria-SelectValue text-sm" />
                <ChevronDown />
              </RACButton>
              <Popover>
                <ListBox items={personsData?.persons}>
                  {(person) => (
                    <ListBoxItem
                      id={formatPersonName(person).toLowerCase()}
                      textValue={formatPersonName(person)}
                    >
                      {formatPersonName(person)}
                    </ListBoxItem>
                  )}
                </ListBox>
              </Popover>
            </SelectFormField>

            <FormField
              defaultValue={toCalendarDateTime(fromDate(new Date(), getLocalTimeZone()))}
              name="report_amendeddate"
            >
              <DatePicker
                granularity="day"
                shouldForceLeadingZeros
              >
                <Label className="mb-1.5 block text-[11px]/[1.4] text-neutral-600">Amend Date</Label>
                <Group className="react-aria-Group data-[focus-within]:border-brand2-400 relative h-9.5 w-full rounded-sm">
                  <DateInput className="flex-1 text-[13px]">
                    {(segment) => (
                      <DateSegment
                        className="react-aria-DateSegment focus:outline-none"
                        segment={segment}
                      />
                    )}
                  </DateInput>
                  <RACButton className="react-aria-Button mr-1 border-0 p-1 outline-none">
                    <CalenderIcon className="h-4 w-4 text-neutral-500" />
                  </RACButton>
                </Group>
                <Popover className="react-aria-Popover w-max">
                  <Dialog className="p-0 outline-none">
                    <Calendar />
                  </Dialog>
                </Popover>
              </DatePicker>
            </FormField>

            <TextFormField
              defaultValue={rftStore.report_amendednotes}
              name="report_amendednotes"
            >
              <Label>Amended notes</Label>
              <TextArea
                placeholder="Enter notes here..."
                className="field-sizing-content min-h-[4em] rounded-sm"
              />
            </TextFormField>
          </div>

          <div className="-mx-6 grid grid-cols-2 gap-2 border-t border-gray-200 px-6 pt-4">
            <Button
              variant="outlined"
              className="w-full"
              onPress={() => setOpenState(false)}
            >
              Cancel
            </Button>
            <Button
              className="w-full"
              type="submit"
            >
              Amend
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
});
