import { useParams } from 'react-router';
import { useQuery, useSubscription } from '@apollo/client';
import { RFT_QUERY, rftSubscription } from '../queries';

/**
 * RftBreadcrumb - A component that displays the test type name in the breadcrumb
 * instead of the generic "RFT Study" text.
 *
 * Uses GraphQL query to fetch RFT testtype and subscription to ensure
 * updates are reflected when testtype changes due to updateRftNameReaction.
 */
const RftBreadcrumb = () => {
  const params = useParams<{ id: string }>();
  const id = Object.values(params)[0].split('/').at(-1);

  // Use query to fetch initial RFT data
  const { data: queryData } = useQuery(RFT_QUERY, {
    variables: { id: +id! },
    // skip: !id,
    // fetchPolicy: 'cache-and-network',
  });

  console.log('queryData: ', queryData);

  // Use subscription to get real-time updates
  const { data: subscriptionData } = useSubscription(rftSubscription, {
    variables: { id: +id! },
    // skip: !id,
  });

  console.log('subscriptionData: ', subscriptionData);

  // Use the most recent data (subscription data takes precedence)
  const currentData = subscriptionData?.rft_routine?.[0] || queryData?.rft_routine?.[0];
  const testType = currentData?.testtype;
  console.log('testtype: ', testType);

  return testType ?? 'RFT Study';
};

export default RftBreadcrumb;
