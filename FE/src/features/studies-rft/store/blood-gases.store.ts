import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';
import {applyMixins} from '@/lib/mixin.ts';
import {DisposableStoreMixin} from '@/store/disposable.store.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  ComputedParameterValue,
  DisposableStoreWithUpsert,
  ParameterContext,
  ParameterValue,
  RefType,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';

class BloodGases {
  constructor(
    public values: ParameterValue[],
    public sampleType: string,
    public fio2: string
  ) {
    makeAutoObservable(this);
  }

  setProperty<K extends keyof BloodGases>(key: K, value: BloodGases[K]) {
    (this[key] as BloodGases[K]) = value;
  }
}

export class BloodGasesStore implements DisposableStoreWithUpsert {
  public result1: BloodGases;
  public result2: BloodGases;

  private mutationPromise?: Promise<any>;

  constructor(public rftStore: RftStore) {
    makeAutoObservable(this);

    // Create parameters
    const ph = Parameter.getOrCreate({description: 'pH'});
    const paco2 = Parameter.getOrCreate({description: 'PaCO2'});
    const pao2 = Parameter.getOrCreate({description: 'PaO2'});
    const hco3 = Parameter.getOrCreate({description: 'HCO3'});
    const be = Parameter.getOrCreate({description: 'BE'});
    const sao2 = Parameter.getOrCreate({description: 'SaO2'});
    const spo2 = Parameter.getOrCreate({description: 'SpO2'});
    const cohb = Parameter.getOrCreate({description: 'COHb'});
    const aaPo2 = Parameter.getOrCreate({description: 'A-aPO2'});
    const shunt = Parameter.getOrCreate({description: 'Shunt'});

    // Create reference contexts
    const phContext = new ParameterContext(7.35, 7.45, 7.4, 0.03);
    const paco2Context = new ParameterContext(36.0, 44.0, 40.0, 2.0);
    const pao2Context = new ParameterContext(73.1, undefined, 85.0, 6.0);
    const hco3Context = new ParameterContext(20.0, 26.0, 24.0, 2.0);
    const beContext = new ParameterContext(-3.3, 2.3, 0.0, 1.5);
    const sao2Context = new ParameterContext(95.0, 99.0, 97.0, 1.0);
    const spo2Context = new ParameterContext(95, 99, 97, 1);
    const aaPo2Context = new ParameterContext(undefined, 31.2, 15.0, 8.0);
    const shuntContext = new ParameterContext(undefined, 7.5, 3.0, 2.0);

    // Create parameter values with their contexts and reference types
    const result1Ph = new ParameterValue(ph, phContext, RefType.RANGE, this, 'r_abg1_ph');
    const result1Paco2 = new ParameterValue(paco2, paco2Context, RefType.RANGE, this, 'r_abg1_paco2');
    const result1Pao2 = new ParameterValue(pao2, pao2Context, RefType.LLN, this, 'r_abg1_pao2');
    const result1Hco3 = new ParameterValue(hco3, hco3Context, RefType.RANGE, this, 'r_abg1_hco3');
    const result1Be = new ParameterValue(be, beContext, RefType.RANGE, this, 'r_abg1_be');
    const result1Sao2 = new ParameterValue(sao2, sao2Context, RefType.RANGE, this, 'r_abg1_sao2');
    const result1Spo2 = new ParameterValue(spo2, spo2Context, RefType.RANGE, this, 'r_spo2_1');
    const result1Cohb = new ParameterValue(cohb, new ParameterContext(), RefType.NO_REF, this, 'r_abg1_cohb');
    const result1AaPo2 = new ComputedParameterValue(
      () => calculateAaPO2(result1Pao2.result, result1Paco2.result, this.result1.fio2),
      aaPo2,
      aaPo2Context,
      RefType.ULN,
      this,
      'r_abg1_aapo2',
      false
    );
    const result1Shunt = new ComputedParameterValue(
      () => calculateShunt(result1Pao2.result, result1Paco2.result, this.result1.fio2),
      shunt,
      shuntContext,
      RefType.ULN,
      this,
      'r_abg1_shunt',
      false
    );

    // Create parameter values for result2
    const result2Ph = new ParameterValue(ph, phContext, RefType.RANGE, this, 'r_abg2_ph');
    const result2Paco2 = new ParameterValue(paco2, paco2Context, RefType.RANGE, this, 'r_abg2_paco2');
    const result2Pao2 = new ParameterValue(pao2, pao2Context, RefType.LLN, this, 'r_abg2_pao2');
    const result2Hco3 = new ParameterValue(hco3, hco3Context, RefType.RANGE, this, 'r_abg2_hco3');
    const result2Be = new ParameterValue(be, beContext, RefType.RANGE, this, 'r_abg2_be');
    const result2Sao2 = new ParameterValue(sao2, sao2Context, RefType.RANGE, this, 'r_abg2_sao2');
    const result2Spo2 = new ParameterValue(spo2, spo2Context, RefType.RANGE, this, 'r_spo2_2');
    const result2Cohb = new ParameterValue(cohb, new ParameterContext(), RefType.NO_REF, this, 'r_abg2_cohb');
    const result2AaPo2 = new ComputedParameterValue(
      () => calculateAaPO2(result2Pao2.result, result2Paco2.result, this.result2.fio2),
      aaPo2,
      aaPo2Context,
      RefType.ULN,
      this,
      'r_abg2_aapo2',
      false
    );
    const result2Shunt = new ComputedParameterValue(
      () => calculateShunt(result2Pao2.result, result2Paco2.result, this.result2.fio2),
      shunt,
      shuntContext,
      RefType.ULN,
      this,
      'r_abg2_shunt',
      false
    );

    this.result1 = new BloodGases(
      [
        result1Ph,
        result1Paco2,
        result1Pao2,
        result1Hco3,
        result1Be,
        result1Sao2,
        result1Spo2,
        result1Cohb,
        result1AaPo2,
        result1Shunt,
      ],
      'Arterial',
      'Air'
    );

    this.result2 = new BloodGases(
      [
        result2Ph,
        result2Paco2,
        result2Pao2,
        result2Hco3,
        result2Be,
        result2Sao2,
        result2Spo2,
        result2Cohb,
        result2AaPo2,
        result2Shunt,
      ],
      '',
      ''
    );
  }

  get completedTestName() {
    const completed = [];

    if (
      (!!this.getResult1Value('PaO2')?.result && !!this.getResult1Value('PaCO2')?.result) ||
      (!!this.getResult2Value('PaO2')?.result && !!this.getResult2Value('PaCO2')?.result)
    ) {
      completed.push('ABG');
    }

    if (!!this.getResult1Value('Shunt')?.result || !!this.getResult2Value('Shunt')?.result) {
      completed.push('Sh');
    }

    if (!!this.getResult1Value('SpO2')?.result || !!this.getResult2Value('SpO2')?.result) {
      completed.push('Ox');
    }

    return completed.join(' ');
  }

  setProperty<K extends keyof BloodGasesStore>(key: K, value: BloodGasesStore[K]) {
    (this[key] as BloodGasesStore[K]) = value;
  }

  getResult1Value(paramName: string) {
    return this.result1.values.find((v) => v.parameter?.description === paramName);
  }

  getResult2Value(paramName: string) {
    return this.result2.values.find((v) => v.parameter?.description === paramName);
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash = false) {
    // Result 1 values
    if (safeParseFloat(data.r_abg1_ph)) {
      this.result1.values[0].setResult(safeParseFloat(data.r_abg1_ph), flash);
    }
    if (safeParseFloat(data.r_abg1_paco2)) {
      this.result1.values[1].setResult(safeParseFloat(data.r_abg1_paco2), flash);
    }
    if (safeParseFloat(data.r_abg1_pao2)) {
      this.result1.values[2].setResult(safeParseFloat(data.r_abg1_pao2), flash);
    }
    if (safeParseFloat(data.r_abg1_hco3)) {
      this.result1.values[3].setResult(safeParseFloat(data.r_abg1_hco3), flash);
    }
    if (safeParseFloat(data.r_abg1_be)) {
      this.result1.values[4].setResult(safeParseFloat(data.r_abg1_be), flash);
    }
    if (safeParseFloat(data.r_abg1_sao2)) {
      this.result1.values[5].setResult(safeParseFloat(data.r_abg1_sao2), flash);
    }
    if (safeParseFloat(data.r_spo2_1)) {
      this.result1.values[6].setResult(safeParseFloat(data.r_spo2_1), flash);
    }
    if (safeParseFloat(data.r_abg1_cohb)) {
      this.result1.values[7].setResult(safeParseFloat(data.r_abg1_cohb), flash);
    }
    if (safeParseFloat(data.r_abg1_aapo2)) {
      this.result1.values[8].setResult(safeParseFloat(data.r_abg1_aapo2), flash);
    }
    if (safeParseFloat(data.r_abg1_shunt)) {
      this.result1.values[9].setResult(safeParseFloat(data.r_abg1_shunt), flash);
    }

    // Result 2 values
    if (safeParseFloat(data.r_abg2_ph)) {
      this.result2.values[0].setResult(safeParseFloat(data.r_abg2_ph), flash);
    }
    if (safeParseFloat(data.r_abg2_paco2)) {
      this.result2.values[1].setResult(safeParseFloat(data.r_abg2_paco2), flash);
    }
    if (safeParseFloat(data.r_abg2_pao2)) {
      this.result2.values[2].setResult(safeParseFloat(data.r_abg2_pao2), flash);
    }
    if (safeParseFloat(data.r_abg2_hco3)) {
      this.result2.values[3].setResult(safeParseFloat(data.r_abg2_hco3), flash);
    }
    if (safeParseFloat(data.r_abg2_be)) {
      this.result2.values[4].setResult(safeParseFloat(data.r_abg2_be), flash);
    }
    if (safeParseFloat(data.r_abg2_sao2)) {
      this.result2.values[5].setResult(safeParseFloat(data.r_abg2_sao2), flash);
    }
    if (safeParseFloat(data.r_spo2_2)) {
      this.result2.values[6].setResult(safeParseFloat(data.r_spo2_2), flash);
    }
    if (safeParseFloat(data.r_abg2_cohb)) {
      this.result2.values[7].setResult(safeParseFloat(data.r_abg2_cohb), flash);
    }
    if (safeParseFloat(data.r_abg2_aapo2)) {
      this.result2.values[8].setResult(safeParseFloat(data.r_abg2_aapo2), flash);
    }
    if (safeParseFloat(data.r_abg2_shunt)) {
      this.result2.values[9].setResult(safeParseFloat(data.r_abg2_shunt), flash);
    }

    // Update sample types and FiO2 values
    if (data.r_abg1_sampletype) {
      this.result1.sampleType = data.r_abg1_sampletype;
    }
    if (data.r_abg1_fio2) {
      this.result1.fio2 = data.r_abg1_fio2;
    }
    if (data.r_abg2_sampletype) {
      this.result2.sampleType = data.r_abg2_sampletype;
    }
    if (data.r_abg2_fio2) {
      this.result2.fio2 = data.r_abg2_fio2;
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_abg1_ph?: string | null;
      r_abg1_paco2?: string | null;
      r_abg1_pao2?: string | null;
      r_abg1_hco3?: string | null;
      r_abg1_be?: string | null;
      r_abg1_sao2?: string | null;
      r_spo2_1?: string | null;
      r_abg1_cohb?: string | null;
      r_abg1_aapo2?: string | null;
      r_abg1_shunt?: string | null;
      r_abg2_ph?: string | null;
      r_abg2_paco2?: string | null;
      r_abg2_pao2?: string | null;
      r_abg2_hco3?: string | null;
      r_abg2_be?: string | null;
      r_abg2_sao2?: string | null;
      r_spo2_2?: string | null;
      r_abg2_cohb?: string | null;
      r_abg2_aapo2?: string | null;
      r_abg2_shunt?: string | null;
      r_abg1_sampletype?: string | null;
      r_abg1_fio2?: string | null;
      r_abg2_sampletype?: string | null;
      r_abg2_fio2?: string | null;
    }

    const updateData: UpdateInput = {};

    // Result 1 values
    if (!field || field === 'r_abg1_ph') {
      updateData.r_abg1_ph = this.result1.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_paco2') {
      updateData.r_abg1_paco2 = this.result1.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_pao2') {
      updateData.r_abg1_pao2 = this.result1.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_hco3') {
      updateData.r_abg1_hco3 = this.result1.values[3].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_be') {
      updateData.r_abg1_be = this.result1.values[4].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_sao2') {
      updateData.r_abg1_sao2 = this.result1.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_spo2_1') {
      updateData.r_spo2_1 = this.result1.values[6].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_cohb') {
      updateData.r_abg1_cohb = this.result1.values[7].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_aapo2') {
      updateData.r_abg1_aapo2 = this.result1.values[8].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg1_shunt') {
      updateData.r_abg1_shunt = this.result1.values[9].result?.toString() ?? null;
    }

    // Result 2 values
    if (!field || field === 'r_abg2_ph') {
      updateData.r_abg2_ph = this.result2.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_paco2') {
      updateData.r_abg2_paco2 = this.result2.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_pao2') {
      updateData.r_abg2_pao2 = this.result2.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_hco3') {
      updateData.r_abg2_hco3 = this.result2.values[3].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_be') {
      updateData.r_abg2_be = this.result2.values[4].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_sao2') {
      updateData.r_abg2_sao2 = this.result2.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_spo2_2') {
      updateData.r_spo2_2 = this.result2.values[6].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_cohb') {
      updateData.r_abg2_cohb = this.result2.values[7].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_aapo2') {
      updateData.r_abg2_aapo2 = this.result2.values[8].result?.toString() ?? null;
    }
    if (!field || field === 'r_abg2_shunt') {
      updateData.r_abg2_shunt = this.result2.values[9].result?.toString() ?? null;
    }

    // Sample types and FiO2 values
    if (!field || field === 'r_abg1_sampletype') {
      updateData.r_abg1_sampletype = this.result1.sampleType;
    }
    if (!field || field === 'r_abg1_fio2') {
      updateData.r_abg1_fio2 = this.result1.fio2;
    }
    if (!field || field === 'r_abg2_sampletype') {
      updateData.r_abg2_sampletype = this.result2.sampleType;
    }
    if (!field || field === 'r_abg2_fio2') {
      updateData.r_abg2_fio2 = this.result2.fio2;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}

function calculateAaPO2(paO2: number | undefined, paCO2: number | undefined, fiO2: string | undefined) {
  // todo: handle traditional units
  if (!paCO2 || !paO2 || !fiO2 || fiO2.toLowerCase() !== 'air') return undefined;
  if (paCO2 <= 0 || paO2 <= 0) return undefined;

  const A = 0.2095 * 713 - paCO2 / 0.8;
  return A - paO2;
}

function calculateShunt(paO2: number | undefined, paCO2: number | undefined, fiO2: string | undefined) {
  // todo: handle traditional units
  if (!paCO2 || !paO2 || !fiO2 || !fiO2.toLowerCase().includes('100')) return undefined;
  if (paCO2 <= 0 || paO2 <= 0) return undefined;

  const shunt = paO2 + paCO2;
  return (100 * (713 - shunt)) / (2305 - shunt);
}

export interface BloodGasesStore extends DisposableStoreMixin {}

applyMixins(BloodGasesStore, [DisposableStoreMixin]);
