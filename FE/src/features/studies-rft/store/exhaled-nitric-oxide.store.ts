import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  ParameterContext,
  ParameterValue,
  RefType,
  DisposableStoreWithUpsert,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';
import {DisposableStoreMixin} from "@/store/disposable.store.ts";
import {applyMixins} from "@/lib/mixin.ts";

class ExhaledNitricOxide {
  constructor(public values: ParameterValue[]) {}
}

export class ExhaledNitricOxideStore implements DisposableStoreWithUpsert {
  public baseline: ExhaledNitricOxide;
  condition: string | null = null;

  private mutationPromise?: Promise<any>;

  constructor(public rftStore: RftStore) {
    makeAutoObservable(this);

    const feno = Parameter.getOrCreate({description: 'FeNO'});

    const baselineFeno = new ParameterValue(
      feno,
      new ParameterContext(undefined, 25.0, undefined, undefined),
      RefType.ULN,
      this,
      'r_bl_feno'
    );
    this.baseline = new ExhaledNitricOxide([baselineFeno]);
  }

  get completedTestName() {
    if (!!this.getParamValue('FeNO')?.result) {
      return 'FeNO';
    }
  }

  getParamValue(paramName: string) {
    return this.baseline.values.find((v) => v.parameter?.description === paramName);
  }

  setProperty<K extends keyof ExhaledNitricOxideStore>(key: K, value: ExhaledNitricOxideStore[K]) {
    (this[key] as ExhaledNitricOxideStore[K]) = value;
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash=false) {
    if (data.r_condition_feno) {
      this.setProperty('condition', data.r_condition_feno);
    }

    if (safeParseFloat(data.r_bl_feno)) {
      this.baseline.values[0].setResult(safeParseFloat(data.r_bl_feno), flash);
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_condition_feno?: string | null;
      r_bl_feno?: string | null;
    }

    const updateData: UpdateInput = {};

    if (!field || field === 'r_condition_feno') {
      updateData.r_condition_feno = this.condition;
    }
    if (!field || field === 'r_bl_feno') {
      updateData.r_bl_feno = this.baseline.values[0].result?.toString() ?? null;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}

export interface ExhaledNitricOxideStore extends DisposableStoreMixin {}
applyMixins(ExhaledNitricOxideStore, [DisposableStoreMixin]);
