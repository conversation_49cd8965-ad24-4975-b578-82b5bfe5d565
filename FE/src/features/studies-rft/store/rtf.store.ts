import {asyncDebounce} from '@tanstack/react-pacer/async-debouncer';
import {isBefore, parseISO} from 'date-fns';
import {makeAutoObservable, reaction} from 'mobx';
import hash from 'stable-hash';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client';
import {RFT_QUERY, UPDATE_RFT_ROUTINE, rftSubscription} from '@/features/studies-rft/queries.ts';
import {BloodGasesStore} from '@/features/studies-rft/store/blood-gases.store.ts';
import {COTransferStore} from '@/features/studies-rft/store/co-transfer.store.ts';
import {ExhaledNitricOxideStore} from '@/features/studies-rft/store/exhaled-nitric-oxide.store.ts';
import {FlowVolumeStore} from '@/features/studies-rft/store/flow-volume.store.ts';
import {LungVolumesStore} from '@/features/studies-rft/store/lung-volumes.store.ts';
import {MRPsStore} from '@/features/studies-rft/store/mrps.store.ts';
import {RftSession} from '@/features/studies-rft/store/rft-session.store.ts';
import {SpirometryStore} from '@/features/studies-rft/store/spirometry.store.ts';
import {RftTestQuality} from '@/features/studies-rft/store/test-quality.store.ts';
import {EquationLoadMethod} from '@/graphql/equations.ts';
import type {ParamEquationMetadata} from '@/store/parameter.ts';
import {assignDefinedProps, fetchAndObserveQuery} from '@/store/utils.ts';
import authStore from "@/store/auth.store.ts";

export enum FerMethod {
  ERS_ATS_2021 = '2021',
  pre_ERS_ATS_2021 = 'pre2021',
}

export class RftStore {
  rftid!: number;
  sessionid!: number;
  patientid!: number;
  testtime?: string;
  testtype?: string;
  lab?: string;
  report_text?: string;
  report_reportedby?: string;
  report_reporteddate?: string;
  report_verifiedby?: string;
  report_verifieddate?: string;
  report_authorisedby?: string;
  report_authoriseddate?: string;
  report_amendedby?: string;
  report_amendeddate?: string;
  report_amendednotes?: string;
  report_status?: string;
  bdstatus?: string;
  technicalnotes?: string;
  scientist?: string;
  lastupdated_rft?: string;
  lastupdatedby_rft?: string;
  sample_type?: string;
  device_info?: string;

  testSession?: RftSession;
  rftTestQuality: RftTestQuality[] = [];

  isEditing = true;

  spirometryStore: SpirometryStore;
  coTransferStore: COTransferStore;
  bloodGasesStore: BloodGasesStore;
  lungVolumesStore: LungVolumesStore;
  exhaledNitricOxideStore: ExhaledNitricOxideStore;
  mrpsStore: MRPsStore;
  flowVolumeStore: FlowVolumeStore;

  autoUpsert = true;
  autoFetch = true;

  private mutationPromise?: Promise<any>;
  private disposers: (() => void)[] = [];

  upsertStore: () => Promise<any>;

  constructor(public props: Partial<PropertiesOnly<RftStore>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);

    this.updateRftNameReaction();

    if (this.rftid && this.autoFetch) {
      this.init();
    }

    this.spirometryStore = new SpirometryStore(this);
    this.coTransferStore = new COTransferStore(this, this.spirometryStore);
    this.lungVolumesStore = new LungVolumesStore(this);
    this.exhaledNitricOxideStore = new ExhaledNitricOxideStore(this);
    this.mrpsStore = new MRPsStore(this);
    this.bloodGasesStore = new BloodGasesStore(this);
    this.flowVolumeStore = new FlowVolumeStore(this);

    this.disposers.push(() => this.spirometryStore.dispose());
    this.disposers.push(() => this.coTransferStore.dispose());
    this.disposers.push(() => this.lungVolumesStore.dispose());
    this.disposers.push(() => this.exhaledNitricOxideStore.dispose());
    this.disposers.push(() => this.mrpsStore.dispose());
    this.disposers.push(() => this.bloodGasesStore.dispose());
    this.disposers.push(() => this.flowVolumeStore.dispose());

    this.upsertStore = asyncDebounce(
      async () => {
        if (!this.autoUpsert) return;
        if (this.mutationPromise) {
          await this.mutationPromise;
        }

        this.mutationPromise = apolloClient.mutate({
          mutation: UPDATE_RFT_ROUTINE,
          variables: {
            id: this.rftid,
            inp: this.toUpsetObject(),
          },
          refetchQueries: [RFT_QUERY],
        });

        this.mutationPromise.finally(() => {
          this.mutationPromise = undefined;
        });
      },
      {wait: 500}
    ).bind(this);

    this.disposers.push(reaction(() => this.stableHash(), this.upsertStore));
  }

  private updateRftNameReaction() {
    const r = reaction(
      () =>
        [
          this.spirometryStore?.completedTestName,
          this.coTransferStore?.completedTestName,
          this.lungVolumesStore?.completedTestName,
          this.exhaledNitricOxideStore?.completedTestName,
          this.mrpsStore?.completedTestName,
          this.bloodGasesStore?.completedTestName,
        ] as const,
      (completedTestsName) => {
        const completed = completedTestsName.filter(Boolean);
        this.testtype = completed.length > 0 ? `RFTs (${completed.join(' ')})` : 'RFTs';
      }
    );
    this.disposers.push(r);
  }

  get predMetadata() {
    if (
      !this.testSession?.patient?.genderForRftResolved ||
      !this.testSession?.patient?.ethnicityForRftResolved ||
      !this.testSession?.ageAtTest ||
      !this.testSession?.height ||
      !this.testSession?.weight
    ) {
      return undefined;
    }

    return {
      gender_for_rfts_id: this.testSession?.patient?.genderForRftResolved?.id,
      gender_for_rfts: this.testSession?.patient?.genderForRftResolved?.gender,
      ethnicity_id: this.testSession?.patient?.ethnicityForRftResolved?.id,
      ethnicity: this.testSession?.patient?.ethnicityForRftResolved?.description,
      age: this.testSession?.ageAtTest,
      Htcm: +this.testSession?.height,
      Wtkg: +this.testSession?.weight,
      testdate: this.testSession?.testdate ? parseISO(this.testSession?.testdate) : new Date(),
      load_method: EquationLoadMethod.UseSourcesInUseAtTestDate,
      dob: this.testSession?.patient?.dob ? parseISO(this.testSession?.patient?.dob) : undefined,
    } satisfies Omit<ParamEquationMetadata, 'testid'>;
  }

  init() {
    const localSubscription = fetchAndObserveQuery(
      {
        query: RFT_QUERY,
        variables: {id: this.rftid},
      },
      (data) => {
        const rftRoutine = data.rft_routine?.[0];
        this.autoUpsert = false;
        this.fromQueryResult(rftRoutine);
        this.autoUpsert = true;
      }
    );
    this.disposers.push(() => localSubscription.unsubscribe());

    const subscription = apolloClient
      .subscribe({
        query: rftSubscription,
        variables: {id: this.rftid},
      })
      .subscribe({
        next: ({data}) => {
          if (!data) return;

          this.flowVolumeStore?.loadData?.();

          this.autoUpsert = false;
          this.fromQueryResult(data?.rft_routine?.[0], true);
          this.autoUpsert = true;
        },
      });

    this.disposers.push(() => subscription.unsubscribe());

    // Return a function that can be used to dispose the store
    return () => this.dispose();
  }

  setProperty<K extends keyof RftStore>(key: K, value: RftStore[K]) {
    this[key] = value as any;
  }

  fromQueryResult(data?: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash = false) {
    if (!data) return;
    assignDefinedProps(this, data as any, this.setProperty.bind(this) as any);

    this.rftTestQuality = Array.from(
      {length: 10},
      (_, i) => new RftTestQuality({param_id: i + 1, test_id: this.rftid})
    );

    data.rft_test_quality?.forEach((quality) => {
      let existingQuality = this.rftTestQuality.find((q) => q.param_id === quality.param_id);
      if (!existingQuality) {
        existingQuality = new RftTestQuality(quality as any);
        this.rftTestQuality.push(existingQuality);
      }
      assignDefinedProps(
        existingQuality,
        quality as any,
        existingQuality.setProperty.bind(existingQuality) as any
      );
    });

    if (data.r_session && !this.testSession) {
      this.testSession = new RftSession(data.r_session as any);
      this.disposers.push(() => this.testSession?.dispose());
    }

    this.spirometryStore.fromQueryResult(data, flash);
    this.coTransferStore.fromQueryResult(data, flash);
    this.lungVolumesStore.fromQueryResult(data, flash);
    this.exhaledNitricOxideStore.fromQueryResult(data, flash);
    this.mrpsStore.fromQueryResult(data, flash);
    this.bloodGasesStore.fromQueryResult(data, flash);
  }

  private toUpsetObject() {
    return {
      report_status: this.report_status || 'Unreported',
      report_text: this.report_text,
      report_reportedby: this.report_reportedby,
      report_reporteddate: this.report_reporteddate,
      report_verifiedby: this.report_verifiedby,
      report_verifieddate: this.report_verifieddate,
      report_authorisedby: this.report_authorisedby,
      report_authoriseddate: this.report_authoriseddate,
      report_amendedby: this.report_amendedby,
      report_amendeddate: this.report_amendeddate,
      report_amendednotes: this.report_amendednotes,
      technicalnotes: this.technicalnotes,

      scientist: this.scientist,
      testtime: this.testtime,
      lab: this.lab,
      bdstatus: this.bdstatus,

      testtype: this.testtype,
    };
  }

  get fer(): [label: string, value: number] {
    if (!this.testSession) return ['fev1/fvc', 0] as const;

    let method = FerMethod.ERS_ATS_2021;
    const changeOverDate = authStore.site?.getConfig('report_options')?.fer_calc_changeover_date
      ? new Date(authStore.site?.getConfig('report_options')?.fer_calc_changeover_date!)
      : undefined;
    const testDate = this.testSession.testdate ? new Date(this.testSession.testdate) : undefined;

    if (testDate && changeOverDate) {
      if (isBefore(testDate, changeOverDate)) {
        method = FerMethod.pre_ERS_ATS_2021;
      } else {
        method = FerMethod.ERS_ATS_2021;
      }
    }

    const fev1 = this.spirometryStore.getSp1Value('FEV1')?.result || 0;
    const fvc = this.spirometryStore.getSp1Value('FVC')?.result || 0;
    const vc = this.spirometryStore.getSp1Value('VC')?.result || 0;

    if (!(fvc > 0 && vc > 0)) {
      const fer = (100 * fev1) / (fvc || vc);
      const label = fvc > 0 ? 'fev1/fvc' : 'fev1/vc';
      return [label, fer] as const;
    }

    if (method === FerMethod.pre_ERS_ATS_2021) {
      return [vc > fvc ? 'fev1/vc' : 'fev1/fvc', (fev1 / Math.max(fvc, vc)) * 100] as const;
    }

    return ['fev1/fvc', (100 * fev1) / fvc] as const;
  }

  get isAmendable() {
    // todo: add check for user permissions
    return !this.isEditing && this.report_status?.toLowerCase() === 'completed';
  }

  get isReportEditable() {
    return this.report_status?.toLowerCase() !== 'completed' || this.isEditing;
  }

  private stableHash() {
    return hash(this.toUpsetObject());
  }

  dispose() {
    this.rftTestQuality.forEach((quality) => quality.dispose());
    this.disposers.forEach((d) => d?.());
  }
}
