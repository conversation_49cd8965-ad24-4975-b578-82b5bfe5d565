import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  ParameterContext,
  ParameterValue,
  RefType,
  DisposableStoreWithUpsert,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';
import {DisposableStoreMixin} from "@/store/disposable.store.ts";
import {applyMixins} from "@/lib/mixin.ts";

class MRPs {
  constructor(public values: ParameterValue[]) {}
}

export class MRPsStore implements DisposableStoreWithUpsert {
  public baseline: MRPs;
  condition: string | null = null;

  private mutationPromise?: Promise<any>;

  constructor(public rftStore: RftStore) {
    makeAutoObservable(this);

    // Create parameters
    const mip = Parameter.getOrCreate({description: 'MIP'});
    const mep = Parameter.getOrCreate({description: 'MEP'});
    const snip = Parameter.getOrCreate({description: 'SNIP'});

    // Initialize with the values
    const baselineMip = new ParameterValue(
      mip,
      new ParameterContext(40.3, undefined, 84.0, 20.0),
      RefType.LLN,
      this,
      'r_bl_mip'
    );
    const baselineMep = new ParameterValue(
      mep,
      new ParameterContext(92.0, undefined, 135.0, 25.0),
      RefType.LLN,
      this,
      'r_bl_mep'
    );
    const baselineSnip = new ParameterValue(
      snip,
      new ParameterContext(60.5, undefined, 98.0, 18.0),
      RefType.LLN,
      this,
      'r_bl_snip'
    );

    this.baseline = new MRPs([baselineMip, baselineMep, baselineSnip]);
  }

  get completedTestName() {
    if (!!this.getParamValue('MIP')?.result || !!this.getParamValue('MEP')?.result || !!this.getParamValue('SNIP')?.result) {
      return 'MRP';
    }
  }

  getParamValue(paramName: string) {
    return this.baseline.values.find((v) => v.parameter?.description === paramName);
  }

  setProperty<K extends keyof MRPsStore>(key: K, value: MRPsStore[K]) {
    (this[key] as MRPsStore[K]) = value;
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash=false) {
    if (data.r_condition_mrp) {
      this.setProperty('condition', data.r_condition_mrp);
    }

    if (safeParseFloat(data.r_bl_mip)) {
      this.baseline.values[0].setResult(safeParseFloat(data.r_bl_mip), flash);
    }

    if (safeParseFloat(data.r_bl_mep)) {
      this.baseline.values[1].setResult(safeParseFloat(data.r_bl_mep), flash);
    }

    if (safeParseFloat(data.r_bl_snip)) {
      this.baseline.values[2].setResult(safeParseFloat(data.r_bl_snip), flash);
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_condition_mrp?: string | null;
      r_bl_mip?: string | null;
      r_bl_mep?: string | null;
      r_bl_snip?: string | null;
    }

    const updateData: UpdateInput = {};

    if (!field || field === 'r_condition_mrp') {
      updateData.r_condition_mrp = this.condition;
    }
    if (!field || field === 'r_bl_mip') {
      updateData.r_bl_mip = this.baseline.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_mep') {
      updateData.r_bl_mep = this.baseline.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_snip') {
      updateData.r_bl_snip = this.baseline.values[2].result?.toString() ?? null;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}

export interface MRPsStore extends DisposableStoreMixin {}
applyMixins(MRPsStore, [DisposableStoreMixin]);
