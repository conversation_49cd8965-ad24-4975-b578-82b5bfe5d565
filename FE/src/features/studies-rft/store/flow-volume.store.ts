import {makeAutoObservable} from 'mobx';

import {apolloClient} from '@/apollo-client.ts';
import {rftFlowImage, updateFlowVolumeImageMutation} from '@/features/studies-rft/queries.ts';
import {type RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {fetchAndObserveQuery} from "@/store/utils.ts";

export class FlowVolumeStore {
  hexValue?: string;
  private cacheHexValue?: string;
  private cachedBlobUrl?: string;

  isLoading?: boolean = false;

  private disposers: (() => void)[] = [];

  constructor(private rftStore: RftStore) {
    makeAutoObservable(this);

    this.isLoading = true;
    this.loadData();
  }

  loadData() {
    const localSubscription = fetchAndObserveQuery(
      {
        query: rftFlowImage,
        variables: {id: this.rftStore.rftid},
      },
      (data) => {
        this.setHexValue(data.rft_routine[0]?.flowvolloop as string | undefined);
        this.isLoading = false;
      }
    );

    this.disposers.push(() => localSubscription.unsubscribe());
  }

  get isCompleted() {
    return !!this.hexValue;
  }

  setHexValue(val?: string) {
    this.hexValue = val;
  }

  get imageUrl() {
    if (!this.hexValue) return null;

    if (this.hexValue === this.cacheHexValue && this.cachedBlobUrl) {
      return this.cachedBlobUrl;
    }

    if (this.cachedBlobUrl && this.cacheHexValue && this.cacheHexValue !== this.hexValue) {
      URL.revokeObjectURL(this.cachedBlobUrl);
    }

    const cachedBlob = this.convertHexToBlob(this.hexValue);
    if (cachedBlob) {
      this.cachedBlobUrl = URL.createObjectURL(cachedBlob);
      this.cacheHexValue = this.hexValue;
    }

    return this.cachedBlobUrl;
  }

  uploadImage(image: Blob) {
    this.isLoading = true;
    return this.hexDump(image)
      .then((hex) => {
        return apolloClient.mutate({
          mutation: updateFlowVolumeImageMutation,
          variables: {id: this.rftStore.rftid, img: hex},
          refetchQueries: [rftFlowImage],
        });
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  clearImage() {
    this.isLoading = true;
    return apolloClient.mutate({
      mutation: updateFlowVolumeImageMutation,
      variables: {id: this.rftStore.rftid, img: null},
      refetchQueries: [rftFlowImage],
    });
  }

  dispose() {
    this.disposers.forEach((d) => d?.());
    if (this.cachedBlobUrl) {
      URL.revokeObjectURL(this.cachedBlobUrl);
    }
  }

  private convertHexToBlob(hexValue: string) {
    // Handle empty input
    if (!hexValue || typeof hexValue !== 'string') {
      console.error('Invalid hex value provided');
      return null;
    }

    try {
      // Remove any non-hex characters
      const cleanHex = hexValue.replace(/[^A-Fa-f0-9]/g, '');

      // Check if we have valid hex (must have even length)
      if (cleanHex.length % 2 !== 0) {
        console.error('Hex string must have even length');
        return null;
      }

      // Convert hex to bytes
      const bytes = new Uint8Array(cleanHex.length / 2);

      for (let i = 0; i < cleanHex.length; i += 2) {
        bytes[i / 2] = parseInt(cleanHex.substring(i, i + 2), 16);
      }

      // Try to detect image type from magic bytes
      let mimeType = 'image/jpeg'; // Default

      // Check for common image format signatures
      if (bytes.length > 2) {
        const signature = bytes.slice(0, 4);

        // JPEG starts with FF D8 FF
        if (signature[0] === 0xff && signature[1] === 0xd8 && signature[2] === 0xff) {
          mimeType = 'image/jpeg';
        }
        // PNG starts with 89 50 4E 47
        else if (
          signature[0] === 0x89 &&
          signature[1] === 0x50 &&
          signature[2] === 0x4e &&
          signature[3] === 0x47
        ) {
          mimeType = 'image/png';
        }
        // GIF starts with 47 49 46 38
        else if (
          signature[0] === 0x47 &&
          signature[1] === 0x49 &&
          signature[2] === 0x46 &&
          signature[3] === 0x38
        ) {
          mimeType = 'image/gif';
        }
      }

      return new Blob([bytes], {type: mimeType});
    } catch (error) {
      console.error('Error converting hex to blob:', error);
      return null;
    }
  }

  private async hexDump(file: Blob) {
    function hex(buffer: ArrayBuffer) {
      return '\\x' + [...new Uint8Array(buffer)].map((x) => x.toString(16).padStart(2, '0')).join('');
    }

    return new Promise<string>((res) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result !== 'string' && reader.result) {
          res(hex(reader.result));
        }
      };
      reader.readAsArrayBuffer(file);
    });
  }
}
