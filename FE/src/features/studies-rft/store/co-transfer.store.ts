import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {Parameter} from '@/store/parameter.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';

import {type SpirometryStore} from './spirometry.store.ts';
import {
  ComputedParameterValue,
  ParameterContext,
  ParameterValue,
  RefType,
  DisposableStoreWithUpsert,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';
import {applyMixins} from "@/lib/mixin.ts";
import {DisposableStoreMixin} from "@/store/disposable.store.ts";

class COTransfer {
  constructor(public values: ParameterValue[]) {}
}

export class COTransferStore implements DisposableStoreWithUpsert {
  public baseline: COTransfer;
  private baselineHb: ParameterValue;

  condition?: string;
  private mutationPromise?: Promise<any>;

  constructor(
    public rftStore: RftStore,
    private spirometryStore: SpirometryStore
  ) {
    makeAutoObservable(this);

    const tlco = Parameter.getOrCreate({description: 'TLCO'});
    const tlcohb = new Parameter(tlco);
    tlcohb.description = 'TLCO(Hb)';
    const va = Parameter.getOrCreate({description: 'VA'});
    const kco = Parameter.getOrCreate({description: 'KCO'});
    const kcohb = new Parameter(kco);
    kcohb.description = 'KCO(Hb)';
    const vi = Parameter.getOrCreate({
      parameterid: 9999,
      description: 'Vi',
      units: 'L',
      units_si: 'L',
      longname: 'Inspired volume',
      decimalplaces: 2,
      decimalplaces_si: 2,
    });
    const hb = Parameter.getOrCreate({description: 'Hb'});
    const vivc = Parameter.getOrCreate({description: 'VI/VC'});

    const baselineTlco = new ParameterValue(tlco, new ParameterContext(17.9, 31.6, 24.2, 3.0), RefType.RANGE, this, 'r_bl_tlco');
    const baselineTlcohb = new ComputedParameterValue(
      () => {
        if (!this.hbFactor || !baselineTlco.result) return undefined;
        return this.hbFactor * baselineTlco.result;
      },
      tlcohb,
      new ParameterContext(17.9, 31.6, 24.2, 3.0),
      RefType.RANGE,
      this,
      'r_bl_tlcohb'
    );
    const baselineVa = new ParameterValue(va, new ParameterContext(4.68, undefined, 5.85, 0.6), RefType.LLN, this, 'r_bl_va');
    const baselineKco = new ComputedParameterValue(
      () => {
        if (!baselineTlco.result || !baselineVa.result) return undefined;
        return baselineTlco.result / baselineVa.result;
      },
      kco,
      new ParameterContext(3.13, 5.34, 4.2, 0.4),
      RefType.RANGE,
      this,
      'r_bl_kco'
    );
    const baselineKcohb = new ComputedParameterValue(
      () => {
        if (!this.hbFactor || !baselineKco.result) return undefined;
        return this.hbFactor * baselineKco.result;
      },
      kcohb,
      new ParameterContext(3.13, 5.34, 4.2, 0.4),
      RefType.RANGE,
      this,
      'r_bl_kcohb'
    );
    const baselineVi = new ParameterValue(vi, new ParameterContext(), RefType.NO_REF, this, 'r_bl_ivc');
    const baselineHb = new ParameterValue(hb, new ParameterContext(), RefType.NO_REF, this, 'r_bl_hb');
    const baselineVivc = new ComputedParameterValue(
      () => {
        const fvc = this.spirometryStore.sp1.values.find((v) => v.parameter?.description === 'FVC')?.result;
        const vc = this.spirometryStore.sp1.values.find((v) => v.parameter?.description === 'VC')?.result;
        const vi = baselineVi.result;

        const big = Math.max(...([fvc, vc].filter(Boolean) as number[]));
        if (big <= 0 || (vi ?? 0) <= 0) {
          return undefined;
        }

        return (100 * vi!) / big;
      },
      vivc,
      new ParameterContext(),
      RefType.NO_REF,
      this,
      'r_bl_vivc'
    );

    this.baselineHb = baselineHb;

    this.baseline = new COTransfer([
      baselineTlco,
      baselineTlcohb,
      baselineVa,
      baselineKco,
      baselineKcohb,
      baselineVi,
      baselineHb,
      baselineVivc,
    ]);
  }

  getParamValue(paramName: string) {
    return this.baseline.values.find((v) => v.parameter?.description === paramName);
  }

  setProperty<K extends keyof COTransferStore>(key: K, value: COTransferStore[K]) {
    (this[key] as COTransferStore[K]) = value;
  }

  get completedTestName() {
    if (!!this.getParamValue('TLCO')?.result) {
      return 'TL';
    }
  }

  get hbFactor() {
    const hb_in_tradUnits =
      (this.baselineHb?.result ?? 0) / (this.baselineHb?.parameter?.units_convert_trad_to_si ?? 1);
    if (!hb_in_tradUnits) return undefined;

    const hbFn = this.rftStore.testSession?.hbFn;
    return hbFn?.(hb_in_tradUnits);
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash=false) {
    if (data.r_condition_tl){
      this.setProperty('condition', data.r_condition_tl);
    }

    if (safeParseFloat(data.r_bl_tlco)) {
      this.baseline.values[0].setResult(safeParseFloat(data.r_bl_tlco), flash);
    }

    if (safeParseFloat(data.r_bl_va)) {
      this.baseline.values[2].setResult(safeParseFloat(data.r_bl_va), flash);
    }

    if (safeParseFloat(data.r_bl_kco)) {
      this.baseline.values[3].setResult(safeParseFloat(data.r_bl_kco), flash);
    }

    if (safeParseFloat(data.r_bl_ivc)) {
      this.baseline.values[5].setResult(safeParseFloat(data.r_bl_ivc), flash); // Vi
    }

    if (safeParseFloat(data.r_bl_hb)) {
      this.baseline.values[6].setResult(safeParseFloat(data.r_bl_hb), flash);
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_bl_tlco?: string | null;
      r_bl_tlcohb?: string | null;
      r_bl_va?: string | null;
      r_bl_kco?: string | null;
      r_bl_kcohb?: string | null;
      r_bl_ivc?: string | null;
      r_bl_hb?: string | null;
      r_bl_vivc?: string | null;
      r_condition_tl?: string | null;
    }

    const updateData: UpdateInput = {};

    if (!field || field === 'r_condition_tl') {
      updateData.r_condition_tl = this.condition;
    }
    if (!field || field === 'r_bl_tlco') {
      updateData.r_bl_tlco = this.baseline.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_tlcohb') {
      updateData.r_bl_tlcohb = this.baseline.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_va') {
      updateData.r_bl_va = this.baseline.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_kco') {
      updateData.r_bl_kco = this.baseline.values[3].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_kcohb') {
      updateData.r_bl_kcohb = this.baseline.values[4].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_ivc') {
      updateData.r_bl_ivc = this.baseline.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_hb') {
      updateData.r_bl_hb = this.baseline.values[6].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_vivc') {
      updateData.r_bl_vivc = this.baseline.values[7].result?.toString() ?? null;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}


export interface COTransferStore extends DisposableStoreMixin {}
applyMixins(COTransferStore, [DisposableStoreMixin]);
