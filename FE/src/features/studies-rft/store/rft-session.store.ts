import {differenceInCalendarMonths} from 'date-fns';
import {makeAutoObservable} from 'mobx';

import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client.ts';
import {getPatientsRSessionData} from '@/graphql/patients.ts';
import {Patient} from '@/store/patient.ts';
import {assignDefinedProps, fetchAndObserveQuery} from '@/store/utils.ts';

export class RftSession {
  sessionid!: number;
  patientid!: number;
  testdate?: string;
  lab?: string;
  height?: string;
  weight?: string;
  req_name?: string;
  req_address?: string;
  req_providernumber?: string;
  req_healthservice_text?: string;
  req_healthservice_code?: string;
  req_date?: Date;
  req_time?: string;
  req_phone?: string;
  req_fax?: string;
  req_email?: string;
  req_clinicalnotes?: string;
  smoke_hx?: string;
  smoke_cigsperday?: string;
  smoke_yearssmoked?: string;
  smoke_packyears?: string;
  smoke_last?: string;
  diagnosticcategory?: string;
  pred_sourceids?: string;
  admissionstatus?: string;
  report_copyto?: string;
  report_copyto_2?: string;
  billing_billedto?: string;
  billing_billingmo?: string;
  billing_billingmoproviderno?: string;
  lastupdated_session?: Date;
  lastupdatedby_session?: string;
  patient: Patient;

  private subscription?: ReturnType<ReturnType<typeof apolloClient.watchQuery>['subscribe']>;

  constructor(props: Partial<PropertiesOnly<RftSession>>, patient?: Patient) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);

    if (this.sessionid) {
      this.init();
    }

    if (!patient) {
      this.patient = new Patient({patientid: this.patientid});
    } else {
      this.patient = patient;
    }
  }

  init() {
    this.subscription = fetchAndObserveQuery(
      {
        query: getPatientsRSessionData,
        variables: {patientId: this.patientid},
      },
      (data) => {
        const testSession = data.r_sessions?.find((e) => e.sessionid === this.sessionid);
        if (testSession) {
          assignDefinedProps(this, testSession as any, this.setProperty.bind(this) as any);
        }
      }
    );
  }

  dispose() {
    this.subscription?.unsubscribe();
  }

  setProperty<K extends keyof RftSession>(key: K, value: RftSession[K]) {
    this[key] = value as any;
  }

  get ageAtTest() {
    if (!this.patient.dob) return undefined;
    const dob = new Date(this.patient.dob);
    const testDate = this.testdate ? new Date(this.testdate) : new Date();
    const months = differenceInCalendarMonths(testDate, dob);
    return months / 12;
  }

  get BMI() {
    if (!this.height || !this.weight) return;

    const patientHeight = parseFloat(this.height);
    const patientWeight = parseFloat(this.weight);

    if (!(patientHeight > 0 && patientWeight > 0)) {
      return undefined;
    }
    return (10000 * patientWeight) / (patientHeight * patientHeight);
  }

  get hbFn(): (hb: number) => number | undefined {
    const age = this.ageAtTest;
    const gender = this.patient.genderForRftResolved;

    if (!age || !gender) return () => undefined;

    if (gender.gender_code === 'M') {
      if (age >= 16 && age <= 120) return (hb: number) => (10.22 + hb) / (1.7 * hb);
      if (age >= 5 && age < 16) return (hb: number) => (9.38 + hb) / (1.7 * hb);

      return () => undefined;
    }

    if (gender.gender_code === 'F') {
      if (age >= 16 && age <= 120) return (hb: number) => (9.38 + hb) / (1.7 * hb);

      return () => undefined;
    }

    return () => undefined;
  }
}
