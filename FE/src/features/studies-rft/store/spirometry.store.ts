import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';
import {applyMixins} from '@/lib/mixin.ts';
import {DisposableStoreMixin} from '@/store/disposable.store.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  ComputedParameterValue,
  DisposableStoreWithUpsert,
  ParameterContext,
  ParameterValue,
  RefType,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';

class Spirometry {
  constructor(public values: ParameterValue[]) {}
}

export class SpirometryStore implements DisposableStoreWithUpsert {
  public sp1: Spirometry;
  public sp2: Spirometry;

  sp1Condition: string | null = null;
  sp2Condition: string | null = null;

  private mutationPromise?: Promise<any>;

  constructor(public rftStore: RftStore) {
    makeAutoObservable(this);

    const fev1 = Parameter.getOrCreate({description: 'FEV1'});
    const fvc = Parameter.getOrCreate({description: 'FVC'});
    const vc = Parameter.getOrCreate({description: 'VC'});
    const fev1_fvc = Parameter.getOrCreate({description: 'FER'});
    const fev1_vc = Parameter.getOrCreate({description: 'FEV1/VC'});
    const fef2575 = Parameter.getOrCreate({description: 'FEF2575'});
    const pef = Parameter.getOrCreate({description: 'PEF'});

    const sp1Fev1 = new ParameterValue(
      fev1,
      new ParameterContext(2.4, undefined, 3.4, 0.4),
      RefType.LLN,
      this,
      'r_bl_fev1'
    );

    const sp1Fvc = new ParameterValue(
      fvc,
      new ParameterContext(3.17, undefined, 4.32, 0.4),
      RefType.LLN,
      this,
      'r_bl_fvc'
    );
    const sp1Vc = new ParameterValue(
      vc,
      new ParameterContext(67.1, undefined, 4.32, 0.4),
      RefType.LLN,
      this,
      'r_bl_vc'
    );

    const sp1fef2525 = new ParameterValue(
      fef2575,
      new ParameterContext(2.4, undefined, 3.4, 0.4),
      RefType.LLN,
      this,
      'r_bl_fef2575'
    );

    const sp1pef = new ParameterValue(pef, new ParameterContext(), RefType.NO_REF, this, 'r_bl_pef');

    const sp2Fev1 = new ParameterValue(
      fev1,
      new ParameterContext(2.4, undefined, 3.4, 0.4),
      RefType.LLN,
      this,
      'r_post_fev1'
    );
    const sp2Fvc = new ParameterValue(
      fvc,
      new ParameterContext(3.17, undefined, 4.32, 0.4),
      RefType.LLN,
      this,
      'r_post_fvc'
    );
    const sp2Vc = new ParameterValue(
      vc,
      new ParameterContext(67.1, undefined, 4.32, 0.4),
      RefType.LLN,
      this,
      'r_post_vc'
    );

    const sp2fef2527 = new ParameterValue(
      fef2575,
      new ParameterContext(2.4, undefined, 3.4, 0.4),
      RefType.LLN,
      this,
      'r_post_fef2575'
    );

    const sp2pef = new ParameterValue(pef, new ParameterContext(), RefType.NO_REF, this, 'r_post_pef');

    this.sp1 = new Spirometry([
      sp1Fev1,
      sp1Fvc,
      sp1Vc,
      new ComputedParameterValue(
        () => (sp1Fvc.result && sp1Fev1.result ? (sp1Fev1.result / sp1Fvc.result) * 100 : undefined),
        fev1_fvc,
        new ParameterContext(67.1, undefined, 4.32, 0.4),
        RefType.LLN,
        this,
      ),
      new ComputedParameterValue(
        () => (sp1Vc.result && sp1Fev1.result ? (sp1Fev1.result / sp1Vc.result) * 100 : undefined),
        fev1_vc,
        new ParameterContext(),
        RefType.NO_REF,
        this
      ),
      sp1fef2525,
      sp1pef,
    ]);

    this.sp2 = new Spirometry([
      sp2Fev1,
      sp2Fvc,
      sp2Vc,
      new ComputedParameterValue(
        () => (sp2Fvc.result && sp2Fev1.result ? (sp2Fev1.result / sp2Fvc.result) * 100 : undefined),
        fev1_fvc,
        new ParameterContext(67.1, undefined, 4.32, 0.4),
        RefType.LLN,
        this
      ),
      new ComputedParameterValue(
        () => (sp2Vc.result && sp2Fev1.result ? (sp2Fev1.result / sp2Vc.result) * 100 : undefined),
        fev1_vc,
        new ParameterContext(),
        RefType.NO_REF,
        this,
      ),
      sp2fef2527,
      sp2pef,
    ]);
  }

  getSp1Value(paramName: string) {
    return this.sp1.values.find((v) => v.parameter?.description === paramName);
  }

  getSp2Value(paramName: string) {
    return this.sp2.values.find((v) => v.parameter?.description === paramName);
  }

  get completedTestName() {
    const completed = [];

    if (!!this.getSp1Value('FEV1')?.result && !!this.getSp1Value('FVC')?.result && !!this.getSp1Value('VC')?.result) {
      completed.push('Sp1');
    }

    if (!!this.getSp2Value('FEV1')?.result && !!this.getSp2Value('FVC')?.result && !!this.getSp2Value('VC')?.result) {
      completed.push('Sp2');
    }

    return completed.join(' ');
  }

  setProperty<K extends keyof SpirometryStore>(key: K, value: SpirometryStore[K]) {
    (this[key] as SpirometryStore[K]) = value;
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash = false) {
    if (data.r_pre_condition) {
      this.setProperty('sp1Condition', data.r_pre_condition);
    }
    if (data.r_post_condition) {
      this.setProperty('sp2Condition', data.r_post_condition);
    }

    // SP1 (Baseline measurements)
    if (safeParseFloat(data.r_bl_fev1)) {
      this.sp1.values[0].setResult(safeParseFloat(data.r_bl_fev1), flash);
    }

    if (safeParseFloat(data.r_bl_fvc)) {
      this.sp1.values[1].setResult(safeParseFloat(data.r_bl_fvc), flash);
    }

    if (safeParseFloat(data.r_bl_vc)) {
      this.sp1.values[2].setResult(safeParseFloat(data.r_bl_vc), flash);
    }

    if (safeParseFloat(data.r_bl_fef2575)) {
      this.sp1.values[5].setResult(safeParseFloat(data.r_bl_fef2575), flash);
    }

    if (safeParseFloat(data.r_bl_pef)) {
      this.sp1.values[6].setResult(safeParseFloat(data.r_bl_pef), flash);
    }

    // SP2 (Post measurements)
    if (safeParseFloat(data.r_post_fev1)) {
      this.sp2.values[0].setResult(safeParseFloat(data.r_post_fev1), flash);
    }

    if (safeParseFloat(data.r_post_fvc)) {
      this.sp2.values[1].setResult(safeParseFloat(data.r_post_fvc), flash);
    }

    if (safeParseFloat(data.r_post_vc)) {
      this.sp2.values[2].setResult(safeParseFloat(data.r_post_vc), flash);
    }

    if (safeParseFloat(data.r_post_fef2575)) {
      this.sp2.values[5].setResult(safeParseFloat(data.r_post_fef2575), flash);
    }

    if (safeParseFloat(data.r_post_pef)) {
      this.sp2.values[6].setResult(safeParseFloat(data.r_post_pef), flash);
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_pre_condition?: string | null;
      r_post_condition?: string | null;
      r_bl_fev1?: string | null;
      r_bl_fvc?: string | null;
      r_bl_vc?: string | null;
      r_bl_fef2575?: string | null;
      r_bl_pef?: string | null;
      r_post_fev1?: string | null;
      r_post_fvc?: string | null;
      r_post_vc?: string | null;
      r_post_fef2575?: string | null;
      r_post_pef?: string | null;
    }

    const updateData: UpdateInput = {};

    if (!field || field === 'r_pre_condition') {
      updateData.r_pre_condition = this.sp1Condition;
    }
    if (!field || field === 'r_post_condition') {
      updateData.r_post_condition = this.sp2Condition;
    }

    // SP1 (Baseline measurements)
    if (!field || field === 'r_bl_fev1') {
      updateData.r_bl_fev1 = this.sp1.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_fvc') {
      updateData.r_bl_fvc = this.sp1.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_vc') {
      updateData.r_bl_vc = this.sp1.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_fef2575') {
      updateData.r_bl_fef2575 = this.sp1.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_pef') {
      updateData.r_bl_pef = this.sp1.values[6].result?.toString() ?? null;
    }

    // SP2 (Post measurements)
    if (!field || field === 'r_post_fev1') {
      updateData.r_post_fev1 = this.sp2.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_post_fvc') {
      updateData.r_post_fvc = this.sp2.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_post_vc') {
      updateData.r_post_vc = this.sp2.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_post_fef2575') {
      updateData.r_post_fef2575 = this.sp2.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_post_pef') {
      updateData.r_post_pef = this.sp2.values[6].result?.toString() ?? null;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}

export interface SpirometryStore extends DisposableStoreMixin {}

applyMixins(SpirometryStore, [DisposableStoreMixin]);
