import clsx from 'clsx';
import {Suspense, useEffect, useState} from 'react';
import {Switch} from 'react-aria-components';
import {useLocation, useNavigate, useParams, useSearchParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {Redo2, Undo2} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import PaperIcon from '@/assets/iconly/Paper.svg?react';
import SearchChatIcon from '@/assets/iconly/SearchChat.svg?react';
import TrendDownGraphIcon from '@/assets/iconly/TrendDownGraph.svg?react';
import {Button} from '@/components/ui/button.tsx';
import {useSidebar} from '@/components/ui/sidebar.tsx';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs.tsx';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {useRftReport} from '@/features/studies-rft/report';
import {ReportSidebar} from '@/features/studies-rft/sections/report-sidebar.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {ReportPatientInfo} from '@/features/studies/ReportPatientInfo.tsx';
import {undoStack} from '@/features/undo-stack';
import {getPatientsDetailData} from '@/graphql/patients.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';
import PatientTrendTable from '@/views/patients/patient-detail/patient-trend-view/components/patient-trend-table.tsx';

import {BloodGasesSection} from './sections/blood-gases.tsx';
import {COTransferSection} from './sections/co-transfer.tsx';
import {ExhaledNitricOxideSection} from './sections/exhaled-nitric-oxide.tsx';
import {FlowVolume} from './sections/flow-volume.tsx';
import {LungVolumesSection} from './sections/lung-volumes.tsx';
import {MRPsSection} from './sections/mrps.tsx';
import {SpirometryFormSection} from './sections/spirometry.tsx';
import './styles.css';
import {Image} from "@react-pdf/renderer";
import {toPng} from "html-to-image";
import {tw} from "@/lib/react-pdf-tailwind.ts";

function TestSections({rftStore}: {rftStore: RftStore}) {
  useGlobalStoreLoader();
  return (
    <div className="relative space-y-3 rounded border border-neutral-200 bg-white p-4">
      <SpirometryFormSection rftStore={rftStore} />
      <COTransferSection rftStore={rftStore} />
      <LungVolumesSection rftStore={rftStore} />
      <ExhaledNitricOxideSection rftStore={rftStore} />
      <MRPsSection rftStore={rftStore} />
      <BloodGasesSection rftStore={rftStore} />
      <FlowVolume rftStore={rftStore} />
    </div>
  );
}

const RFTStudyDetail = observer(() => {
  const {id, patientId} = useParams<{id: string; patientId: string}>();
  const {toggleSidebar, state} = useSidebar();
  const {createReport, isLoading} = useRftReport();
  const [searchParams] = useSearchParams();
  const editParam = searchParams.get('edit');
  const [isTrendViewActive, setIsTrendViewActive] = useState(false);

  const isEditing = editParam === 'true';

  useQuery(getPatientsDetailData, {variables: {patientId: +patientId!}});

  const [rftStore, setRftStore] = useState(() => {
    return new RftStore({rftid: +id!, isEditing});
  });

  useEffect(() => {
    undoStack.reset();

    setRftStore(new RftStore({rftid: +id!, isEditing}));
    return () => {
      rftStore.dispose();
      undoStack.reset();
    };
  }, []);

  useEffect(() => {
    if (state === 'expanded') {
      toggleSidebar();
    }
  }, []);

  return (
    <div>
      <ReportPatientInfo testSession={rftStore.testSession} />
      <Tabs
        defaultValue="results"
        onValueChange={(value) => setIsTrendViewActive(value === 'trend-view')}
      >
        <div className="mb-4 flex items-center gap-x-5">
          <RftStudyToolbar rftStore={rftStore} />

          <div className="flex-1" />
          <Button
            variant="outlined"
            size="small"
            isDisabled={isLoading}
            isPending={isLoading}
            onPress={async () => {

              const spZscoreEl = document.getElementById('sp-zscore-plot');
              const coZscoreEl = document.getElementById('co-zscore-plot');
              const lvZscoreEl = document.getElementById('lv-zscore-plot');

              const spZscoreImage = spZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(spZscoreEl)} /> : null;
              const coZscoreImage = coZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(coZscoreEl)} /> : null;
              const lvZscoreImage = lvZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(lvZscoreEl)} /> : null;

              createReport({rftStore, spZscoreImage, coZscoreImage, lvZscoreImage}).then((url) => {
                window.open(url);
              });
            }}
          >
            <PaperIcon />
            {isLoading ? 'Compiling Report...' : 'View Report'}
          </Button>
        </div>

        <div className="flex items-start gap-x-3">
          <div
            className={clsx(
              'study-results w-278 shrink-0',
              isTrendViewActive ? 'sticky top-13.5 isolate z-10' : ''
            )}
          >
            <Suspense fallback={<div>Loading...</div>}>
              <TabsContent
                className="mt-0"
                value="results"
              >
                <TestSections rftStore={rftStore} />
              </TabsContent>
              <TabsContent
                className="mt-0"
                value="trend-view"
              >
                <PatientTrendTable />
              </TabsContent>
            </Suspense>
          </div>
          <ReportSidebar rftStore={rftStore} />
        </div>
      </Tabs>
    </div>
  );
});

const RftStudyToolbar = observer(({rftStore}: {rftStore: RftStore}) => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (rftStore.isEditing) {
      searchParams.set('edit', 'true');
    } else {
      searchParams.delete('edit');
    }
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.toString(),
      },
      {replace: true}
    );
  }, [rftStore.isEditing]);

  return (
    <>
      <TabsList className="flex w-fit justify-start">
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="results"
        >
          <SearchChatIcon className="size-4.5" />
          Results
        </TabsTrigger>
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="trend-view"
        >
          <TrendDownGraphIcon className="size-4.5" />
          Trend View
        </TabsTrigger>
      </TabsList>

      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <Switch
              isSelected={rftStore.isEditing}
              onChange={(isSelected) => rftStore.setProperty('isEditing', isSelected)}
              isDisabled={!rftStore.isReportEditable}
              isReadOnly={!rftStore.isReportEditable}
            >
              <div>Edit Mode</div>
              <div className="flex h-7.5 items-center gap-x-2 rounded border border-neutral-300 px-2">
                <div className="w-5 text-center text-[10px] text-neutral-600">
                  <div className="block [[data-selected]_&]:hidden">OFF</div>
                  <div className="hidden [[data-selected]_&]:block">ON</div>
                </div>
                <div className="indicator" />
              </div>
            </Switch>
          </div>
        </TooltipTrigger>
        {!rftStore.isReportEditable && (
          <TooltipContent>You cannot edit a completed report. Try amending instead.</TooltipContent>
        )}
      </Tooltip>

      {rftStore.isEditing && (
        <div className="flex items-center gap-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canUndo}
                onClick={() => undoStack.undo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canUndo && 'cursor-default'
                )}
              >
                <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canRedo}
                onClick={() => undoStack.redo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canRedo && 'cursor-default'
                )}
              >
                <Redo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Redo</TooltipContent>
          </Tooltip>
        </div>
      )}
    </>
  );
});

export default RFTStudyDetail;
