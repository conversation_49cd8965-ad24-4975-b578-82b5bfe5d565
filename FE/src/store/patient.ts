import {differenceInCalendarMonths, isFuture} from 'date-fns';
import {makeAutoObservable} from 'mobx';

import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client.ts';
import {getPatientsDetailData} from '@/graphql/patients.ts';
import {globalStore} from '@/store/global.store.ts';
import {assignDefinedProps, fetchAndObserveQuery} from '@/store/utils.ts';

export class PatientName {
  patientid!: number;
  name_type?: 'legal' | (string & {});
  title?: string;
  firstname?: string;
  middlename?: string;
  surname?: string;

  constructor(props: Partial<PropertiesOnly<PatientName>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);
  }
}

export class PatientURN {
  ur_id!: number;
  patientid!: number;
  ur?: string;
  ur_hsid?: string;
  ur_status?: 'primary' | 'other_healthservice' | 'secondary' | (string & {});
  created_inreslab_xx?: boolean;
  create_date?: string;
  create_by?: string;

  constructor(props: Partial<PropertiesOnly<PatientName>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);
  }
}

export class PatientAddress {
  addressid!: number;
  patientid!: number;
  address_type_code?: string;
  address_1?: string;
  address_2?: string;
  suburb?: string;
  statename?: string;
  postcode?: string;

  get addressType() {
    return globalStore.addressTypes.find((a) => a.code.toString() === this.address_type_code);
  }

  constructor(props: Partial<PropertiesOnly<PatientAddress>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);
  }
}

export class Patient {
  patientid!: number;
  ur?: string;
  ur_id?: number;
  ur_hsid?: string;
  ur_mergeto?: string;
  ur_mergeto_urid?: number;
  ur_mergeto_hsid?: string;
  dob?: string;
  gender_code?: string;
  gender_forrfts_code?: string;
  preferredlanguage_code?: string;
  race_forrfts_code?: string;
  aboriginalstatus_code?: string;
  countryofbirth_code?: string;
  phone_home?: string;
  phone_work?: string;
  phone_mobile?: string;
  email?: string;
  medicare_no?: string;
  medicare_expirydate?: string;
  gpid_fromreftbl?: string;
  death_date?: string;
  death_indicator?: string;
  research_discussed_date?: string;
  research_discussed_by?: string;
  research_discussed_outcome?: string;
  research_notes?: string;
  research_tags?: string;
  lastupdated_date?: string;
  lastupdated_by?: string;
  firstname?: string;
  surname?: string;
  title?: string;

  names: PatientName[] = [];
  urns: PatientURN[] = [];
  addresses: PatientAddress[] = [];

  private subscription?: ReturnType<ReturnType<typeof apolloClient.watchQuery>['subscribe']>;

  constructor(props: Partial<PropertiesOnly<Patient>>, init = true) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);

    if (init) {
      this.init();
    }
  }

  init() {
    this.subscription = fetchAndObserveQuery(
      {
        query: getPatientsDetailData,
        variables: {patientId: this.patientid},
      },
      (data) => {
        const patient = data.pas_pt?.[0];
        if (patient) {
          assignDefinedProps(this, patient as any, this.setProperty.bind(this) as any);

          const names = patient.pas_pt_names.map((name) => new PatientName(name as any));
          const urns = patient.pas_pt_ur_numbers.map((ur) => new PatientURN(ur as any));
          const addresses = patient.pas_pt_addresses.map((address) => new PatientAddress(address as any));

          this.setProperty('names', names);
          this.setProperty('urns', urns);
          this.setProperty('addresses', addresses);
        }
      }
    );
  }

  destroy() {
    this.subscription?.unsubscribe();
  }

  setProperty<K extends keyof Patient>(key: K, value: Patient[K]) {
    this[key] = value as any;
  }

  get isDeceased() {
    if (!this.death_date) return false;
    if (isFuture(new Date(this.death_date))) return false;
    return true;
  }

  get fullName() {
    let internalName = [this.surname, this.firstname].filter(Boolean).join(', ')
    if (internalName) {
      return this.title ? `${this.title}. ${internalName}` : internalName;
    }

    let fullName = '';
    if (this.names[0]?.title) {
      fullName += this.names[0].title + '. ';
    }

    if (this.names[0]?.surname) {
      fullName += this.names[0].surname + ', ';
    }

    if (this.names[0]?.firstname) {
      fullName += this.names[0].firstname;
    }

    return fullName;
  }

  get MRNResolved () {
    const hospitalFacility = globalStore.healthServices.find((hs) => hs?.code?.toString() === this.ur_hsid)?.description;
    if (!hospitalFacility) return this.ur;
    return `${this.ur} - ${hospitalFacility}`;
  }

  get genderForRftResolved() {
    const genderCode = this.gender_forrfts_code || this.gender_code;
    return globalStore.genders.find((g) => g.genderid?.toString() == genderCode);
  }

  get genderResolved() {
    return globalStore.genders.find((g) => g.gender_code?.toString() == this.gender_code);
  }

  get ethnicityForRftResolved() {
    return globalStore.ethnicities.find((g) => g.ethnicityid?.toString() == this.race_forrfts_code);
  }

  get ageToday() {
    if (!this.dob) return undefined;
    const dob = new Date(this.dob);
    const now = new Date();
    const months = differenceInCalendarMonths(now, dob);
    return months / 12;
  }

  get countryOfBirthResolved() {
    return globalStore.nationalities.find((n) => n.code === this.countryofbirth_code);
  }

  get preferredLanguageResolved() {
    return globalStore.languages.find((l) => l.code === this.preferredlanguage_code);
  }

  get aboriginalStatusResolved() {
    return globalStore.aboriginalStatuses.find((l) => l.code === this.aboriginalstatus_code);
  }

  static validateMedicareNumber(medicareNum: string | number) {
    // Remove any hyphens from the Medicare number
    const s = medicareNum?.toString()?.replace(/-/g, '');
    if (s.length !== 11) {
      return "Incorrect length (must be 11 numeric digits)";
    }

    if (!"23456".includes(s.charAt(0))) {
      return "First digit must be in the range 2-6";
    }

    const weights = [1, 3, 7, 9, 1, 3, 7, 9];
    let sum = 0;
    for (let i = 0; i < 8; i++) {
      sum += parseInt(s.charAt(i), 10) * weights[i];
    }
    const calculatedChecksum = sum % 10;
    const providedChecksum = parseInt(s.charAt(8), 10);

    if (calculatedChecksum !== providedChecksum) {
      return "Invalid checksum digit";
    }
  }
}
