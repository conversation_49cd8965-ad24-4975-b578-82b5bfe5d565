import {jwtDecode, type JwtPayload} from 'jwt-decode';
import {autorun, makeAutoObservable, runInAction, untracked} from 'mobx';

import {Paths} from '@/api-types/routePaths.ts';
import {ApiResponse} from '@/api-types/utils';
import {axiosInstance, setHeaders} from '@/axios.ts';
import {SiteStore} from '@/store/site.store.ts';

const ACCESS_TOKEN_KEY = 'rezibase:access_token';
const REFRESH_TOKEN_KEY = 'rezibase:refresh_token';

interface LoginMessage {
  type: 'logout' | 'login';
  accessToken: string;
  refreshToken: string;
}

export enum AuthStatus {
  LOGGED_OUT = 'logged_out',
  VALIDATING = 'validating',
  LOGGED_IN = 'logged_in',
}

export interface JWTPayload extends JwtPayload {
  mfaRequired?: boolean;
  'https://hasura.io/jwt/claims': {
    'X-Hasura-Default-Role': string;
    'X-Hasura-Allowed-Roles': string[];
    'X-Hasura-Allowed-Sites': string;
    'X-Hasura-User-Id': string;
    'X-Hasura-Site-Id': string;
  };
}

class AuthStore {
  accessToken = localStorage.getItem(ACCESS_TOKEN_KEY) || null;
  refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY) || null;
  channel = new BroadcastChannel('auth-channel');
  status: AuthStatus = localStorage.getItem(ACCESS_TOKEN_KEY) ? AuthStatus.VALIDATING : AuthStatus.LOGGED_OUT;
  site?: SiteStore;
  user?: ApiResponse<typeof Paths.PROFILE>;

  constructor() {
    makeAutoObservable(this);
    this.setupChannelListener();
    this.validateTokenOnMount();

    autorun(() => {
      untracked(() => this.site)?.dispose();

      if (!this.tokenSiteId) {
        this.site = undefined;
      } else {
        this.site = new SiteStore({id: this.tokenSiteId});
      }
    });
  }

  get tokenPayload() {
    if (!this.accessToken) return undefined;
    try {
      return jwtDecode<JWTPayload>(this.accessToken);
    } catch (e) {
      return undefined;
    }
  }

  get tokenSiteId() {
    const siteId = this.tokenPayload?.['https://hasura.io/jwt/claims']?.['X-Hasura-Site-Id'];
    return siteId ? +siteId : undefined;
  }

  get requireMfa() {
    return this.tokenPayload?.mfaRequired;
  }

  async login(accessToken: string, refreshToken: string) {
    try {
      runInAction(() => {
        this.status = AuthStatus.VALIDATING;
      });
      // Validate token before setting state
      const response = await axiosInstance.get(Paths.PROFILE, {
        headers: {Authorization: `Bearer ${accessToken}`},
      });
      setHeaders(accessToken);
      runInAction(() => {
        this.user = response.data;
        this.status = AuthStatus.LOGGED_IN;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
        localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
      });
      // Broadcast login to other tabs
      this.channel.postMessage({type: 'login', accessToken, refreshToken});
    } catch (error) {
      console.error('Login token validation failed', error);
      this.logout();
    }
  }

  logout() {
    setHeaders();
    runInAction(() => {
      this.status = AuthStatus.LOGGED_OUT;
      this.accessToken = null;
      this.refreshToken = null;
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
    });
    this.channel.postMessage({type: 'logout'});
  }

  async setupChannelListener() {
    this.channel.onmessage = async (msg: MessageEvent<LoginMessage>) => {
      if (msg.data.type === 'login') {
        try {
          runInAction(() => {
            this.status = AuthStatus.VALIDATING;
          });
          // Validate token in other tabs
          const response = await axiosInstance.get(Paths.PROFILE, {
            headers: {Authorization: `Bearer ${msg.data.accessToken}`},
          });
          setHeaders(msg.data.accessToken);
          runInAction(() => {
            this.user = response.data;
            this.status = AuthStatus.LOGGED_IN;
            this.accessToken = msg.data.accessToken;
            this.refreshToken = msg.data.refreshToken;
            localStorage.setItem('accessToken', msg.data.accessToken);
            localStorage.setItem('refreshToken', msg.data.refreshToken);
          });
        } catch (error) {
          console.error('Token validation failed in other tab', error);
          this.logout();
        }
      } else if (msg.type === 'logout') {
        runInAction(() => {
          this.status = AuthStatus.LOGGED_OUT;
          this.accessToken = null;
          this.refreshToken = null;
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
        });
      }
    };
  }

  async validateTokenOnMount() {
    try {
      if (this.accessToken) {
        runInAction(() => {
          this.status = AuthStatus.VALIDATING;
        });
        const response = await axiosInstance.get(Paths.PROFILE, {
          headers: {Authorization: `Bearer ${this.accessToken}`},
        });
        runInAction(() => {
          this.user = response.data;
          this.status = AuthStatus.LOGGED_IN;
        });
      }
    } catch (error) {
      console.error('Initial token validation failed', error);
      this.logout();
    }
  }

  async switchSite(siteId: number) {
    try {
      runInAction(() => {
        this.status = AuthStatus.VALIDATING;
      });
      const response = await axiosInstance.post(Paths.SWITCH_SITE, {site_id: siteId});
      const {access_token: accessToken} = response.data;
      if (!accessToken) throw new Error('No access token returned');
      setHeaders(accessToken);
      runInAction(() => {
        this.status = AuthStatus.LOGGED_IN;
        this.accessToken = accessToken;
        localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
      });
      // Broadcast new token to other tabs
      this.channel.postMessage({type: 'login', accessToken, refreshToken: this.refreshToken});
    } catch (error) {
      console.error('Site switch failed', error);
      this.logout();
    }
  }

  setAccessToken(accessToken: string) {
    setHeaders(accessToken);
    runInAction(() => {
      this.status = AuthStatus.LOGGED_IN;
      this.accessToken = accessToken;
      localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
    });
    this.channel.postMessage({type: 'login', accessToken, refreshToken: this.refreshToken});
  }

  dispose() {
    this.channel.close();
  }
}

const authStore = new AuthStore();

export default authStore;
