import {use} from 'react';

import {makeAutoObservable} from 'mobx';

import {AllParameters} from '@/graphql/common.ts';
import {assignDefinedProps, fetchAndObserveQuery} from '@/store/utils.ts';

import {
  AboriginalStatus,
  AddressType,
  Ethnicity,
  Gender,
  HealthService,
  Language,
  Nationality,
  ReportStatus,
} from './lists.store';
import {Parameter} from './parameter';

class GlobalStore {
  parameters: Parameter[] = [];
  loaded = false;
  genders: Gender[] = [];
  ethnicities: Ethnicity[] = [];
  addressTypes: AddressType[] = [];
  reportStatuses: ReportStatus[] = [];
  nationalities: Nationality[] = [];
  languages: Language[] = [];
  aboriginalStatuses: AboriginalStatus[] = [];
  healthServices: HealthService[] = [];

  private _initPromise: Promise<any> | null = null;

  private subscriptions = new Set<Awaited<ReturnType<typeof fetchAndObserveQuery>>>();

  constructor() {
    makeAutoObservable(this);
  }

  async init() {
    if (this.loaded) return;

    if (!this._initPromise) {
      this._initPromise = Promise.allSettled([
        this.loadParameters(),
        Gender.loadAll().then((l) => this.setProperty('genders', l)),
        AddressType.loadAll().then((l) => this.setProperty('addressTypes', l)),
        ReportStatus.loadAll().then((l) => this.setProperty('reportStatuses', l)),
        Ethnicity.loadAll().then((l) => this.setProperty('ethnicities', l)),
        Nationality.loadAll().then((l) => this.setProperty('nationalities', l)),
        Language.loadAll().then((l) => this.setProperty('languages', l)),
        AboriginalStatus.loadAll().then((l) => this.setProperty('aboriginalStatuses', l)),
        HealthService.loadAll().then((l) => this.setProperty('healthServices', l)),
      ]);
    }

    await this._initPromise.finally(() => {
      this._initPromise = null;
    });

    this.setProperty('loaded', true);
    this.loaded = true;
  }

  getParameter(name: string) {
    return this.parameters.find((p) => p.description === name);
  }

  async loadParameters() {
    const subscription = fetchAndObserveQuery({query: AllParameters}, (data) => {
      for (const parameter of data.pred_ref_parameters) {
        let param = this.getParameter(parameter.description!);
        if (param) {
          assignDefinedProps(param, parameter as any, param.setProperty.bind(param) as any);
        } else {
          this.parameters.push(new Parameter(parameter as any));
        }
      }
    });

    this.subscriptions.add(subscription);
  }

  clear() {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscription);
    }

    for (const parameter of this.parameters) {
      parameter.destroy();
    }

    this.parameters = [];
  }

  setProperty<K extends keyof GlobalStore>(key: K, value: GlobalStore[K]) {
    this[key] = value as any;
  }
}

export const globalStore = new GlobalStore();

let _promise: Promise<any> | null = null;

export function useGlobalStoreLoader(suspend: boolean = true) {
  if (globalStore.loaded) return true;

  if (!_promise) {
    _promise = globalStore.init().finally(() => {
      globalStore.setProperty('loaded', true);
      _promise = null;
    });
  }
  if (suspend) {
    use(_promise);
  }
}
