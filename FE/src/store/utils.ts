import type {OperationVariables} from '@apollo/client/core/types';
import type {QueryOptions, WatchQueryOptions} from '@apollo/client/core/watchQueryOptions';

import {PropertiesOnly} from '@/@types/utils';
import {apolloClient} from '@/apollo-client.ts';

export function fetchAndObserveQuery<
  T = any,
  TVariables extends OperationVariables = OperationVariables,
>(options: WatchQueryOptions<TVariables, T> & QueryOptions<TVariables, T>, cb: (data: T) => void) {
  const watchOptions = {...options} as WatchQueryOptions<TVariables, T>;
  if (!watchOptions.fetchPolicy) {
    watchOptions.fetchPolicy = 'cache-and-network';
  }

  const observable = apolloClient.watchQuery(options);
  const subscription = observable.subscribe({
    next: ({data}) => cb(data),
  });

  if (!options.fetchPolicy || options.fetchPolicy === 'cache-only') {
    return subscription;
  }

  apolloClient.query(options).then((res) => cb(res.data));
  return subscription;
}

export function assignDefinedProps<T extends object, P extends PropertiesOnly<T>>(
  object: T,
  props: Partial<P>,
  setterFn?: (key: keyof T, value: any) => void
) {
  const definedKeys = Object.keys(object) as (keyof T)[];
  for (const key of definedKeys) {
    if (props && Object.hasOwn(props, key)) {
      if (setterFn) {
        setterFn(key, (props as any)[key]);
      } else {
        (object[key] as any) = (props as any)[key];
      }
    }
  }
}
