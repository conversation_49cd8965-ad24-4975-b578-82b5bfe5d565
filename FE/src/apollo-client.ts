import {ApolloClient, InMemoryCache, createHttpLink, from, split} from '@apollo/client';
import {setContext} from '@apollo/client/link/context';
import {FetchResult} from '@apollo/client/link/core';
import {onError} from '@apollo/client/link/error';
import {GraphQLWsLink} from '@apollo/client/link/subscriptions';
import {Observable, getMainDefinition} from '@apollo/client/utilities';
import {PersistentStorage, persistCache} from 'apollo3-cache-persist';
import {createClient} from 'graphql-ws';
import {del, get, set} from 'idb-keyval';

import {Paths} from '@/api-types/routePaths.ts';

import {axiosInstance} from './axios';

class IdbStorageWrapper<T> implements PersistentStorage<T> {
  getItem(key: string) {
    return get(key);
  }

  removeItem(key: string) {
    return del(key);
  }

  setItem(key: string, value: T) {
    if (value !== null) {
      return set(key, value);
    } else {
      return del(key);
    }
  }
}

const httpLink = createHttpLink({
  uri: window.globalEnv.VITE_APP_HASURA_URL as string,
});

const wsLink = new GraphQLWsLink(
  createClient({
    url: (window.globalEnv.VITE_APP_HASURA_URL as string)?.replace('http', 'ws') as string,
    connectionParams: async () => {
      const accessToken = localStorage.getItem('rezibase:access_token') // obtain the token from auth
      return {
        headers: {
          authorization: `Bearer ${accessToken}`,
        }
      }
    }
  })
);

const splitLink = split(
  ({query}) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
  },
  wsLink,
  httpLink
);

const cache = new InMemoryCache();

await persistCache({
  cache,
  storage: new IdbStorageWrapper(),
  serialize: false as any,
});

let validationRequest: Promise<any> | undefined;

const errorLink = onError(({graphQLErrors, operation, forward}) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      if (err.extensions?.code === 'invalid-jwt') {
        // Token is expired, let axios interceptor handle the refresh
        return new Observable((observer) => {
          if (!validationRequest) {
            validationRequest = axiosInstance.get(Paths.PROFILE).finally(() => {
              validationRequest = undefined;
            });
          }

          // Make a dummy request to trigger axios interceptor
          validationRequest
            .then(() => {
              // After token refresh, retry the failed operation
              const subscriber = {
                next: (result: FetchResult) => observer.next(result),
                error: (error: Error) => observer.error(error),
                complete: () => observer.complete(),
              };
              forward(operation).subscribe(subscriber);
            })
            .catch((error: Error) => {
              observer.error(error);
            });
        });
      }
    }
  }
});

const authLink = setContext((_, context) => {
  const token = localStorage.getItem('rezibase:access_token');
  const selectedSiteId = localStorage.getItem('rezibase:site_id');
  return {
    ...context,
    headers: {
      ...context.headers,
      authorization: token ? `Bearer ${token}` : '',
      'X-Hasura-Site-Id': selectedSiteId || '',
    },
  };
});

export const apolloClient = new ApolloClient({
  link: from([errorLink, authLink, splitLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
    },
  },
});
