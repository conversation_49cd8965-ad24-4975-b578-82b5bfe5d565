export const Paths = {
  LOGIN: '/api/login',
  UPLOAD_PDF: '/api/pdf_import',
  REGISTER: '/api/register',
  REFRESH_TOKEN: '/api/refresh',
  PROFILE: '/api/users/current',
  FAVOURITE: '/api/qc/update_favourite/',
  GET_FAVOURITE: '/api/get_favourites/{siteID}',
  CHANGE_PASS: '/api/change_password',

  SITES_LIST: `/api/qc/site/list`,
  SET_SITE: `/api/qc/site/set_site`,
  SWITCH_SITE: '/api/users/site/switch',

  QC_LABS_LIST: `/api/qc/labs`,
  QC_EQUIPMENT_LIST: `/api/qc/equipments`,
  QC_CONTROL_METHODS_LIST: `/api/qc/control-methods`,
  QC_PARAMETERS: `/api/qc/parameters`, // control_methods_equipments_id=6
  QC_PARAMETERS_FILTER: `/api/qc/parameter/filter`, // lab_id=6
  QC_CONTROL_METHOD_FILTER: `/api/qc/control-method/filter`, // lab_id=6
  QC_EQUIPMENT_FILTER: `/api/qc/equipments/filter`, // lab_id=6
  QC_CONTROL_RULES: `/api/qc/control-rules`, // control_methods_equipments_id=6
  QC_CONTROL_RULE_INTERVALS: `/api/qc/control-rule-intervals`, // control_methods_equipments_id=6

  QC_SESSIONS: `/api/qc/sessions`, // control_methods_equipments_id=6
  QC_SESSIONS_DATA: `/api/qc/session_datas`, // control_methods_equipments_id=6
  QC_SESSIONS_DETAIL: `/api/qc/sessions/{sessionId}`, // control_methods_equipments_id=6
  QC_SESSIONS_STATS: `/api/qc/session-statistics`,

  QC_ALARMS: `/api/qc/alarms`, // control_methods_equipments_id=6, site_id=1
  QC_UPDATE_SESSION: `/api/qc/sessions`, // /id: 514 i.e /api/qc/sessions/514 (for example), PATCH, payload = { control_methods_equipments_parameter_id: 57, session_id: 514, value: "1001" }
  QC_UPDATE_SESSION_ALARM: `/api/qc/sessions/{sessionDataId}/alarm`, // /api/qc/sessions/754/alarm
  QC_UPDATE_NOTE: `/api/qc/sessions/{sessionId}/notes`, // /api/qc/sessions/946/notes

  QC_SESSION_ALARM_COUNTS: '/api/qc/parameter-session-alarm-counts', //equipment_id=1,start_date = 10-10-1999, end_date = 10-10-1999
  QC_USERS: `/api/users/`,
} as const;
