export interface CurrentUser {
  active: boolean;
  email: string;
  id: number;
  name: string;
  roles: string[];
  sites: string[];
  password_reset_required: boolean;
  totp_enabled: boolean;
}

export interface Site {
  text: string;
  value: number;
}

export interface Lab {
  id: number;
  name: string;
  site: string;
  site__ref_id: number;
}

export interface Equipment {
  active: boolean;
  equipment_distributor_id: number | null;
  equipment_manufacturer_id: number | null;
  id: number;
  inactivity_date: string | null; // ISO date string
  inactivity_reason: string;
  lab_id: number;
  model: string;
  purchase_date: string | null; // ISO date string
  serial_no: string | null;
}

export interface ControlMethod {
  control_method_type: string;
  control_methods_equipments_id: number;
  date_of_birth: string | null;
  equipment_id: number | null;
  first_name: string | null;
  gender: string | null;
  id: number;
  name: string;
  sur_name: string;
}

export interface ControlMethodEquipment {
  control_method_id: number;
  equipment_id: number;
  id: number;
}

export interface Alert {
  closed_by_id: number | null;
  created: string; // ISO 8601 formatted date-time string
  id: number;
  notes: string | null;
  open: boolean;
  relevant_control_rule_ids: number[];
  relevant_control_rules: {description: string; name: string}[];
  session_data_id: number;
}

export interface Parameter {
  abbreviation: string;
  active: boolean;
  control_methods_equipments_parameter_id: number;
  decimal_places: number;
  id: number;
  inactivity_date: string | null; // ISO 8601 formatted date-time or null
  inactivity_reason: string | null;
  long_name: string;
  unit: string;
}

export interface SessionData {
  closed_by: string | null;
  closed_by_id: number | null;
  control_method_equipment_parameter_id: number;
  created: string; // ISO date string
  id: number;
  notes: string;
  open: boolean;
  parameter_value: number;
  session_id: number;
  is_in_control: boolean;
  alarms: Alert[];
  sr_value: number | null;
}

export interface SessionParameterData {
  idx: number;
  closed_by: string | null;
  closed_by_id: number | null;
  control_method_equipment_parameter_id: number;
  created: Date;
  equipment_id: number;
  id: number;
  notes: string | null;
  open: boolean | null;
  parameter_value: number | undefined;
  session_id: number;
  session_time: string;
}

export interface ControlRule {
  control_method_equipment_parameters: number[];
  description: string;
  id: number;
  name: string;
}

export interface ControlRuleInterval {
  control_method_equipment_parameter_id: number;
  id: number;
  created: string;
}

export interface Session {
  control_method_equipment_id: number;
  id: number;
  performed_by: string;
  performed_by_id: number;
  session_datas: SessionData[];
  session_time: string; // ISO date string
}

export interface User {
  active: boolean;
  email: string;
  id: number;
  name: string;
  roles: string[];
  sites: string[];
}

export interface UserFavourites {
  device: {
    is_favourite: boolean;
    item_id: string;
    site_id: number;
    type: string;
  } | null;
  parameter: {
    is_favourite: boolean;
    item_id: string;
    site_id: number;
    type: string;
  } | null;
}

export interface SessionDataCount {
  alarm_count: number;
  control_method_equipment_parameter_id: number;
  session_count: number;
  session_month: string;
}

export interface SessionStats {
  mean: number;
  standard_deviation: number;
}

export type PatientData = {
  patientid: string;
  dob: string | null;
  email: string | null;
  gender_code: string | null;
  lastupdated_by: string | null;
  lastupdated_date: string | null;
  medicare_no: string | null;
  phone_home: string | null;
  pas_pt_names: {
    firstname: string | null;
    middlename: string | null;
    name_type: string | null;
    surname: string | null;
    title: string | null;
  }[];
};
