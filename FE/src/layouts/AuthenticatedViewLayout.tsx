import {useEffect} from 'react';
import {useNavigate, Navigate} from 'react-router';

import {useGlobalStoreLoader} from "@/store/global.store.ts";
import {observer} from "mobx-react-lite";
import authStore, {AuthStatus} from "@/store/auth.store.ts";


const AuthenticatedViewLayout = observer(({children}: {children: any}) => {
  const navigate = useNavigate();

  useGlobalStoreLoader(false);

  useEffect(() => {
    if (authStore.status === AuthStatus.LOGGED_OUT) {
      navigate('/auth/login');
    }
  }, [authStore.status]);

  if (authStore.requireMfa) {
    return <Navigate to="/auth/mfa" />
  }

  if (authStore.user?.password_reset_required === true) {
    return <Navigate to="/auth/reset-pass" />
  }

  if (authStore.user?.totp_enabled === false) {
    return <Navigate to="/auth/mfa/setup" />
  }

  if (authStore.status === AuthStatus.VALIDATING || !authStore.user) {
    // todo: show proper loading state
    return <div />;
  }

  return children;
});
export default AuthenticatedViewLayout
